{"screenSpecs": {"path": "/posts/generate", "description": "A screen with a text input field for the AI prompt and a button to initiate content generation, displaying the generated draft upon completion", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-created post, including its content, metadata, and relationship to the user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who created this post."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was published or created."}, "content": {"type": "String", "required": false, "isUser": false, "description": "The main content of the post."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether a specific user has access to this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Identifier for an associated workflow."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was first created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was last updated."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/generate"}}, {"who": "User", "if": null, "action": {"type": "fill", "element": {"type": "text:input", "eventId": "topic_field"}}}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button:primary", "eventId": "generate_post_button"}}}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "createPost_request_1", "notify": {"target": "me", "title": "Post Generation", "message": "Your blog post draft is being generated.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button:primary", "eventId": "generate_post_button"}}}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPost_request_1"}}}]}, "requests": {"createPost_request_1": {"requestId": "createPost_request_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating your blog post draft...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing the generated post...", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "AI content generator", "objective": "Generate a comprehensive blog post draft based on the user's topic."}, "dataSchema": "post", "type": "Create", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id"}}}}
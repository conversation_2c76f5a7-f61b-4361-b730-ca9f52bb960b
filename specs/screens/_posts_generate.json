{"screenSpecs": {"path": "/posts/generate", "description": "The main screen where users input a topic or prompt to trigger AI-powered blog post generation", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-generated content post, including its title, summary, full content, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A concise summary of the post's content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who created this post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user associated with this post, representing a one-to-many relationship."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The main topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits a user needs to access this post."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the user currently has access to the post."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "load"}}, {"who": "User", "action": {"type": "fill", "element": {"type": "text_input", "eventId": "post_topic_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPost_request_1", "notify": {"target": "me", "title": "Post Generation Initiated", "message": "Your blog post is being generated by AI. We'll notify you when it's ready!", "link": "/workflow/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPost_request_1"}}}]}, "requests": {"createPost_request_1": {"requestId": "createPost_request_1", "useWorkflow": {"tasks": [{"name": "generatePostService.generatePost", "uiDescription": "Generating your blog post with AI...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your generated blog post...", "dependencies": ["generatePostService.generatePost"], "connect": ["generatePostService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "Content creator assistant", "objective": "Generate a comprehensive blog post based on the provided topic."}, "dataSchema": "post", "type": "Create", "body": {"topic": "string"}, "notifyLink": "/workflow/:id"}}}}
{"screenSpecs": {"path": "/posts/generate", "description": "A screen where users input a topic or specific instructions to leverage AI for generating a new blog post", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "Represents a user's post, including its title, content, and related metadata. Each post is linked to a specific user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post. Automatically generated and stored as VARCHAR(36)."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post, limited to 255 characters."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who authored this post. Stored as VARCHAR(36)."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The User entity representing the author of this post. Establishes a relationship using userId."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was created or published."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post, stored as TEXT."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the current user has access to view this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow, stored as VARCHAR(36)."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The primary topic or category of the post, stored as TEXT."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was created, defaults to the current time."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was last updated, automatically updated on changes."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "generatePost_request_1"}}}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "post_topic_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "generatePost_request_1", "notify": {"target": "me", "title": "Post Generation Started", "message": "Your blog post is being generated. You will be notified when it's ready.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "generatePost_request_1"}}}]}, "requests": {"generatePost_request_1": {"requestId": "generatePost_request_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating blog post content using AI.", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing the generated blog post.", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "AI Blog Post Generator", "objective": "Generate a comprehensive and engaging blog post based on the provided topic."}, "dataSchema": "post", "type": "Create", "body": {"topic": "string"}, "notifyLink": "/posts/:id"}}}}
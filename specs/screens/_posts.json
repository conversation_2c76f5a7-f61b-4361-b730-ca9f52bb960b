{"screenSpecs": {"path": "/posts", "description": "The main screen displaying a list of blog post cards, each showing the title, author, date, and a 'Premium' tag for exclusive content, allowing quick browsing and selection", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-created post, including its content, metadata, and relationship to the user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who created this post."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was published or created."}, "content": {"type": "String", "required": false, "isUser": false, "description": "The main content of the post."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether a specific user has access to this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Identifier for an associated workflow."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was first created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was last updated."}}}}, "userExperience": {"ListPosts": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "listPosts_request_1"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "selectPost_click"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "selectPost_click"}}}]}, "requests": {"listPosts_request_1": {"requestId": "listPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}}}}
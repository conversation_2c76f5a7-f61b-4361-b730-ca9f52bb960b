{"screenSpecs": {"path": "/posts", "description": "Displays a list of blog posts, showing only public information like titles and summaries, with an indication if they require unlocking", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "Represents a publication or written contribution by a user, potentially related to a specific topic.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for each post; generated via cuid()."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post, limited to 255 characters."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief overview or summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post expressed in text."}, "userId": {"type": "String", "required": true, "isUser": true, "description": "ID of the user who authored the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was initially created, set to current time by default."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated, updated automatically upon modification."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The subject or theme of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier linking the post to a specific workflow or process."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post requires a premium subscription to access."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post if it is premium."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating if the current viewer has access to view the post based on subscription or credits."}}}}, "userExperience": {"ViewPost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findPosts_request_1"}}}, {"who": "User", "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}}]}, "requests": {"findPosts_request_1": {"requestId": "findPosts_request_1", "isArray": true, "useWorkflow": null, "useAi": null, "dataSchema": "post", "type": "Find", "params": {}, "body": {}, "notifyLink": null, "useAuthStore": null}}}}
{"screenSpecs": {"path": "/posts", "description": "The primary screen displaying a list of blog posts with titles, authors, and summaries. Exclusive content will show only the title and summary in the listing. Displays a list of available blog posts", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "Represents a user's post, including its title, content, and related metadata. Each post is linked to a specific user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post. Automatically generated and stored as VARCHAR(36)."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post, limited to 255 characters."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who authored this post. Stored as VARCHAR(36)."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The User entity representing the author of this post. Establishes a relationship using userId."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was created or published."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post, stored as TEXT."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the current user has access to view this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow, stored as VARCHAR(36)."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The primary topic or category of the post, stored as TEXT."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was created, defaults to the current time."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was last updated, automatically updated on changes."}}}}, "userExperience": {"BrowseReadPosts": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "browseReadPosts_request_1"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry_click"}}, "when": null}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry_click"}}}], "CommentonPost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findPosts_request_1"}}}, {"who": "User", "if": null, "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}}]}, "requests": {"browseReadPosts_request_1": {"requestId": "browseReadPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}, "findPosts_request_1": {"requestId": "findPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}}}}
{"screenSpecs": {"path": "/posts/:id", "description": "The screen displaying the full content of the AI-generated blog post. Dedicated screen displaying the full content of a single blog post after it has been unlocked by credits. Integrated within the post detail screen, this section allows users to view existing comments and submit new ones", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "Represents a publication or written contribution by a user, potentially related to a specific topic.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for each post; generated via cuid()."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post, limited to 255 characters."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief overview or summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post expressed in text."}, "userId": {"type": "String", "required": true, "isUser": true, "description": "ID of the user who authored the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was initially created, set to current time by default."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated, updated automatically upon modification."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The subject or theme of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier linking the post to a specific workflow or process."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post requires a premium subscription to access."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post if it is premium."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating if the current viewer has access to view the post based on subscription or credits."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Entity for user comments on a blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "content": {"type": "string", "required": true, "description": "The text content of the comment."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who posted the comment."}, "objectId": {"type": "string", "required": true, "description": "The ID of the entity (e.g., post) being commented on."}, "objectType": {"type": "string", "required": true, "description": "The type of the entity (e.g., 'post') being commented on."}, "createdAt": {"type": "string", "required": false, "description": "Timestamp when the comment was created."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_1"}}}], "ViewPost": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}}], "AddComment": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "getPostComments_request_1"}}}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "comment_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_2", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A new comment has been added to your post.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}, {"who": "App", "action": {"type": "load", "request": {"requestId": "getPostComments_request_1"}}}]}, "requests": {"readPost_request_1": {"requestId": "readPost_request_1", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "readPost_request_3": {"requestId": "readPost_request_3", "useWorkflow": null, "useAi": null, "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null, "useAuthStore": null}, "getPostComments_request_1": {"requestId": "getPostComments_request_1", "isArray": true, "useWorkflow": null, "useAi": null, "dataSchema": "comment", "type": "Find", "params": {}, "body": {"objectId": "string", "objectType": "string"}, "notifyLink": null}, "createComment_request_2": {"requestId": "createComment_request_2", "isArray": false, "useWorkflow": null, "useAi": null, "dataSchema": "comment", "type": "Create", "params": {}, "body": {"content": "string", "objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id"}}}}
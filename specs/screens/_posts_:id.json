{"screenSpecs": {"path": "/posts/:id", "description": "The screen that displays the generated blog post draft. The screen dedicated to displaying the full content of a selected blog post, with access controls for premium content", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-created post, including its content, metadata, and relationship to the user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who created this post."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was published or created."}, "content": {"type": "String", "required": false, "isUser": false, "description": "The main content of the post."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether a specific user has access to this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Identifier for an associated workflow."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was first created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was last updated."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}}], "ListPosts": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_2"}}}]}, "requests": {"readPost_request_3": {"requestId": "readPost_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}}, "readPost_request_2": {"requestId": "readPost_request_2", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}}}}
{"screenSpecs": {"path": "/posts/:id", "description": "The screen that displays the newly generated blog post, allowing users to review and manage it. The screen that displays the full content of an individual blog post, requiring access for exclusive content. A dedicated screen for viewing individual blog posts, displaying content, and enabling users to read and submit comments", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "Represents a user's post, including its title, content, and related metadata. Each post is linked to a specific user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post. Automatically generated and stored as VARCHAR(36)."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post, limited to 255 characters."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who authored this post. Stored as VARCHAR(36)."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The User entity representing the author of this post. Establishes a relationship using userId."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was created or published."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post, stored as TEXT."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the current user has access to view this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow, stored as VARCHAR(36)."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The primary topic or category of the post, stored as TEXT."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was created, defaults to the current time."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was last updated, automatically updated on changes."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Schema for user comments on blog posts.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "objectId": {"type": "string", "required": true, "description": "ID of the object (e.g., post) this comment is associated with."}, "objectType": {"type": "string", "required": true, "description": "Type of the object this comment is associated with (e.g., 'post')."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who submitted the comment."}, "content": {"type": "string", "required": true, "description": "The textual content of the comment."}, "createdAt": {"type": "string", "required": true, "description": "Timestamp when the comment was created."}, "updatedAt": {"type": "string", "required": true, "description": "Timestamp when the comment was last updated."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}}], "BrowseReadPosts": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "browseReadPosts_request_3"}}}], "CommentonPost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPostDetails_request_1"}}}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findPostComments_request_1"}}}, {"who": "User", "if": null, "action": {"type": "fill", "element": {"type": "input:text", "eventId": "comment_input"}}}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "createComment_request_1", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A user left a comment on your post.", "link": "/posts/{{objectId}}"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}]}, "requests": {"readPost_request_3": {"requestId": "readPost_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "browseReadPosts_request_3": {"requestId": "browseReadPosts_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "readPostDetails_request_1": {"requestId": "readPostDetails_request_1", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "findPostComments_request_1": {"requestId": "findPostComments_request_1", "isArray": true, "dataSchema": "comment", "type": "Find", "params": {"objectId": "string", "objectType": "string"}}, "createComment_request_1": {"requestId": "createComment_request_1", "dataSchema": "comment", "type": "Create", "body": {"objectId": "string", "objectType": "string", "content": "string"}, "notifyLink": "/posts/:id"}}}}
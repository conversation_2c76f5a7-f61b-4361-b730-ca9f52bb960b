{"screenSpecs": {"path": "/posts/:id", "description": "A screen to display the fully generated blog post. Shows the detailed content of a specific blog post after access has been verified or purchased. Integrated within the post detail screen, this section allows users to view existing comments and submit new ones", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-generated content post, including its title, summary, full content, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A concise summary of the post's content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who created this post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user associated with this post, representing a one-to-many relationship."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The main topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits a user needs to access this post."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the user currently has access to the post."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Entity for user comments on a blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "content": {"type": "string", "required": true, "description": "The text content of the comment."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who submitted the comment."}, "objectId": {"type": "string", "required": true, "description": "ID of the entity (e.g., post) being commented on."}, "objectType": {"type": "string", "required": true, "description": "Type of the entity being commented on (e.g., 'post')."}, "createdAt": {"type": "string", "required": false, "description": "Timestamp when the comment was created."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}}], "ViewPost": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "viewPost_request_4"}}}], "AddComment": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_1"}}}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findComments_request_2"}}}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "comment_text_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_3", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A new comment has been added to your post.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}}]}, "requests": {"readPost_request_3": {"requestId": "readPost_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "viewPost_request_4": {"requestId": "viewPost_request_4", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null}, "readPost_request_1": {"requestId": "readPost_request_1", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "findComments_request_2": {"requestId": "findComments_request_2", "isArray": true, "dataSchema": "comment", "type": "Find", "params": {}, "body": {"objectId": "string", "objectType": "string"}}, "createComment_request_3": {"requestId": "createComment_request_3", "useWorkflow": null, "useAi": null, "isArray": false, "dataSchema": "comment", "type": "Create", "body": {"content": "string", "objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id"}}}}
{"screenSpecs": {"path": "/ai-post-generator", "description": "The primary screen where users input a topic or prompt to trigger AI-powered blog post generation", "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "Represents a publication or written contribution by a user, potentially related to a specific topic.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for each post; generated via cuid()."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post, limited to 255 characters."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief overview or summary of the post."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post expressed in text."}, "userId": {"type": "String", "required": true, "isUser": true, "description": "ID of the user who authored the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was initially created, set to current time by default."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated, updated automatically upon modification."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The subject or theme of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier linking the post to a specific workflow or process."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post requires a premium subscription to access."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post if it is premium."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating if the current viewer has access to view the post based on subscription or credits."}}}}, "userExperience": {"GeneratePost": [{"who": "App", "if": null, "action": {"type": "load", "path": "/ai-post-generator"}}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "post_topic_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "generatePost_create_1", "notify": {"target": "me", "title": "Post Generated", "message": "Your blog post has been successfully created!", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "generatePost_create_1"}}}]}, "requests": {"generatePost_create_1": {"requestId": "generatePost_create_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating your blog post using AI...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Saving your generated post...", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "Content Creator Assistant", "objective": "To generate a complete, coherent, and engaging blog post based on a user-provided topic."}, "dataSchema": "post", "type": "Create", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id"}}}}
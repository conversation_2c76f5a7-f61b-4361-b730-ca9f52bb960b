{"post": [{"type": "POST", "path": "/workflow/post/find", "useWorkflow": {"tasks": [{"name": "blogService.findPostsFromInputInput", "uiDescription": "Retrieving blog posts", "dependencies": [], "next": []}]}, "params": {}, "body": {}, "dataResult": {"items": [{"id": "post123", "title": "Exploring the Future of AI", "userId": "user001", "user": {"name": "<PERSON>"}, "date": "2023-10-26T10:00:00Z", "content": "A deep dive into artificial intelligence trends.", "isPremium": true, "hasAccess": true, "workflowId": "wf_ai_post123", "topic": "AI", "createdAt": "2023-10-25T09:00:00Z", "updatedAt": "2023-10-25T09:00:00Z"}, {"id": "post124", "title": "Blockchain Beyond Cryptocurrencies", "userId": "user002", "user": {"name": "<PERSON>"}, "date": "2023-10-25T11:30:00Z", "content": "Understanding the wider applications of blockchain technology.", "isPremium": false, "hasAccess": true, "workflowId": "wf_blockchain_post124", "topic": "Blockchain", "createdAt": "2023-10-24T10:30:00Z", "updatedAt": "2023-10-24T10:30:00Z"}, {"id": "post125", "title": "Sustainable Living Practices", "userId": "user003", "user": {"name": "<PERSON>"}, "date": "2023-10-24T14:45:00Z", "content": "Tips and tricks for an eco-friendly lifestyle.", "isPremium": true, "hasAccess": false, "workflowId": "wf_sustainability_post125", "topic": "Sustainability", "createdAt": "2023-10-23T13:45:00Z", "updatedAt": "2023-10-23T13:45:00Z"}]}, "steps": {"blogService.findPostsFromInputInput": {"originalName": "findPosts", "model": "gemini-1.5-flash", "provider": "google", "modelInput": "prompt", "modelOutput": "text", "extraParams": {}, "input": "[\"input\"]"}}}, {"type": "POST", "path": "/workflow/post/create", "useAi": {"generationType": ["text"], "role": "AI content generator", "objective": "Generate a comprehensive blog post draft based on the user's topic.", "context": ["topic"], "output": [{"title": "title", "outputDescription": "The AI-generated title for the blog post."}, {"content": "content", "outputDescription": "The AI-generated main content of the blog post draft."}]}, "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePostFromPromptInput", "uiDescription": "Generating your blog post draft...", "dependencies": [], "next": [{"name": "postWorkflowService.storePostFromPostInput", "uiDescription": "Storing the generated post...", "dependencies": ["postWorkflowService.generatePostFromPromptInput"], "connect": ["postWorkflowService.generatePostFromPromptInput"]}]}]}, "params": {}, "body": {"topic": "string"}, "dataResult": {"id": "post_id_abc123", "title": "AI-Generated Post Title", "userId": "user_id_xyz789", "user": {"id": "user_id_xyz789", "name": "<PERSON>"}, "date": "2024-07-30T10:00:00Z", "content": "This is the AI-generated content for the blog post draft...", "topic": "Future of AI", "workflowId": "workflow_id_def456", "createdAt": "2024-07-30T09:30:00Z", "updatedAt": "2024-07-30T09:30:00Z"}, "notifyLink": "/posts/:id", "steps": {"postWorkflowService.generatePostFromPromptInput": {"originalName": "postWorkflowService.generatePost", "model": "gemini-1.5-pro", "provider": "google", "modelInput": "prompt", "modelOutput": "text", "extraParams": {}, "input": "[\"input\"]"}, "postWorkflowService.storePostFromPostInput": {"originalName": "postWorkflowService.storePost", "input": "[\"postWorkflowService.generatePostFromPromptInput\"].input"}}}], ":id": [{"type": "GET", "path": "/posts/:id", "useWorkflow": {}, "params": {"id": "post_id_123"}, "body": {}, "dataResult": {"id": "post_id_123", "title": "Exclusive Premium Content: The Future of AI", "userId": "user_id_456", "user": {"id": "user_id_456", "name": "<PERSON>"}, "date": "2023-10-26T10:00:00Z", "content": "This content is locked because you don't have access to this premium post. To view the full article, please purchase access.  \n\n**Key takeaways:**\n* AI is rapidly evolving.\n* Ethical considerations are paramount.\n* Future applications are boundless.", "isPremium": true, "hasAccess": false, "workflowId": null, "topic": "Technology", "createdAt": "2023-10-25T09:00:00Z", "updatedAt": "2023-10-26T10:00:00Z"}}]}
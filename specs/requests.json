{"post": [{"type": "POST", "path": "/workflow/post/find", "useAi": null, "useWorkflow": {}, "params": {}, "body": {}, "dataResult": {"items": [{"id": "post_id_1", "title": "Introduction to AI", "summary": "A brief overview of Artificial Intelligence and its applications.", "content": "Full content of post 1 for authorized users.", "userId": "user_id_1", "user": {"name": "<PERSON>"}, "createdAt": "2023-01-15T10:00:00Z", "updatedAt": "2023-01-15T10:00:00Z", "topic": "Technology", "workflowId": "workflow_id_1", "isPremium": false, "creditsRequired": 0, "hasAccess": true}, {"id": "post_id_2", "title": "The Future of Web Development", "summary": "Exploring new trends and technologies shaping the web.", "content": "Full content of post 2 for authorized users.", "userId": "user_id_2", "user": {"name": "<PERSON>"}, "createdAt": "2023-02-20T11:30:00Z", "updatedAt": "2023-02-20T11:30:00Z", "topic": "Web Development", "workflowId": "workflow_id_2", "isPremium": true, "creditsRequired": 5, "hasAccess": false}]}, "notifyLink": null}, {"type": "GET", "path": "/workflow/post/:id", "useWorkflow": {}, "params": {"id": ":id"}, "body": {}, "dataResult": {"id": "post123", "title": "My First Blog Post", "summary": "A concise summary of the post's content.", "content": "This is the full, detailed content of my first blog post, accessible because hasAccess is true.", "userId": "user456", "user": {"name": "<PERSON>"}, "createdAt": "2023-01-01T10:00:00Z", "updatedAt": "2023-01-01T10:00:00Z", "topic": "Technology", "isPremium": true, "creditsRequired": 10, "hasAccess": true}}, {"type": "POST", "path": "/workflow/post/create", "useAi": {"generationType": ["text"], "role": "Content creator assistant", "objective": "Generate a comprehensive blog post based on the provided topic.", "context": ["topic"], "output": [{"title": "title", "outputDescription": "The title of the generated blog post."}, {"summary": "summary", "outputDescription": "A concise summary of the blog post content."}, {"content": "content", "outputDescription": "The full content of the generated blog post."}, {"isPremium": "isPremium", "outputDescription": "Indicates if the generated post is premium content."}, {"creditsRequired": "creditsRequired", "outputDescription": "Credits needed to access the generated post."}]}, "useWorkflow": {"tasks": [{"name": "generatePostService.generatePostFromPromptInput", "uiDescription": "Generating your blog post with AI...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your generated blog post...", "dependencies": ["generatePostService.generatePostFromPromptInput"], "connect": ["generatePostService.generatePostFromPromptInput"]}]}]}, "params": {}, "body": {"topic": "AI in Content Creation"}, "dataResult": {"id": "post_id_abc123", "title": "The Future of AI in Content Creation", "summary": "An exploration into how artificial intelligence is transforming the landscape of content generation, from blog posts to marketing copy.", "content": "Artificial intelligence is rapidly evolving, bringing unprecedented changes to various industries, particularly in content creation. AI-powered tools can now generate high-quality articles, marketing copy, and even creative stories, significantly reducing the time and effort required for content production. This shift not only boosts productivity but also opens new avenues for personalized and data-driven content strategies...", "userId": "user_id_xyz789", "user": {"id": "user_id_xyz789", "name": "<PERSON>"}, "createdAt": "2023-10-27T10:00:00Z", "updatedAt": "2023-10-27T10:00:00Z", "topic": "AI in Content Creation", "workflowId": "workflow_id_def456", "isPremium": false, "creditsRequired": 1, "hasAccess": true}, "notifyLink": "/workflow/:id", "steps": {"generatePostService.generatePostFromPromptInput": {"originalName": "generatePostService.generatePost", "model": "gemini-1.5-pro", "provider": "google", "modelInput": "prompt", "modelOutput": "text", "extraParams": {}, "input": "[\"input\"]"}, "postWorkflowService.storePost": {"originalName": "postWorkflowService.storePost", "input": "[\"generatePostService.generatePostFromPromptInput\"].input"}}}], "find": [{"type": "POST", "path": "/comment/find", "useWorkflow": {}, "params": {}, "body": {"objectId": ":id", "objectType": "post"}, "dataResult": {"items": [{"id": "comment001", "content": "Great post! Very insightful.", "userId": "user789", "objectId": "post123", "objectType": "post", "createdAt": "2023-01-01T11:00:00Z", "user": {"name": "<PERSON>"}}, {"id": "comment002", "content": "I learned a lot from this.", "userId": "user101", "objectId": "post123", "objectType": "post", "createdAt": "2023-01-01T11:30:00Z", "user": {"name": "<PERSON>"}}]}}], "create": [{"type": "POST", "path": "/comment/create", "useWorkflow": {}, "params": {}, "body": {"content": "string", "objectId": "string", "objectType": "string"}, "dataResult": {"id": "comment003", "content": "This is a new comment.", "userId": "user123", "objectId": "post123", "objectType": "post", "createdAt": "2023-01-02T09:00:00Z", "user": {"name": "Current User"}}, "notifyLink": "/posts/:id"}]}
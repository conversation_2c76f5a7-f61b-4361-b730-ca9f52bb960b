{"screenSpecs": {"name": "GeneratePost", "type": "page", "path": "/posts/generate", "description": "Screen for generating a blog post draft using AI based on a user-provided topic, initiating a workflow for content creation.", "userExperience": [{"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/generate"}, "where": "/posts/generate"}, {"who": "User", "if": null, "action": {"type": "fill", "element": {"type": "text:input", "eventId": "topic_field"}}, "where": "/posts/generate"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button:primary", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "createPost_request_1", "notify": {"target": "me", "title": "Post Generation", "message": "Your blog post draft is being generated.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button:primary", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": "createPost_request_1"}, "where": "/posts/generate"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-created post, including its content, metadata, and relationship to the user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who created this post."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was published or created."}, "content": {"type": "String", "required": false, "isUser": false, "description": "The main content of the post."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether a specific user has access to this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Identifier for an associated workflow."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was first created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was last updated."}}}}, "requests": {"createPost_request_1": {"useAi": {"generationType": ["text"], "role": "AI content generator", "objective": "Generate a comprehensive blog post draft based on the user's topic.", "context": ["topic"], "output": [{"title": "title", "outputDescription": "The AI-generated title for the blog post."}, {"content": "content", "outputDescription": "The AI-generated main content of the blog post draft."}]}, "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating your blog post draft...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing the generated post...", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "dataResult": {"id": "post_id_abc123", "title": "AI-Generated Post Title", "userId": "user_id_xyz789", "user": {"id": "user_id_xyz789", "name": "<PERSON>"}, "date": "2024-07-30T10:00:00Z", "content": "This is the AI-generated content for the blog post draft...", "topic": "Future of AI", "workflowId": "workflow_id_def456", "createdAt": "2024-07-30T09:30:00Z", "updatedAt": "2024-07-30T09:30:00Z"}, "type": "POST", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id", "accessCredit": {"consumes": {"text": 1}}, "path": "/workflow/post/create", "onSuccess": {"actionType": "navigate", "path": "/workflow/:id"}}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostGenerationForm", "layoutType": "Vertical", "description": "Input section for providing an AI prompt and initiating post generation.", "elements": [{"name": "PostPromptInput", "description": "Form for user to enter the topic for AI-generated blog post.", "dataSource": null, "inputs": [{"eventId": "topic_field", "componentName": "TextArea", "fieldName": "topic"}, {"eventId": "generate_post_button", "componentName": "<PERSON><PERSON>", "type": "submit"}]}]}]}]}}
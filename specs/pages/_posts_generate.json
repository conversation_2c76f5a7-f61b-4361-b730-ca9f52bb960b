{"screenSpecs": {"name": "GenerateBlogPost", "type": "page", "path": "/posts/generate", "description": "The main screen where users input a topic or prompt to trigger AI-powered blog post generation", "userExperience": [{"who": "App", "if": null, "action": {"type": "load"}, "where": "/posts/generate"}, {"who": "User", "action": {"type": "fill", "element": {"type": "text_input", "eventId": "post_topic_input"}}, "where": "/posts/generate"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPost_request_1", "notify": {"target": "me", "title": "Post Generation Initiated", "message": "Your blog post is being generated by AI. We'll notify you when it's ready!", "link": "/workflow/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPost_request_1"}}, "where": "/posts/generate"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-generated content post, including its title, summary, full content, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A concise summary of the post's content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who created this post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user associated with this post, representing a one-to-many relationship."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The main topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits a user needs to access this post."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the user currently has access to the post."}}}}, "requests": {"createPost_request_1": {"useAi": {"generationType": ["text"], "role": "Content creator assistant", "objective": "Generate a comprehensive blog post based on the provided topic.", "context": ["topic"], "output": [{"title": "title", "outputDescription": "The title of the generated blog post."}, {"summary": "summary", "outputDescription": "A concise summary of the blog post content."}, {"content": "content", "outputDescription": "The full content of the generated blog post."}, {"isPremium": "isPremium", "outputDescription": "Indicates if the generated post is premium content."}, {"creditsRequired": "creditsRequired", "outputDescription": "Credits needed to access the generated post."}]}, "useWorkflow": {"tasks": [{"name": "generatePostService.generatePost", "uiDescription": "Generating your blog post with AI...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your generated blog post...", "dependencies": ["generatePostService.generatePost"], "connect": ["generatePostService.generatePost"]}]}]}, "dataResult": {"id": "post_id_abc123", "title": "The Future of AI in Content Creation", "summary": "An exploration into how artificial intelligence is transforming the landscape of content generation, from blog posts to marketing copy.", "content": "Artificial intelligence is rapidly evolving, bringing unprecedented changes to various industries, particularly in content creation. AI-powered tools can now generate high-quality articles, marketing copy, and even creative stories, significantly reducing the time and effort required for content production. This shift not only boosts productivity but also opens new avenues for personalized and data-driven content strategies...", "userId": "user_id_xyz789", "user": {"id": "user_id_xyz789", "name": "<PERSON>"}, "createdAt": "2023-10-27T10:00:00Z", "updatedAt": "2023-10-27T10:00:00Z", "topic": "AI in Content Creation", "workflowId": "workflow_id_def456", "isPremium": false, "creditsRequired": 1, "hasAccess": true}, "type": "POST", "body": {"topic": "AI in Content Creation"}, "notifyLink": "/workflow/:id", "accessCredit": {"consumes": {"text": 1}}, "path": "/workflow/post/create", "onSuccess": {"actionType": "navigate", "path": "/workflow/:id"}}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "GeneratePostSection", "layoutType": "Vertical", "description": "Contains elements for generating a blog post.", "elements": [{"name": "Page<PERSON><PERSON>le", "description": "Title of the blog post generation page.", "components": [{"component": "Text", "texts": {"titleText": "Generate Your Blog Post"}}]}, {"name": "Instructions", "description": "Instructions for using the AI blog post generator.", "components": [{"component": "Text", "texts": {"instructionText": "Enter a topic or prompt below to generate an AI-powered blog post."}}]}, {"name": "GeneratePostForm", "description": "Form to input a topic and trigger AI blog post generation.", "inputs": [{"eventId": "post_topic_input", "componentName": "TextArea", "fieldName": "topic"}, {"eventId": "generate_post_button", "componentName": "<PERSON><PERSON>", "type": "submit", "texts": {"buttonText": "Generate Post"}}]}]}]}]}}
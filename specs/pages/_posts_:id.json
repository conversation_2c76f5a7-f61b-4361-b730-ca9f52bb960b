{"screenSpecs": {"name": "PostDetail", "type": "page", "path": "/posts/:id", "description": "A screen to display the fully generated blog post. Shows the detailed content of a specific blog post after access has been verified or purchased. Integrated within the post detail screen, this section allows users to view existing comments and submit new ones", "userExperience": [{"who": "App", "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "findComments_request_2"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "comment_text_input"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_3", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A new comment has been added to your post.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "findComments_request_2"}}, "when": {"type": "request", "request": "createComment_request_3"}, "where": "/posts/:id"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-generated content post, including its title, summary, full content, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A concise summary of the post's content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who created this post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user associated with this post, representing a one-to-many relationship."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The main topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits a user needs to access this post."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the user currently has access to the post."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Entity for user comments on a blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "content": {"type": "string", "required": true, "description": "The text content of the comment."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who submitted the comment."}, "objectId": {"type": "string", "required": true, "description": "ID of the entity (e.g., post) being commented on."}, "objectType": {"type": "string", "required": true, "description": "Type of the entity being commented on (e.g., 'post')."}, "createdAt": {"type": "string", "required": false, "description": "Timestamp when the comment was created."}}}}, "requests": {"readPost_request_3": {"isArray": false, "dataResult": {"id": "post123", "title": "My First Blog Post", "summary": "A concise summary of the post's content.", "content": "This is the full, detailed content of my first blog post, accessible because hasAccess is true.", "userId": "user456", "user": {"name": "<PERSON>"}, "createdAt": "2023-01-01T10:00:00Z", "updatedAt": "2023-01-01T10:00:00Z", "topic": "Technology", "isPremium": true, "creditsRequired": 10, "hasAccess": true}, "type": "GET", "params": {"id": ":id"}, "path": "/workflow/post/:id"}, "findComments_request_2": {"isArray": true, "dataResult": {"items": [{"id": "comment001", "content": "Great post! Very insightful.", "userId": "user789", "objectId": "post123", "objectType": "post", "createdAt": "2023-01-01T11:00:00Z", "user": {"name": "<PERSON>"}}, {"id": "comment002", "content": "I learned a lot from this.", "userId": "user101", "objectId": "post123", "objectType": "post", "createdAt": "2023-01-01T11:30:00Z", "user": {"name": "<PERSON>"}}]}, "type": "POST", "params": {}, "body": {"objectId": ":id", "objectType": "post"}, "path": "/comment/find"}, "createComment_request_3": {"isArray": false, "dataResult": {"id": "comment003", "content": "This is a new comment.", "userId": "user123", "objectId": "post123", "objectType": "post", "createdAt": "2023-01-02T09:00:00Z", "user": {"name": "Current User"}}, "type": "POST", "body": {"content": "string", "objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id", "path": "/comment/create", "onSuccess": {"actionType": "load", "requestId": "findComments_request_2"}}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostDisplay", "layoutType": "Vertical", "description": "Displays the detailed content of the blog post.", "elements": [{"name": "PostTitle", "description": "Title of the blog post.", "dataSource": "readPost_request_3", "components": [{"component": "Text", "data": "title"}]}, {"name": "AuthorInfo", "description": "Author's avatar and name.", "dataSource": "readPost_request_3", "components": [{"component": "Avatar", "data": "user.name"}, {"component": "Text", "data": "user.name"}]}, {"name": "PostSummaryText", "description": "A concise summary of the post's content.", "dataSource": "readPost_request_3", "components": [{"component": "Text", "data": "summary"}]}, {"name": "PostContentMarkdown", "description": "The full content of the post in Markdown format.", "dataSource": "readPost_request_3", "components": [{"component": "ReactMarkdown", "data": "content"}]}]}, {"groupName": "CommentsSection", "layoutType": "Vertical", "description": "Section for viewing existing comments and submitting new ones.", "elements": [{"name": "CommentsList", "description": "List of user comments for the post.", "dataSource": "findComments_request_2", "items": true, "components": [{"eventId": "comment_author_avatar", "component": "Avatar", "data": "user.name"}, {"eventId": "comment_author_name", "component": "Text", "data": "user.name"}, {"eventId": "comment_content_text", "component": "Text", "data": "content"}, {"eventId": "comment_date", "component": "Text", "data": "createdAt"}]}, {"name": "NewCommentForm", "description": "Form for submitting a new comment.", "dataSource": "createComment_request_3", "inputs": [{"eventId": "comment_text_input", "componentName": "TextArea", "fieldName": "content"}, {"eventId": "submit_comment_button", "componentName": "<PERSON><PERSON>", "type": "submit", "texts": {"buttonText": "Submit Comment"}}]}]}]}]}}
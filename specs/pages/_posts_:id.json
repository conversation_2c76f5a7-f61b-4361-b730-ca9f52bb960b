{"screenSpecs": {"name": "PostDetail", "type": "page", "path": "/posts/:id", "description": "The screen that displays the full content of an individual blog post, allowing users to review and manage it, including exclusive content access and comment submission.", "userExperience": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPostDetails_request_1"}}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "findPostComments_request_1"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "comment_input"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_1", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A user left a comment on your post.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": true, "description": "Represents a user's post, including its title, content, and related metadata. Each post is linked to a specific user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post. Automatically generated and stored as VARCHAR(36)."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post, limited to 255 characters."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who authored this post. Stored as VARCHAR(36)."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The User entity representing the author of this post. Establishes a relationship using userId."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was created or published."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post, stored as TEXT."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the current user has access to view this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow, stored as VARCHAR(36)."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The primary topic or category of the post, stored as TEXT."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was created, defaults to the current time."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post record was last updated, automatically updated on changes."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Schema for user comments on blog posts.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "objectId": {"type": "string", "required": true, "description": "ID of the object (e.g., post) this comment is associated with."}, "objectType": {"type": "string", "required": true, "description": "Type of the object this comment is associated with (e.g., 'post')."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who submitted the comment."}, "content": {"type": "string", "required": true, "description": "The textual content of the comment."}, "createdAt": {"type": "string", "required": true, "description": "Timestamp when the comment was created."}, "updatedAt": {"type": "string", "required": true, "description": "Timestamp when the comment was last updated."}}}}, "requests": {"readPostDetails_request_1": {"dataResult": {"id": "post-id-123", "title": "My Awesome Blog Post", "userId": "user-id-abc", "user": {"name": "<PERSON>"}, "date": "2023-10-26T10:00:00Z", "content": "This is the **full content** of my awesome blog post. It's very informative and engaging.", "isPremium": true, "hasAccess": true, "workflowId": null, "topic": "Technology", "createdAt": "2023-10-26T09:00:00Z", "updatedAt": "2023-10-26T09:00:00Z"}, "type": "GET", "params": {"id": "post-id-123"}, "path": "/workflow/post/:id"}, "findPostComments_request_1": {"isArray": true, "dataResult": {"items": [{"id": "comment-id-1", "objectId": "post-id-123", "objectType": "post", "userId": "user-id-xyz", "user": {"name": "<PERSON>"}, "content": "Great post! Very insightful.", "createdAt": "2023-10-26T11:00:00Z", "updatedAt": "2023-10-26T11:00:00Z"}, {"id": "comment-id-2", "objectId": "post-id-123", "objectType": "post", "userId": "user-id-pqr", "user": {"name": "<PERSON>"}, "content": "I learned a lot from this. Thanks!", "createdAt": "2023-10-26T12:00:00Z", "updatedAt": "2023-10-26T12:00:00Z"}]}, "type": "POST", "body": {"objectId": "post-id-123", "objectType": "post"}, "path": "/workflow/comment/find"}, "createComment_request_1": {"dataResult": {"id": "comment-id-3", "objectId": "post-id-123", "objectType": "post", "userId": "current-user-id", "user": {"name": "Current User"}, "content": "This is a new comment.", "createdAt": "2023-10-26T13:00:00Z", "updatedAt": "2023-10-26T13:00:00Z"}, "type": "POST", "body": {"objectId": "post-id-123", "objectType": "post", "content": "This is a new comment."}, "notifyLink": "/posts/:id", "path": "/workflow/comment/create", "onSuccess": {"actionType": "load", "requestId": "findPostComments_request_1"}}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostH<PERSON><PERSON>", "description": "Displays the title, author, date, and topic of the blog post.", "layoutType": "Vertical", "elements": [{"name": "PostTitleDisplay", "description": "The main title of the blog post.", "dataSource": "readPostDetails_request_1", "components": [{"component": "Text", "data": "title"}]}, {"name": "PostMetaData", "description": "Author's name and publication date.", "dataSource": "readPostDetails_request_1", "components": [{"component": "Text", "data": "user.name"}, {"component": "Text", "data": "date"}]}, {"name": "PostTopicDisplay", "description": "The primary topic or category of the post.", "dataSource": "readPostDetails_request_1", "components": [{"component": "Text", "texts": {"topicLabel": "Topic:"}}, {"component": "Text", "data": "topic"}]}]}, {"groupName": "PostContentDisplay", "description": "The main content area of the blog post, including premium status.", "layoutType": "Vertical", "elements": [{"name": "Post<PERSON>ain<PERSON><PERSON>nt", "description": "Displays the full markdown content of the blog post.", "dataSource": "readPostDetails_request_1", "components": [{"component": "ReactMarkdown", "data": "content"}]}, {"name": "PremiumContentIndicator", "description": "Indicates if the post is premium content.", "dataSource": "readPostDetails_request_1", "components": [{"component": "Badge", "data": "isPremium", "texts": {"premiumText": "Premium Content"}}]}]}, {"groupName": "CommentSection", "description": "Section for viewing and submitting comments.", "layoutType": "Vertical", "elements": [{"name": "CommentListDisplay", "description": "A list of comments on the post.", "dataSource": "findPostComments_request_1", "items": true, "components": [{"eventId": "commentAuthor_display", "component": "Text", "data": "user.name"}, {"eventId": "commentContent_display", "component": "Text", "data": "content"}, {"eventId": "commentDate_display", "component": "Text", "data": "createdAt"}]}, {"name": "CommentSubmissionForm", "description": "Form for users to submit new comments.", "inputs": [{"eventId": "comment_input", "componentName": "TextArea", "fieldName": "content"}, {"eventId": "submit_comment_button", "componentName": "<PERSON><PERSON>", "type": "submit", "texts": {"submitButton": "Submit Comment"}}]}]}]}]}}
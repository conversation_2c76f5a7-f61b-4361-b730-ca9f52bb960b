{"screenSpecs": {"name": "BlogPostDetail", "type": "page", "path": "/posts/:id", "description": "Displays the full content of a selected blog post, with access controls for premium content. If the user does not have access to premium content, a paywall modal is presented.", "userExperience": [{"who": "App", "action": {"type": "load", "request": {"requestId": "readPost_request_2"}}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": false, "isPremium": true}}, "action": {"type": "open", "modal": "/paywall"}, "when": {"type": "load", "request": "readPost_request_2"}, "where": "/posts/:id"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-created post, including its content, metadata, and relationship to the user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who created this post."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was published or created."}, "content": {"type": "String", "required": false, "isUser": false, "description": "The main content of the post."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether a specific user has access to this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Identifier for an associated workflow."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was first created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was last updated."}}}}, "requests": {"readPost_request_2": {"dataResult": {"id": "post_id_123", "title": "Exclusive Premium Content: The Future of AI", "userId": "user_id_456", "user": {"id": "user_id_456", "name": "<PERSON>"}, "date": "2023-10-26T10:00:00Z", "content": "This content is locked because you don't have access to this premium post. To view the full article, please purchase access.  \n\n**Key takeaways:**\n* AI is rapidly evolving.\n* Ethical considerations are paramount.\n* Future applications are boundless.", "isPremium": true, "hasAccess": false, "workflowId": null, "topic": "Technology", "createdAt": "2023-10-25T09:00:00Z", "updatedAt": "2023-10-26T10:00:00Z"}, "type": "GET", "params": {"id": "post_id_123"}, "path": "/posts/:id"}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostH<PERSON><PERSON>", "layoutType": "Vertical", "description": "Displays the title, author, publication date, and premium status of the blog post.", "elements": [{"name": "PostTitle", "description": "The main title of the blog post.", "dataSource": "readPost_request_2", "components": [{"component": "Text", "data": "title"}]}, {"name": "PostMeta", "description": "Author's name and publication date.", "dataSource": "readPost_request_2", "components": [{"component": "Text", "data": "user.name"}, {"component": "Text", "data": "date"}]}, {"name": "PostStatus", "description": "Badge indicating if the post is premium.", "dataSource": "readPost_request_2", "components": [{"eventId": "premium_status_badge", "component": "Badge", "data": "isPremium", "texts": {"textId": "premiumLabel", "text": "Premium"}}]}]}, {"groupName": "PostContentDisplay", "layoutType": "Vertical", "description": "Displays the main content of the blog post, showing a preview or full content based on access.", "elements": [{"name": "BlogPostText", "description": "The main body content of the blog post, rendered in markdown.", "dataSource": "readPost_request_2", "components": [{"component": "ReactMarkdown", "data": "content"}]}, {"name": "LastUpdatedInfo", "description": "Information about when the post was last updated.", "dataSource": "readPost_request_2", "components": [{"component": "Text", "texts": {"textId": "lastUpdatedPrefix", "text": "Last updated:"}}, {"component": "Text", "data": "updatedAt"}]}]}, {"groupName": "CallToAction", "layoutType": "Center", "description": "Call to action for users who don't have access to premium content.", "elements": [{"name": "AccessRestrictedMessage", "description": "Message informing the user about restricted premium content.", "dataSource": "readPost_request_2", "components": [{"component": "Text", "texts": {"textId": "restrictionMessage", "text": "This is premium content. Unlock full access to read the complete article and all other exclusive posts."}}]}, {"name": "AccessPurchaseButton", "description": "Button to prompt user to buy access to premium content.", "dataSource": "readPost_request_2", "components": [{"eventId": "buy_access_button", "component": "<PERSON><PERSON>", "texts": {"textId": "buyAccessText", "text": "Unlock Full Access"}}]}, {"name": "LearnMoreLink", "description": "Link to learn more about premium subscription benefits.", "dataSource": "readPost_request_2", "components": [{"eventId": "learn_more_link", "component": "<PERSON><PERSON>", "texts": {"textId": "learnMoreText", "text": "Learn More about Premium"}}]}]}]}]}}
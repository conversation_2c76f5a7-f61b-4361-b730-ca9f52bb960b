{"screenSpecs": {"name": "BlogPostsDisplay", "type": "page", "path": "/posts", "description": "The main screen displaying a list of blog post cards, each showing the title, author, date, and a 'Premium' tag for exclusive content, allowing quick browsing and selection", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "listPosts_request_1"}}, "where": "/posts"}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "selectPost_click"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "selectPost_click"}}, "where": "/posts"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-created post, including its content, metadata, and relationship to the user.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who created this post."}, "date": {"type": "DateTime", "required": true, "isUser": false, "description": "The date when the post was published or created."}, "content": {"type": "String", "required": false, "isUser": false, "description": "The main content of the post."}, "isPremium": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether the post is premium content."}, "hasAccess": {"type": "Boolean", "required": false, "isUser": false, "description": "Indicates whether a specific user has access to this post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Identifier for an associated workflow."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was first created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was last updated."}}}}, "requests": {"listPosts_request_1": {"isArray": true, "useWorkflow": {"tasks": [{"name": "findPosts", "uiDescription": "Retrieving blog posts", "dependencies": [], "next": []}]}, "dataResult": {"items": [{"id": "post123", "title": "Exploring the Future of AI", "userId": "user001", "user": {"name": "<PERSON>"}, "date": "2023-10-26T10:00:00Z", "content": "A deep dive into artificial intelligence trends.", "isPremium": true, "hasAccess": true, "workflowId": "wf_ai_post123", "topic": "AI", "createdAt": "2023-10-25T09:00:00Z", "updatedAt": "2023-10-25T09:00:00Z"}, {"id": "post124", "title": "Blockchain Beyond Cryptocurrencies", "userId": "user002", "user": {"name": "<PERSON>"}, "date": "2023-10-25T11:30:00Z", "content": "Understanding the wider applications of blockchain technology.", "isPremium": false, "hasAccess": true, "workflowId": "wf_blockchain_post124", "topic": "Blockchain", "createdAt": "2023-10-24T10:30:00Z", "updatedAt": "2023-10-24T10:30:00Z"}, {"id": "post125", "title": "Sustainable Living Practices", "userId": "user003", "user": {"name": "<PERSON>"}, "date": "2023-10-24T14:45:00Z", "content": "Tips and tricks for an eco-friendly lifestyle.", "isPremium": true, "hasAccess": false, "workflowId": "wf_sustainability_post125", "topic": "Sustainability", "createdAt": "2023-10-23T13:45:00Z", "updatedAt": "2023-10-23T13:45:00Z"}]}, "type": "POST", "body": {}, "path": "/workflow/post/find"}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "BlogPostsListGroup", "layoutType": "Vertical", "description": "Displays a list of blog post cards, each containing essential details and access information.", "elements": [{"name": "PostList", "description": "A scrollable list of blog post cards, allowing users to browse and select posts.", "dataSource": "listPosts_request_1", "items": true, "dataRequest": {"type": "POST", "path": "/workflow/post/find", "fields": {"PostCard": "items"}}, "components": [{"eventId": "selectPost_click", "component": "<PERSON><PERSON>", "data": "title"}, {"eventId": "postAuthor_text", "component": "Text", "data": "user.name"}, {"eventId": "postDate_text", "component": "Text", "data": "date"}, {"eventId": "postPremium_badge", "component": "Badge", "data": "isPremium"}]}]}]}]}}
{"screenSpecs": {"name": "BlogPosts", "type": "page", "path": "/posts", "description": "Displays a list of available blog posts, showing only their titles and summaries to prevent unauthorized access.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "viewPost_request_1", "notify": null}}, "where": "/posts"}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user-generated content post, including its title, summary, full content, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "Title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A concise summary of the post's content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The full content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The unique identifier of the user who created this post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user associated with this post, representing a one-to-many relationship."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp indicating when the post was last updated."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The main topic or category of the post."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional identifier for an associated workflow."}, "isPremium": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the post is premium content."}, "creditsRequired": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits a user needs to access this post."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Flag indicating whether the user currently has access to the post."}}}}, "requests": {"viewPost_request_1": {"isArray": true, "useAi": null, "useWorkflow": null, "dataResult": {"items": [{"id": "post_id_1", "title": "Introduction to AI", "summary": "A brief overview of Artificial Intelligence and its applications.", "content": "Full content of post 1 for authorized users.", "userId": "user_id_1", "user": {"name": "<PERSON>"}, "createdAt": "2023-01-15T10:00:00Z", "updatedAt": "2023-01-15T10:00:00Z", "topic": "Technology", "workflowId": "workflow_id_1", "isPremium": false, "creditsRequired": 0, "hasAccess": true}, {"id": "post_id_2", "title": "The Future of Web Development", "summary": "Exploring new trends and technologies shaping the web.", "content": "Full content of post 2 for authorized users.", "userId": "user_id_2", "user": {"name": "<PERSON>"}, "createdAt": "2023-02-20T11:30:00Z", "updatedAt": "2023-02-20T11:30:00Z", "topic": "Web Development", "workflowId": "workflow_id_2", "isPremium": true, "creditsRequired": 5, "hasAccess": false}]}, "type": "POST", "params": {}, "body": {}, "notifyLink": null, "accessCredit": null, "path": "/workflow/post/find", "onSuccess": null}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "B<PERSON><PERSON><PERSON><PERSON>", "layoutType": "Horizontal", "description": "Header section for the blog posts list page.", "elements": [{"name": "Page<PERSON><PERSON>le", "description": "Title of the blog posts page.", "dataSource": null, "inputs": [], "components": [{"component": "Text", "texts": {"title": "Available Blog Posts"}}]}, {"name": "PageDescription", "description": "Brief description of the blog posts page.", "dataSource": null, "inputs": [], "components": [{"component": "Text", "texts": {"descriptionText": "Explore a variety of articles. Click on a post to read its full content."}}]}]}, {"groupName": "PostListings", "layoutType": "Vertical", "description": "Displays a list of available blog posts, showing only their titles and summaries.", "elements": [{"name": "PostListIntroduction", "description": "Introductory text for the post list.", "dataSource": null, "inputs": [], "components": [{"component": "Text", "texts": {"intro": "Discover our latest insights and stories below."}}]}, {"name": "PostList", "description": "A scrollable list of blog post entries.", "dataSource": "viewPost_request_1", "items": true, "dataRequest": {"type": "POST", "path": "/workflow/post/find", "fields": {"PostTitleButton": "title", "PostSummaryText": "summary", "PostId": "id"}}, "components": [{"eventId": "post_entry", "component": "<PERSON><PERSON>", "data": "title"}, {"eventId": "post_summary_text", "component": "Text", "data": "summary"}], "inputs": []}]}]}]}}
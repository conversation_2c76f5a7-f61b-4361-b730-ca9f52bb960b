{"workflow": [{"step": 1, "script": "project", "status": "completed", "description": "Generate project configuration"}, {"step": 2, "script": "feature", "status": "completed", "description": "Generate feature configuration"}, {"step": 3, "script": "screen", "status": "completed", "description": "Generate screen specifications and unify feature data"}, {"step": 4, "script": "step", "status": "completed", "description": "Generate workflow steps and task configurations"}, {"step": 5, "script": "navigation", "status": "completed", "description": "Generate navigation configuration and routing"}, {"step": 6, "script": "home", "status": "completed", "description": "Generate home page routing and card configurations"}, {"step": 7, "script": "form", "status": "completed", "description": "Generate form specifications and validation"}, {"step": 8, "script": "notification", "status": "completed", "description": "Generate notification templates and configurations"}]}
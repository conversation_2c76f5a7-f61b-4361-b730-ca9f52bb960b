{"workflow": [{"step": 1, "script": "project", "status": "completed", "description": "Generate project configuration"}, {"step": 2, "script": "feature", "status": "completed", "description": "Generate feature configuration"}, {"step": 3, "script": "screen", "status": "pending", "description": "Generate screen specifications and unify feature data"}, {"step": 4, "script": "step", "status": "pending", "description": "Generate workflow steps and task configurations"}, {"step": 5, "script": "navigation", "status": "pending", "description": "Generate navigation configuration and routing"}, {"step": 6, "script": "home", "status": "pending", "description": "Generate home page routing and card configurations"}, {"step": 7, "script": "form", "status": "pending", "description": "Generate form specifications and validation"}, {"step": 8, "script": "generateServer", "status": "pending", "description": "Generate server-side code based on specifications"}, {"step": 9, "script": "generateScreens", "status": "pending", "description": "Generate client-side screens and components"}]}
{"feature": {"name": "Comment on Post", "description": "Enables users to submit and view comments on any public or purchased exclusive blog post, fostering community interaction.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findPosts_request_1"}}, "where": "/posts"}, {"who": "User", "if": null, "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPostWorkflow_request_1"}}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPostDetails_request_1"}}, "where": "/posts/:id"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findPostComments_request_1"}}, "where": "/posts/:id"}, {"who": "User", "if": null, "action": {"type": "fill", "element": {"type": "input:text", "eventId": "comment_input"}}, "where": "/posts/:id"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "createComment_request_1", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A user left a comment on your post.", "link": "/posts/{{objectId}}"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}], "screens": {"/posts": "Displays a list of available blog posts.", "/workflow/:id": "The screen that checks the generation status of a post before displaying it.", "/posts/:id": "A dedicated screen for viewing individual blog posts, displaying content, and enabling users to read and submit comments."}, "conditions": ["User must be logged in to submit a comment.", "Comments are displayed chronologically below the post content.", "The post must exist and be accessible to view comments."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": false, "description": "<PERSON><PERSON>a for a blog post, which can receive comments.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": true, "description": "Title of the blog post."}, "content": {"type": "string", "required": true, "description": "Full content of the blog post."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "ID of the author of the post."}, "createdAt": {"type": "string", "required": true, "description": "Timestamp when the post was created."}, "updatedAt": {"type": "string", "required": true, "description": "Timestamp when the post was last updated."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Schema for user comments on blog posts.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "objectId": {"type": "string", "required": true, "description": "ID of the object (e.g., post) this comment is associated with."}, "objectType": {"type": "string", "required": true, "description": "Type of the object this comment is associated with (e.g., 'post')."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who submitted the comment."}, "content": {"type": "string", "required": true, "description": "The textual content of the comment."}, "createdAt": {"type": "string", "required": true, "description": "Timestamp when the comment was created."}, "updatedAt": {"type": "string", "required": true, "description": "Timestamp when the comment was last updated."}}}, "workflow": {"description": "Handles the generation and processing status of various resources.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the workflow process."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'completed', 'failed')."}, "resourceId": {"type": "string", "required": false, "description": "ID of the resource being processed by the workflow."}, "resourceType": {"type": "string", "required": false, "description": "Type of the resource being processed (e.g., 'post', 'image')."}}}}, "requests": [{"requestId": "findPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}, {"requestId": "readPostWorkflow_request_1", "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPostDetails_request_1", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, {"requestId": "findPostComments_request_1", "isArray": true, "dataSchema": "comment", "type": "Find", "params": {"objectId": "string", "objectType": "string"}}, {"requestId": "createComment_request_1", "dataSchema": "comment", "type": "Create", "body": {"objectId": "string", "objectType": "string", "content": "string"}, "notifyLink": "/posts/:id"}]}}
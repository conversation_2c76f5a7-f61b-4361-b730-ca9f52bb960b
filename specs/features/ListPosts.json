{"feature": {"name": "List Posts", "description": "Displays a comprehensive list of all published blog posts. For exclusive posts, only the title, author, and date are shown, indicating it's premium content without revealing the full article.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "listPosts_request_1"}}, "where": "/posts"}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "selectPost_click"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "selectPost_click"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readWorkflow_request_1"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_2"}}, "where": "/posts/:id"}], "screens": {"/posts": "The main screen displaying a list of blog post cards, each showing the title, author, date, and a 'Premium' tag for exclusive content, allowing quick browsing and selection.", "/workflow/:id": "A screen to check the generation or processing status of a selected blog post before displaying its content.", "/posts/:id": "The screen dedicated to displaying the full content of a selected blog post, with access controls for premium content."}, "conditions": ["Blog posts are listed with title, author, and date.", "Exclusive content is clearly identified in the listing.", "Users can select a post from the list to view its details.", "Access to the full content of exclusive posts is subject to access verification."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": true, "description": "Entity representing a blog post, including its content and access status.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": true, "description": "Title of the blog post."}, "author": {"type": "string", "required": true, "isUser": true, "description": "Author of the blog post."}, "date": {"type": "string", "required": true, "description": "Publication date of the blog post."}, "content": {"type": "string", "required": false, "description": "Full content of the blog post, accessible based on 'hasAccess' status."}, "isPremium": {"type": "boolean", "required": false, "description": "Indicates if the post is exclusive or premium content."}, "hasAccess": {"type": "boolean", "required": false, "description": "Determines if the current user has access to view the full content of this post."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the blog post generation."}}}, "workflow": {"description": "Entity for tracking the generation status of a resource.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the workflow."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'completed', 'failed')."}, "resourceId": {"type": "string", "required": false, "description": "ID of the resource being generated or processed."}}}}, "requests": [{"requestId": "listPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}, {"requestId": "readWorkflow_request_1", "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPost_request_2", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}
{"feature": {"name": "Generate Post", "description": "Users can generate blog posts using AI by providing a topic or prompt, streamlining content creation.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "generatePost_request_1"}}, "where": "/posts/generate"}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "post_topic_input"}}, "where": "/posts/generate"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "generatePost_request_1", "notify": {"target": "me", "title": "Post Generation Started", "message": "Your blog post is being generated. You will be notified when it's ready.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "generatePost_request_1"}}, "where": "/posts/generate"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "readWorkflow_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "when": {"type": "load", "request": {"requestId": "readWorkflow_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}, "where": "/posts/:id"}], "screens": {"/posts/generate": "A screen where users input a topic or specific instructions to leverage AI for generating a new blog post.", "/workflow/:id": "The screen that checks the generation process of the post, displaying its status and progress.", "/posts/:id": "The screen that displays the newly generated blog post, allowing users to review and manage it."}, "conditions": ["User is authenticated.", "AI model is available for content generation.", "User has sufficient credits for post generation.", "A topic or prompt is provided for AI generation."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": true, "description": "Entity representing a blog post, including AI-generated content and associated metadata.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": true, "description": "The title of the generated blog post."}, "content": {"type": "string", "required": true, "description": "The full body content of the blog post, generated by AI."}, "author": {"type": "string", "required": true, "isUser": true, "description": "The author of the blog post, typically the user who initiated the generation."}, "topic": {"type": "string", "required": true, "description": "The original topic or prompt used to guide the AI generation."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the post generation process."}, "createdAt": {"type": "string", "required": false, "description": "Timestamp indicating when the post was created."}, "updatedAt": {"type": "string", "required": false, "description": "Timestamp automatically updated when the post is modified."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity tracking the progress and status of a resource generation workflow.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the workflow."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'completed', 'failed')."}, "entityId": {"type": "string", "required": true, "description": "ID of the entity being processed by the workflow (e.g., postId)."}, "entityType": {"type": "string", "required": true, "description": "Type of entity being processed (e.g., 'post')."}}}}, "requests": [{"requestId": "generatePost_request_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating blog post content using AI.", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing the generated blog post.", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "AI Blog Post Generator", "objective": "Generate a comprehensive and engaging blog post based on the provided topic."}, "dataSchema": "post", "type": "Create", "body": {"topic": "string"}, "notifyLink": "/posts/:id"}, {"requestId": "readWorkflow_request_2", "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPost_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}
{"feature": {"name": "Generate Post", "description": "Allows users to quickly create a blog post draft by providing a topic or prompt to an AI. The AI generates a full article, which can then be published.", "userExperience": [{"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/generate"}, "where": "/posts/generate"}, {"who": "User", "if": null, "action": {"type": "fill", "element": {"type": "text:input", "eventId": "topic_field"}}, "where": "/posts/generate"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button:primary", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "createPost_request_1", "notify": {"target": "me", "title": "Post Generation", "message": "Your blog post draft is being generated.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button:primary", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPost_request_1"}}, "where": "/posts/generate"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readWorkflow_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}, "where": "/posts/:id"}], "screens": {"/posts/generate": "A screen with a text input field for the AI prompt and a button to initiate content generation, displaying the generated draft upon completion.", "/workflow/:id": "The screen that displays the generation process of a blog post.", "/posts/:id": "The screen that displays the generated blog post draft."}, "conditions": ["User is authenticated as a content creator.", "AI service for content generation is operational.", "A valid topic or prompt is provided by the user."], "dataSchemas": {"post": {"canBeCommented": false, "usePayment": false, "description": "Entity for a blog post, including AI-generated drafts.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": false, "description": "Title of the blog post, often AI-generated."}, "content": {"type": "string", "required": false, "description": "Full content of the blog post, AI-generated."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who authored or generated the post."}, "topic": {"type": "string", "required": true, "description": "The user-provided topic or prompt for AI generation."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the blog post generation."}, "createdAt": {"type": "string", "required": true, "description": "Timestamp indicating when the post was created."}, "updatedAt": {"type": "string", "required": true, "description": "Timestamp automatically updated when the post is modified."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity for tracking the status and progress of resource generation workflows.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the workflow process."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'generating', 'completed', 'failed')."}, "resultId": {"type": "string", "required": false, "description": "ID of the resource generated by this workflow upon completion."}, "resultType": {"type": "string", "required": false, "description": "Type of resource generated by this workflow (e.g., 'post', 'image')."}, "createdAt": {"type": "string", "required": true, "description": "Timestamp indicating when the workflow was created."}, "updatedAt": {"type": "string", "required": true, "description": "Timestamp automatically updated when the workflow is modified."}}}}, "requests": [{"requestId": "createPost_request_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating your blog post draft...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing the generated post...", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "AI content generator", "objective": "Generate a comprehensive blog post draft based on the user's topic."}, "dataSchema": "post", "type": "Create", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id"}, {"requestId": "readWorkflow_request_2", "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}, "body": {}}, {"requestId": "readPost_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}}]}}
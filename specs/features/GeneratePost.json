{"feature": {"name": "Generate Post", "description": "Allows users to create new blog posts instantly using AI, by providing a topic or prompt.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load"}, "where": "/posts/generate"}, {"who": "User", "action": {"type": "fill", "element": {"type": "text_input", "eventId": "post_topic_input"}}, "where": "/posts/generate"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPost_request_1", "notify": {"target": "me", "title": "Post Generation Initiated", "message": "Your blog post is being generated by AI. We'll notify you when it's ready!", "link": "/workflow/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_post_button"}}, "where": "/posts/generate"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPost_request_1"}}, "where": "/posts/generate"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "readWorkflow_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "if": {"workflow": {"status": "completed"}}, "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}, "where": "/posts/:id"}], "screens": {"/posts/generate": "The main screen where users input a topic or prompt to trigger AI-powered blog post generation.", "/workflow/:id": "A screen to display the status and progress of the blog post generation workflow.", "/posts/:id": "A screen to display the fully generated blog post."}, "conditions": ["User must provide a valid topic or prompt for AI generation.", "AI service must be available to process the generation request.", "Generated blog post content must be saved and accessible."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": false, "description": "Entity for storing generated blog posts.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": true, "description": "Title of the blog post."}, "content": {"type": "string", "required": true, "description": "Full content of the blog post."}, "topic": {"type": "string", "required": true, "description": "The topic or prompt used to generate the post."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who generated the post."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the post generation."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity for tracking the status and progress of background processes, such as AI generation or file processing.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the workflow."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'processing', 'completed', 'failed')."}, "progress": {"type": "number", "required": false, "description": "Numerical progress of the workflow (0-100)."}, "resultObjectId": {"type": "string", "required": false, "description": "ID of the object generated or processed by the workflow upon completion."}, "resultObjectType": {"type": "string", "required": false, "description": "Type of the object generated or processed by the workflow (e.g., 'post', 'image')."}}}}, "requests": [{"requestId": "createPost_request_1", "useWorkflow": {"tasks": [{"name": "generatePostService.generatePost", "uiDescription": "Generating your blog post with AI...", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your generated blog post...", "dependencies": ["generatePostService.generatePost"], "connect": ["generatePostService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "Content creator assistant", "objective": "Generate a comprehensive blog post based on the provided topic."}, "dataSchema": "post", "type": "Create", "body": {"topic": "string"}, "notifyLink": "/workflow/:id"}, {"requestId": "readWorkflow_request_2", "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPost_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}
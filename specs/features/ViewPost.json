{"feature": {"name": "View Post", "description": "Enables users to access and read the full, exclusive content of a blog post after it has been unlocked using credits.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "viewPost_request_1"}}, "where": "/posts"}, {"who": "User", "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "viewPost_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "purchase_post_access"}}, "where": "/paywall"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "viewPost_request_3", "notify": {"target": "me", "title": "Access Granted", "message": "You have successfully unlocked this post and can now read its full content.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "purchase_post_access"}}, "where": "/paywall"}, {"who": "App", "action": {"type": "close", "modal": "/paywall"}, "when": {"type": "send", "request": {"requestId": "viewPost_request_3"}}, "where": "/paywall"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/paywall"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "viewPost_request_4"}}, "where": "/posts/:id"}], "screens": {"/posts": "Displays a list of available blog posts, showing only their titles and summaries to prevent unauthorized access.", "/workflow/:id": "Checks the generation status of a specific post before displaying its content.", "/posts/:id": "Shows the detailed content of a specific blog post after access has been verified or purchased."}, "conditions": ["User has sufficient credits to purchase access to a blog post.", "Blog post content has completed its generation workflow.", "User has purchased access to the specific blog post."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": true, "description": "Represents a blog post with content, author, and access restrictions.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": true, "description": "The title of the blog post."}, "summary": {"type": "string", "required": true, "description": "A brief summary of the blog post, visible in lists."}, "content": {"type": "string", "required": false, "description": "The full, exclusive content of the blog post."}, "author": {"type": "string", "required": true, "isUser": true, "description": "The author of the blog post."}, "creditsRequired": {"type": "number", "required": true, "description": "The number of credits required to unlock this post."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the post generation."}, "hasAccess": {"type": "boolean", "required": false, "description": "Indicates if the current user has access to the full content (derived by the server)."}}}, "userPostAccess": {"canBeCommented": false, "usePayment": false, "description": "Records a user's purchase of access to a specific blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the access record."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "The ID of the user who purchased access."}, "objectId": {"type": "string", "required": true, "description": "The ID of the post to which access was granted."}, "objectType": {"type": "string", "required": true, "description": "The type of entity being accessed (e.g., 'post')."}, "accessedAt": {"type": "string", "required": true, "description": "Timestamp when access was granted."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity for tracking the status and progress of resource generation workflows.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the workflow process."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'processing', 'completed', 'failed')."}, "resourceId": {"type": "string", "required": true, "description": "ID of the resource being processed by the workflow."}, "resourceType": {"type": "string", "required": true, "description": "Type of the resource being processed (e.g., 'post', 'image')."}}}}, "requests": [{"requestId": "viewPost_request_1", "isArray": true, "dataSchema": "post", "type": "Find", "params": {}, "body": {}, "notifyLink": null}, {"requestId": "viewPost_request_2", "isArray": false, "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null}, {"requestId": "viewPost_request_3", "isArray": false, "dataSchema": "userPostAccess", "type": "Create", "params": {}, "body": {"objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id"}, {"requestId": "viewPost_request_4", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null}]}}
{"feature": {"name": "Browse Read Posts", "description": "Allows users to view a listing of all available blog posts and access their full content. Exclusive posts require credits for full access, while titles are always visible.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "browseReadPosts_request_1"}}, "where": "/posts"}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry_click"}}, "when": null, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry_click"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "browseReadPosts_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "when": {"type": "load", "request": {"requestId": "browseReadPosts_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "browseReadPosts_request_3"}}, "where": "/posts/:id"}], "screens": {"/posts": "The primary screen displaying a list of blog posts with titles, authors, and summaries. Exclusive content will show only the title and summary in the listing.", "/workflow/:id": "The screen that checks the generation status of the blog post and redirects to the post page once completed.", "/posts/:id": "The screen that displays the full content of an individual blog post, requiring access for exclusive content."}, "conditions": ["Blog post titles and summaries are always visible in the listing.", "Access to full content of exclusive posts requires sufficient credits.", "Users can browse all available posts regardless of access status."], "dataSchemas": {"post": {"canBeCommented": false, "usePayment": true, "description": "Entity for detailed information about a blog post, including title, summary, and full content.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier of the blog post."}, "name": {"type": "string", "required": true, "description": "Title of the blog post."}, "summary": {"type": "string", "required": true, "description": "A brief summary or excerpt of the blog post, visible in listings."}, "content": {"type": "string", "required": true, "description": "The full content of the blog post. Access is required for viewing."}, "author": {"type": "string", "required": true, "isUser": true, "description": "The author of the blog post."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the post generation process."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity for tracking the status and progress of a resource generation workflow.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier of the workflow."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'processing', 'completed', 'failed')."}, "resourceId": {"type": "string", "required": false, "description": "ID of the resource being generated or processed by the workflow."}, "resourceType": {"type": "string", "required": false, "description": "Type of the resource being generated (e.g., 'post', 'image', 'video')."}}}}, "requests": [{"requestId": "browseReadPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}, {"requestId": "browseReadPosts_request_2", "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "browseReadPosts_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}
{"feature": {"name": "Add Comment", "description": "Allows users to submit comments on any blog post, fostering interaction and discussion among the community.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_1"}}, "where": "/posts/:id"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findComments_request_2"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "comment_text_input"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_3", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A new comment has been added to your post.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}], "screens": {"/posts/:id": "Integrated within the post detail screen, this section allows users to view existing comments and submit new ones."}, "conditions": ["User must be logged in to submit a comment.", "The post must exist and be accessible."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": false, "description": "Entity for blog post details, allowing users to view and interact with content.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the post."}, "title": {"type": "string", "required": true, "description": "Title of the blog post."}, "content": {"type": "string", "required": true, "description": "Full content of the blog post."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "ID of the post's author."}}}, "comment": {"canBeCommented": false, "usePayment": false, "description": "Entity for user comments on a blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "content": {"type": "string", "required": true, "description": "The text content of the comment."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who submitted the comment."}, "objectId": {"type": "string", "required": true, "description": "ID of the entity (e.g., post) being commented on."}, "objectType": {"type": "string", "required": true, "description": "Type of the entity being commented on (e.g., 'post')."}, "createdAt": {"type": "string", "required": false, "description": "Timestamp when the comment was created."}}}}, "requests": [{"requestId": "readPost_request_1", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, {"requestId": "findComments_request_2", "isArray": true, "dataSchema": "comment", "type": "Find", "params": {}, "body": {"objectId": "string", "objectType": "string"}}, {"requestId": "createComment_request_3", "useWorkflow": null, "useAi": null, "isArray": false, "dataSchema": "comment", "type": "Create", "body": {"content": "string", "objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id"}]}}
{"form": {"name": "PostPromptInput", "description": "Form for user to enter the topic for AI-generated blog post.", "inputs": [{"eventId": "topic_field", "data-testid": "topic-input", "component": "FormikTextArea", "fieldName": "topic", "type": "string", "label": "Topic for Blog Post", "placeholder": "Enter the topic for your blog post (e.g., The Impact of AI on Modern Education)", "data": {"value": ""}, "validations": {"required": {"enabled": true, "requiredMessageError": "The blog post topic is required."}, "minLength": {"enabled": true, "value": 5, "invalidMessageError": "The topic must be at least 5 characters long."}, "maxLength": {"enabled": true, "value": 150, "invalidMessageError": "The topic cannot exceed 150 characters."}}}], "buttons": [{"data-testid": "generate-post-button", "eventId": "generate_post_button", "componentName": "<PERSON><PERSON>", "type": "submit", "data": {"text": "Generate"}}]}}
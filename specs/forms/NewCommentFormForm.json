{"form": {"name": "NewCommentForm", "description": "Form for submitting a new comment.", "inputs": [{"eventId": "comment_text_input", "data-testid": "comment-content-textarea", "component": "FormikTextArea", "fieldName": "content", "type": "string", "label": "Comment Text", "placeholder": "Enter your comment here...", "data": {"value": ""}, "validations": {"required": {"enabled": true, "requiredMessageError": "This field is required."}, "minLength": {"enabled": true, "value": 5, "invalidMessageError": "Comment must be at least 5 characters long."}, "maxLength": {"enabled": true, "value": 255, "invalidMessageError": "Comment cannot exceed 255 characters."}}}], "buttons": [{"data-testid": "submit-comment-button", "eventId": "submit_comment_button", "componentName": "<PERSON><PERSON>", "type": "submit", "texts": {"buttonText": "Submit Comment"}, "data": {"text": "Submit"}}]}}
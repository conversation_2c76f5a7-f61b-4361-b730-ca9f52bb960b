{"form": {"name": "GeneratePostForm", "description": "Form to input a topic and trigger AI blog post generation.", "inputs": [{"eventId": "post_topic_input", "data-testid": "post-topic-input", "component": "FormikTextArea", "fieldName": "topic", "type": "string", "label": "Topic", "placeholder": "Enter the topic for your blog post...", "data": {"value": ""}, "validations": {"required": {"enabled": true, "requiredMessageError": "Blog post topic is required."}, "minLength": {"enabled": true, "value": 10, "invalidMessageError": "Topic must be at least 10 characters long."}, "maxLength": {"enabled": true, "value": 255, "invalidMessageError": "Topic cannot exceed 255 characters."}}}], "buttons": [{"data-testid": "generate-post-button", "eventId": "generate_post_button", "componentName": "<PERSON><PERSON>", "type": "submit", "texts": {"buttonText": "Generate Post"}, "data": {"text": "Generate"}}]}}
import {Module} from '@nestjs/common';
import {WorkflowController} from 'src/modules/workflow/workflow.controller';
import {WorkflowService} from 'src/modules/workflow/workflow.service';
import {AiService} from 'src/shared/ai/ai.service';
import {WorkerService} from './worker.service';

import {FileService} from 'src/shared/file/file.service';
import {NotificationService} from 'src/shared/notification/notification.service';
import {ImageWorkflowService} from './image/image.workflow.service';
import {ImageWorkflowController} from './image/image.workflow.controller';
import {AudioWorkflowService} from './audio/audio.workflow.service';
import {AudioWorkflowController} from './audio/audio.workflow.controller';
import {PostWorkflowController} from './post/post.workflow.controller';
import {PostWorkflowService} from './post/post.workflow.service';

@Module({
  imports: [],
  controllers: [
    Workflow<PERSON>ontroller,
    ImageWorkflowController,
    AudioWorkflowController,
   PostWorkflowController],
  providers: [
    WorkflowService,
    WorkerService,
    AiService,
    FileService,
    WorkflowController,
    NotificationService,
    ImageWorkflowService,
    AudioWorkflowService,
   PostWorkflowService, PostWorkflowController],
  exports: [WorkflowController, PostWorkflowController],
})
export class WorkflowModule {}

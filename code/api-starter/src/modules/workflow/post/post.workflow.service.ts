
import { NotFoundException, Injectable } from '@nestjs/common';
import { Post, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
import { FileService } from 'src/shared/file/file.service';
import { AiService, DEFAULT_IMAGE_MODEL } from 'src/shared/ai/ai.service';
import path from 'path';
import fs from 'fs/promises';
import { ImagePrompt, VideoPrompt } from 'specs/prompt';
import { extractText } from 'src/bot/extractors';
import { extractJson } from 'src/shared/format/helpers';
import { downloadImage } from 'src/shared/format/images';
import { downloadMedia, getFileMetadata } from 'src/shared/format/media';
import {contentGenerationPrompt} from 'src/prompts/contentGenerationPrompt';

@Injectable()
export class PostWorkflowService {
  constructor(
    public prisma: PrismaService,
    public aiService: AiService,
    public fileService: FileService
  ) {}

  async create(data: Prisma.PostCreateInput): Promise<Post> {
    return await this.prisma.post.create({
      data
    });
  }

  async get(where: Prisma.PostWhereUniqueInput): Promise<Post> {
    return await this.prisma.post.findUnique({ where });
  }

  async delete(where: Prisma.PostWhereUniqueInput): Promise<any> {
    try {
      return await this.prisma.post.delete({ where });
    } catch (e) {
      return false;
    }
  }

  async update(where: Prisma.PostWhereUniqueInput, params: Prisma.PostUpdateInput): Promise<Post> {
    return await this.prisma.post.update({ data: params, where });
  }

  async findAll(): Promise<Post[]> {
    return await this.prisma.post.findMany();
  }

  async find({ where, orderBy, skip = 0, take = 10 }: Prisma.PostFindManyArgs): Promise<Post[]> {
    return await this.prisma.post.findMany({ where, orderBy, skip, take });
  }

  async count(where: Prisma.PostWhereInput): Promise<number> {
    return await this.prisma.post.count({ where });
  }

 async generatePostFromPromptInput(inputs, taskId, workflowId): Promise<any> {

      // Default workflow step for postWorkflowService.generatePostFromPromptInput
      console.log('Executing postWorkflowService.generatePostFromPromptInput');
      return { success: true, message: 'Default step executed' };
    
}

 async storePost(inputs, taskId, workflowId): Promise<any> {

      // Default workflow step for postWorkflowService.storePost
      console.log('Executing postWorkflowService.storePost');
      return { success: true, message: 'Default step executed' };
    
}

}

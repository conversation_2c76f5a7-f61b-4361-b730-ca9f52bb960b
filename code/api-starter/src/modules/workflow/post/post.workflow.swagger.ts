
  import { ApiProperty } from '@nestjs/swagger';
  import { IsNotEmpty, IsString, IsOptional, IsNumber, IsObject, IsEnum, IsDateString } from 'class-validator';
  import { Prisma } from '@prisma/client';
export class FindPostParams {

      @ApiProperty({
        required: false,
        description: 'Order by field',
        enum: Object.keys(Prisma.PostScalarFieldEnum),
      })
      @IsEnum(Object.keys(Prisma.PostScalarFieldEnum))
      @IsOptional()
      sortField: string;

      @ApiProperty({
        required: false,
        description: 'Order sort',
        enum: Prisma.SortOrder,
      })
      @IsEnum(Prisma.SortOrder)
      @IsOptional()
      sortOrder: Prisma.SortOrder;

      @ApiProperty({
        required: false,
        description: 'Number of results to return',
        example: 10,
      })
      @IsOptional()
      @IsNumber()
      take: number;

      @ApiProperty({
        required: false,
        description: 'Number of results to skip',
        example: 0,
      })
      @IsOptional()
      @IsNumber()
      skip: number;
      }

export class ReadPostParams {
}

export class CreatePostParams {

      @ApiProperty({
        required: true,
        description: 'The main topic or category of the post.',
        example: 'mock.word',
      })
      @IsNotEmpty()

      @IsString()
      topic: string;
      }



        import {
          Body,
          Controller,
          Delete as NestDelete,
          Get as NestGet,
          NotFoundException,
          Param,
          Patch as NestPatch,
          Post as NestPost,
          UseGuards,
          UseInterceptors,
          UploadedFile,
        } from '@nestjs/common';
        import {
          ApiBearerAuth,
          ApiOperation,
          ApiResponse,
          ApiConsumes,
          ApiTags,
        } from '@nestjs/swagger';
        import { AccessTo, AccessCreditNeeded} from 'src/acl/acl.decorator';
        import { AccessGuard } from 'src/acl/acl.guard';
        import { Post, Prisma, User } from '@prisma/client';
        import { AuthGuard } from 'src/modules/auth/auth.guard';
        import { ReqUser } from 'src/modules/auth/auth.decorator';
        import * as story from 'specs/story/post';
        import {ApiFile} from 'src/shared/swagger/swagger.decorator';
        import {FileInterceptor} from '@nestjs/platform-express';
        import {Upload} from 'src/shared/file/file.interface';

        import {
  FindPostParams,
  CreatePostParams,
} from './post.workflow.swagger';

        
        import { AiService, DEFAULT_IMAGE_MODEL } from 'src/shared/ai/ai.service';
        import { extractJson } from 'src/shared/format/helpers';
        import { ProviderType } from 'src/shared/ai/ai.type';
        import { ConfigService } from 'src/shared/config/config.service';
        import { ImagePrompt, VideoPrompt } from 'specs/prompt';
        import { extractText } from 'src/bot/extractors';
        import path from 'path';
        import { downloadImage } from 'src/shared/format/images';
         import { downloadMedia, getFileMetadata } from 'src/shared/format/media';
        import {promises as fs} from 'fs';
        import { FileService } from 'src/shared/file/file.service';
        import {NotificationService} from 'src/shared/notification/notification.service';
        import Notifications from 'src/shared/notification/notifications';
        import {NOTIFICATION_CONFIG} from 'src/shared/config/notification.config';
        import moment from 'moment';

              
        
        import { PostWorkflowService } from './post.workflow.service';
        import { WorkflowService } from '../workflow.service';
              

        @Controller("workflow/post" )
        @ApiTags("workflow")
        @ApiBearerAuth()
        export class PostWorkflowController {
          constructor(
            
        private aiService: AiService,
        private config: ConfigService,
        public fileService: FileService,
        public notificationService: NotificationService,
      
            
        private postWorkflowService: PostWorkflowService,
        private workflowService: WorkflowService
      
          ) {}

          
    
    
      whereToPrisma(params): Prisma.PostWhereInput {
        let where: Prisma.PostWhereInput = {};
        const OR = [];
        
            if (params.topic) {
              OR.push({
                topic: {
                  contains: params.topic,
                  mode: "insensitive",
                },
              });
            }

        if (OR.length > 0) {
          where.OR = OR;
        }
        return where;
      }

      @NestPost('find')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.postToFind.access)
      @ApiOperation(story.postToFind.operation)
      @ApiResponse(story.postToFind.codes['201'].response)
      async find(@ReqUser() user: User,@Body() params: FindPostParams): Promise<Post[]> {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;

        const currentData = await this.postWorkflowService.find({
          where: this.whereToPrisma(where),
          orderBy: { [sortField]: sortOrder },
          take,
          skip,
        });

        if(currentData && currentData.length > 0){
        
        }
        return currentData;
      }

    
    
    

    
    
    
        @NestGet(':id')
        @UseGuards(AuthGuard, AccessGuard)
        @AccessTo(story.postToRead.access)
        @ApiOperation(story.postToRead.operation)
        @ApiResponse(story.postToRead.codes['200'].response)
        @ApiResponse(story.postToRead.codes['401'].response)
        @ApiResponse(story.postToRead.codes['404'].response)
        async read(@ReqUser() user: User, @Param('id') id: string): Promise<Post> {
          const currentData = await this.postWorkflowService.get({ id });
          if (!currentData) {
            throw new NotFoundException();
          }
          
          return currentData;
        }

    
    

    
      @NestPost('/create')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.postToCreate.access)
      @ApiOperation(story.postToCreate.operation)
      @ApiResponse(story.postToCreate.codes['201'].response)
      @ApiResponse(story.postToCreate.codes['401'].response)
      @AccessCreditNeeded("{}")
      async create(
        @ReqUser() user: User,
        @Body() post: CreatePostParams,
      ): Promise<string> {
        const workflow: any = await this.workflowService.create(
          user.id,
          user.id,
          'generatePost',
          { user , input:post, prompt: post.topic },
          [{"name":"generatePostService.generatePostFromPromptInput","uiDescription":"Generating your blog post with AI...","dependencies":[],"next":[{"name":"postWorkflowService.storePost","uiDescription":"Storing your generated blog post...","dependencies":["generatePostService.generatePostFromPromptInput"],"connect":["generatePostService.generatePostFromPromptInput"]}]}],
          {
            ownerId: user.id,
            actionType: 'generatePost',
            ownerType: 'post',
            objectName: 'post' + moment(),
          },
        );
        
    if (NOTIFICATION_CONFIG["workflowPostCreatePostWorkflowParams"]) {
        const userId =
        NOTIFICATION_CONFIG["workflowPostCreatePostWorkflowParams"]?.target === 'owner'
          ? workflow.ownerId
          : user.id;

        await this.notificationService.sendAndSave(
        [userId],
          Notifications.workflowPostCreatePostWorkflowParams(),
      );
    }
    
        return workflow.workflowId;
      }
    
    
    
    
    

        }

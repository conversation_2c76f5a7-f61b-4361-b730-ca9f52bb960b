
        import {
          Body,
          Controller,
          Delete as NestDelete,
          Get as NestGet,
          NotFoundException,
          Param,
          Patch as NestPatch,
          Post as NestPost,
          UseGuards,
          UseInterceptors,
          UploadedFile,
        } from '@nestjs/common';
        import {
          ApiBearerAuth,
          ApiOperation,
          ApiResponse,
          ApiConsumes,
          ApiTags,
        } from '@nestjs/swagger';
        import { AccessTo, AccessCreditNeeded} from 'src/acl/acl.decorator';
        import { AccessGuard } from 'src/acl/acl.guard';
        import { Find, Prisma, User } from '@prisma/client';
        import { AuthGuard } from 'src/modules/auth/auth.guard';
        import { ReqUser } from 'src/modules/auth/auth.decorator';
        import * as story from 'specs/story/find';
        import {ApiFile} from 'src/shared/swagger/swagger.decorator';
        import {FileInterceptor} from '@nestjs/platform-express';
        import {Upload} from 'src/shared/file/file.interface';

        import {
  FindFindParams,
} from './find.workflow.swagger';

        
        
        import { FindWorkflowService } from './find.workflow.service';
        import { WorkflowService } from '../workflow.service';
              

        @Controller("workflow/find" )
        @ApiTags("workflow")
        @ApiBearerAuth()
        export class FindWorkflowController {
          constructor(
            
            
        private findWorkflowService: FindWorkflowService,
        private workflowService: WorkflowService
      
          ) {}

          
    
    
      whereToPrisma(params): Prisma.FindWhereInput {
        let where: Prisma.FindWhereInput = {};
        const OR = [];
        
            if (params.objectId) {
              OR.push({
                objectId: {
                  contains: params.objectId,
                  mode: "insensitive",
                },
              });
            }

            if (params.objectType) {
              OR.push({
                objectType: {
                  contains: params.objectType,
                  mode: "insensitive",
                },
              });
            }

        if (OR.length > 0) {
          where.OR = OR;
        }
        return where;
      }

      @NestPost('find')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.findToFind.access)
      @ApiOperation(story.findToFind.operation)
      @ApiResponse(story.findToFind.codes['201'].response)
      async find(@ReqUser() user: User,@Body() params: FindFindParams): Promise<Find[]> {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;

        const currentData = await this.findWorkflowService.find({
          where: this.whereToPrisma(where),
          orderBy: { [sortField]: sortOrder },
          take,
          skip,
        });

        if(currentData && currentData.length > 0){
        
        }
        return currentData;
      }

    
    
    

        }

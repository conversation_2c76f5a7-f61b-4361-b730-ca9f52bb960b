
        import {
          Body,
          Controller,
          Delete as NestDelete,
          Get as NestGet,
          NotFoundException,
          Param,
          Patch as NestPatch,
          Post as NestPost,
          UseGuards,
          UseInterceptors,
          UploadedFile,
        } from '@nestjs/common';
        import {
          ApiBearerAuth,
          ApiOperation,
          ApiResponse,
          ApiConsumes,
          ApiTags,
        } from '@nestjs/swagger';
        import { AccessTo, AccessCreditNeeded} from 'src/acl/acl.decorator';
        import { AccessGuard } from 'src/acl/acl.guard';
        import { Create, Prisma, User } from '@prisma/client';
        import { AuthGuard } from 'src/modules/auth/auth.guard';
        import { ReqUser } from 'src/modules/auth/auth.decorator';
        import * as story from 'specs/story/create';
        import {ApiFile} from 'src/shared/swagger/swagger.decorator';
        import {FileInterceptor} from '@nestjs/platform-express';
        import {Upload} from 'src/shared/file/file.interface';

        import {
  CreateCreateParams,
} from './create.workflow.swagger';

        
        
        import { CreateWorkflowService } from './create.workflow.service';
        import { WorkflowService } from '../workflow.service';
              

        @Controller("workflow/create" )
        @ApiTags("workflow")
        @ApiBearerAuth()
        export class CreateWorkflowController {
          constructor(
            
            
        private createWorkflowService: CreateWorkflowService,
        private workflowService: WorkflowService
      
          ) {}

          
    
      @NestPost('/create')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.createToCreate.access)
      @ApiOperation(story.createToCreate.operation)
      @ApiResponse(story.createToCreate.codes['201'].response)
      @ApiResponse(story.createToCreate.codes['401'].response)
      
      async create(
        @ReqUser() user: User,
        @Body() create: CreateCreateParams,
      ): Promise<string> {
        const workflow: any = await this.workflowService.create(
          user.id,
          user.id,
          'generateCreate',
          { user , input:create, prompt: create.undefined },
          undefined,
          {
            ownerId: user.id,
            actionType: 'generateCreate',
            ownerType: 'create',
            objectName: 'create' + moment(),
          },
        );
        
      if (NOTIFICATION_CONFIG["commentsCreatePostWorkflowParams"]) {
      
      const userId =
      NOTIFICATION_CONFIG["commentsCreatePostWorkflowParams"]?.target === 'owner'
        ? currentData.userId
        : user.id;

      await this.notificationService.sendAndSave(
      [userId],
      Notifications.commentsCreatePostWorkflowParams(),
    );
 }
        return workflow.workflowId;
      }
    
    
    
    
    

        }

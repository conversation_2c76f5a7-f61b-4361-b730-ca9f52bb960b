import {Injectable} from '@nestjs/common';
import {PrismaService} from 'src/shared/prisma/prisma.service';
import {WorkflowStatus} from '@prisma/client';
import {WorkflowService} from './workflow.service';
import {ConfigService} from 'src/shared/config/config.service';
import {AiService} from 'src/shared/ai/ai.service';
import {BugsnagService} from 'src/shared/bugsnag/bugsnag.service';
import {ImageWorkflowService} from './image/image.workflow.service';
import {AudioWorkflowService} from './audio/audio.workflow.service';

import { PostWorkflowService } from './postWorkflow/postWorkflowService.service';

@Injectable()
export class WorkerService {
  constructor(
public prisma: PrismaService,
public config: ConfigService,
public bugsnag: BugsnagService,
private aiService: AiService,
private imageWorkflowService: ImageWorkflowService,
private audioWorkflowService: AudioWorkflowService,
private postWorkflowService: PostWorkflowService
) {}

  public async exec(taskName, workflow: WorkflowService) {
    console.log('exec', taskName);

    let task = await workflow.requestTask({
      taskName,
      podId: process.env.POD_NAME || taskName,
    });

    if (!task) {
      console.log('No task found');
      return false;
    }

    try {
      await workflow.updateTaskStatus(task.id, WorkflowStatus.IN_PROGRESS);

      // (task.inputs as any).projectId = task.projectId;

      let result;

      if (await this[taskName]) {
        result = await this[taskName](task.inputs, task.id, task.workflowId);
      } else if (taskName.indexOf('.') > 0) {
        let [serviceName, methodName] = taskName.split('.');
        result = await this[serviceName][methodName](
          task.inputs,
          task.id,
          task.workflowId,
        );
      }

      await workflow.updateTaskStatus(
        task.id,
        WorkflowStatus.COMPLETED,
        result,
      );
    } catch (e) {
      e.context = {task, taskName};
      console.error(e);
      this.bugsnag.client.notify(e);
      if (task.nbOfExecution > 2) {
        await this.prisma.task.update({
          where: {id: task.id},
          data: {
            status: WorkflowStatus.ERROR,
            nbOfExecution: {
              increment: 1,
            },
          },
        });
        await workflow.updateTaskStatus(task.id, WorkflowStatus.ERROR, null, {
          message: e.message,
          stack: e.stack,
        });
      } else {
        await this.prisma.task.update({
          where: {id: task.id},
          data: {
            status: WorkflowStatus.PENDING,
            nbOfExecution: {
              increment: 1,
            },
          },
        });
      }

      await workflow.updateTaskStatus(
        task.id,
        WorkflowStatus.ERROR,
        null,
        e.message,
      );
    }
  }

  async start(inputs: any) {
    //   const ai = new AiService('openai', this.config);

    // const result = await ai.send('gpt-3.5-turbo', [
    //   {
    //     role: 'user',
    //     content: prompt,
    //   },
    // ]);

    // return {result};
    // console.log('start', inputs);

    return {result: 'start result'};
  }

  async edit(inputs: any) {
    //  console.log('edit', inputs);
    return {result: 'edit result'};
  }
  async end(inputs: any) {
    // console.log('end', inputs);
    return {result: 'end result'};
  }
}

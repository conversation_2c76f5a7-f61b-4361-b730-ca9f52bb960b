
        import {
          Body,
          Controller,
          Delete as NestDelete,
          Get as NestGet,
          NotFoundException,
          Param,
          Patch as NestPatch,
          Post as NestPost,
          UseGuards,
          UseInterceptors,
          UploadedFile,
        } from '@nestjs/common';
        import {
          ApiBearerAuth,
          ApiOperation,
          ApiResponse,
          ApiConsumes,
          ApiTags,
        } from '@nestjs/swagger';
        import { AccessTo, AccessCreditNeeded} from 'src/acl/acl.decorator';
        import { AccessGuard } from 'src/acl/acl.guard';
        import { :id, Prisma, User } from '@prisma/client';
        import { AuthGuard } from 'src/modules/auth/auth.guard';
        import { ReqUser } from 'src/modules/auth/auth.decorator';
        import * as story from 'specs/story/:id';
        import {ApiFile} from 'src/shared/swagger/swagger.decorator';
        import {FileInterceptor} from '@nestjs/platform-express';
        import {Upload} from 'src/shared/file/file.interface';

        
        
        
        import { :idWorkflowService } from './:id.workflow.service';
        import { WorkflowService } from '../workflow.service';
              

        @Controller("workflow/:id" )
        @ApiTags("workflow")
        @ApiBearerAuth()
        export class :idWorkflowController {
          constructor(
            
            
        private :idWorkflowService: :idWorkflowService,
        private workflowService: WorkflowService
      
          ) {}

          
    
    
    
        @NestGet(':id')
        @UseGuards(AuthGuard, AccessGuard)
        @AccessTo(story.:idToRead.access)
        @ApiOperation(story.:idToRead.operation)
        @ApiResponse(story.:idToRead.codes['200'].response)
        @ApiResponse(story.:idToRead.codes['401'].response)
        @ApiResponse(story.:idToRead.codes['404'].response)
        async read(@ReqUser() user: User, @Param('id') id: string): Promise<:id> {
          const currentData = await this.:idWorkflowService.get({ id });
          if (!currentData) {
            throw new NotFoundException();
          }
          
          return currentData;
        }

    
    

        }

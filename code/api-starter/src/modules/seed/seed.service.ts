import {ContentService} from './../content/content.service';
import { FindWorkflowController } from 'src/modules/workflow/find/find.workflow.controller';
import { PostWorkflowController } from 'src/modules/workflow/post/post.workflow.controller';
import { :idWorkflowController } from 'src/modules/workflow/:id/:id.workflow.controller';
import { CreateWorkflowController } from 'src/modules/workflow/create/create.workflow.controller';
import {ContentController} from 'src/modules/content/content.controller';
import {AdminController} from 'src/modules/admin/admin.controller';
import {forwardRef, Inject, Injectable, OnModuleInit} from '@nestjs/common';
import {Timeout} from '@nestjs/schedule';
import {demoSeedConfig} from 'specs/demo';
import {testSeedConfig} from 'specs/seed';
import {AdminService} from 'src/modules/admin/admin.service';
import {AuthAdminController} from 'src/modules/auth/admin.auth.controller';
import {AuthUserService} from 'src/modules/auth/auth.user.service';
import {AuthUserController} from 'src/modules/auth/user.auth.controller';
import {CommentController} from 'src/modules/comment/comment.controller';
import {ExempleController} from 'src/modules/exemple/exemple.controller';
import {ExempleService} from 'src/modules/exemple/exemple.service';
import {LikeController} from 'src/modules/like/like.controller';
import {LikeService} from 'src/modules/like/like.service';
import {NewsController} from 'src/modules/news/news.controller';
import {NewService} from 'src/modules/news/news.service';
import {RatingController} from 'src/modules/rating/rating.controller';
import {RatingService} from 'src/modules/rating/rating.service';
import {ReportController} from 'src/modules/report/report.controller';
import {UserService} from 'src/modules/user/user.service';
import {ConfigService} from 'src/shared/config/config.service';
import {SeedMap} from 'src/modules/seed/seed.map';
import {
  isProductionEnv,
  isSeedEnv,
  isSeedNeededEnv,
  isStagingEnv,
  isLocalEnv,
  isDebugEnv,
} from 'src/shared/env/env.utils';
import {PrismaService} from 'src/shared/prisma/prisma.service';
import seedData, {
  isSeedReady,
  seedReady,
  setData,
} from 'src/shared/seed/seed.data';
import {formatParams} from 'src/shared/seed/seed.utils';
import {AuthEmailService} from '../auth/passport/email/auth.email.service';
import {FieldController} from '../field/field.controller';
import {HighlightController} from '../highlight/highlight.controller';
import {HomeController} from '../home/<USER>';
import {ProfileController} from '../profile/profile.controller';
import {AnalyticService} from '../analytic/analytic.service';
import {AnalyticController} from '../analytic/analytic.controller';
import {CronController} from '../cron/cron.controller';
import {IapController} from '../iap/iap.controller';
import {UserController} from '../user/user.controller';
import {HomeService} from '../home/<USER>';
import {AccountController} from '../account/account.controller';
import {exit} from 'process';
import {WorkflowController} from '../workflow/workflow.controller';
import {AgentController} from '../chat/agent.controller';

@Injectable()
export class SeedService {
  seedMap: SeedMap;

  configs = isStagingEnv() ? demoSeedConfig : testSeedConfig;

  constructor(
    private readonly findWorkflowController: FindWorkflowController,
    private readonly postWorkflowController: PostWorkflowController,
    private readonly :idWorkflowController: :idWorkflowController,
    private readonly createWorkflowController: CreateWorkflowController,
    private readonly configService: ConfigService,

    private readonly exempleService: ExempleService,
    private readonly exempleController: ExempleController,
    private readonly userService: UserService,
    private readonly adminService: AdminService,
    private readonly authUserService: AuthUserService,
    private readonly authEmailService: AuthEmailService,
    private readonly authUserController: AuthUserController,
    private readonly authAdminController: AuthAdminController,
    private readonly adminController: AdminController,

    private readonly contentService: ContentService,
    private readonly contentController: ContentController,

    private readonly homeController: HomeController,
    private readonly commentController: CommentController,
    private readonly reportController: ReportController,
    private readonly ratingService: RatingService,
    private readonly ratingController: RatingController,
    private readonly likeService: LikeService,
    private readonly likeController: LikeController,
    private readonly profileController: ProfileController,
    private readonly newsController: NewsController,
    private readonly newService: NewService,
    private readonly highlightController: HighlightController,

    private readonly analyticService: AnalyticService,
    private readonly analyticController: AnalyticController,
    private readonly homeService: HomeService,
    private readonly userController: UserController,

    private readonly fieldController: FieldController,
    private readonly iapController: IapController,
    private readonly prisma: PrismaService,
    private readonly cronController: CronController,
    private readonly accountController: AccountController,
    private readonly workflowController: WorkflowController,
    private readonly agentController: AgentController,
  ) {
    this.seedMap = new SeedMap();
  }

  public async init() {
    console.log('SeedService init');
    if (!isSeedReady()) {
      if (!isProductionEnv()) {
        await this.seedMap.init(this.seed.bind(this)); // Use SeedMap's logic here
      } else {
        const numberofAdmins = await this.prisma.admin.count();

        console.log('numberofAdmins', numberofAdmins);
        if (numberofAdmins == 0) {
          await this.authAdminController.signUp({
            name: 'Admin',
            password: this.configService.get('ADMIN_PASSWORD'),
            email: this.configService.get('ADMIN_EMAIL'),
          });

          console.log('admin created');
        }
      }
    }

    await seedReady();

    return true;
  }

  public async seed(name, config) {
    try {
      let params = null;
      let result: any = null;
      if (!isProductionEnv()) {
        //  console.log(name + ' => ', {config});
      }
      for (const y in config) {
        //
        //await new Promise(done => setTimeout(done, 100));

        if (!isProductionEnv()) {
          //  console.log(name + ' - ', config[y]);
        }
        const seed = await this.story(config[y], result);
        if (params == null) {
          params = seed.params;
        }
        result = seed.result;

        if (seedData[name] === undefined) {
          setData(name, {params: seed.params, result});
          if (!isProductionEnv()) {
            //  console.log(name + ' = ', result);
          }
        }

        if (config[y].saveAs !== undefined) {
          if (seedData[config[y].saveAs] === undefined) {
            if (!isProductionEnv()) {
              //  console.log('saving : ' + config[y].saveAs);
            }
            setData(config[y].saveAs, {params: seed.params, result});
          } else {
            throw new Error(
              config[y].saveAs +
                ' seed already exists, change saveAs to a different value',
            );
          }
        }
      }

      return {params, result};
    } catch (e) {
      console.error(e);
      if (!isProductionEnv()) {
        exit(0);
      }
    }
  }

  public getProvider(provider) {
    return provider.charAt(0).toLowerCase() + provider.slice(1);
  }

  public async story(
    {provider, action, params},
    lastResult = {},
  ): Promise<{result: any; params: any}> {
    if (!provider) {
      console.log({provider, action, params});
    }
    //  console.log({provider, action, params});

    const storyParams = formatParams(params, {}, lastResult);
    const providerName = this.getProvider(provider);
    if (this[providerName] !== undefined) {
      if (this[providerName][action] !== undefined) {
        if (isSeedEnv()) {
          // console.log(`story :  [${providerName}][${action}] `);
          // console.log(`params : `, storyParams);
        }

        // eslint-disable-next-line prefer-spread
        const result = await this[providerName][action].apply(
          this[providerName],
          storyParams,
        );

        let isInProgress = true;

        console.log({result});
        if (result.workflowId) {
          while (isInProgress) {
            const workflow = await this.prisma.workflow.findUnique({
              where: {id: result.workflowId},
              include: {
                tasks: true,
              },
            });
            console.log({workflow});
            const tasksInProg = workflow.tasks.filter(
              task => task.status === 'IN_PROGRESS',
            );
            console.log({tasksInProg});
            if (workflow.status === 'COMPLETED' && tasksInProg.length === 0) {
              isInProgress = false;
            } else if (workflow.status === 'ERROR') {
              isInProgress = false;
            }
            await new Promise(done => setTimeout(done, 10000));
          }
        }

        console.log(`result : `, result);

        return {
          result,
          params: storyParams,
        };
      } else {
        throw new Error(
          `Can't load action  : [${providerName}][${action}] - add  private readonly ${providerName}: ${provider}, to src/modules/seed/seed.service.ts`,
        );
      }
    } else {
      throw new Error(
        `Can't load provider  :[${providerName}] - add private readonly ${providerName}: ${provider}, to src/modules/seed/seed.service.ts`,
      );
    }
  }

  public async create(name): Promise<any> {
    let params = null;
    let result = null;

    testSeedConfig[name].map(async config => {
      const story = await this.story(config);

      if (params === null) params = story.params;
      result = story.result;
    });

    return {
      result,
      params,
    };
  }
}

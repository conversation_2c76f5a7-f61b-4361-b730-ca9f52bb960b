export const NOTIFICATION_CONFIG = {
  "workflowPostCreatePostWorkflowParams": {
    "requestId": "createPost_request_1",
    "requestPath": "/workflow/post/create",
    "requestType": "POST",
    "useWorkflow": true,
    "dynamicRedirectionParams": [
      "id"
    ],
    "link": "/workflow/:id",
    "target": "me",
    "title": "Post Generation Initiated",
    "message": "Your blog post is being generated by AI. We'll notify you when it's ready!"
  },
  "commentCreatePostParams": {
    "requestId": "createComment_request_3",
    "requestPath": "/comment/create",
    "requestType": "POST",
    "useWorkflow": false,
    "dynamicRedirectionParams": [
      "id"
    ],
    "link": "/posts/:id",
    "target": "owner",
    "title": "New Comment on Your Post",
    "message": "A new comment has been added to your post."
  }
}

      export type PaymentType = 'subscription' | 'one-time-payment' | 'credit' | 'cart';
      export const PAYMENT_TYPES: PaymentType[] = ['subscription', 'one-time-payment', 'credit', 'cart'];
      export const PLAN_CONFIG = {
  "paymentType": "credit" as PaymentType,
  "paymentEntity": "post",
  "options": {
    "smallCreditPack": {
      "id": "smallCreditPack",
      "name": "Small Credit Pack",
      "description": "Get 50 credits to unlock 5 posts.",
      "price": 5,
      "currency": "usd",
      "interval": "one-time",
      "credits": 50,
      "buttonLabel": "Buy 50 Credits"
    },
    "mediumCreditPack": {
      "id": "mediumCreditPack",
      "name": "Medium Credit Pack",
      "description": "Get 120 credits to unlock 12 posts.",
      "price": 10,
      "currency": "usd",
      "interval": "one-time",
      "credits": 120,
      "buttonLabel": "Buy 120 Credits"
    },
    "largeCreditPack": {
      "id": "largeCreditPack",
      "name": "Large Credit Pack",
      "description": "Get 300 credits to unlock 30 posts.",
      "price": 20,
      "currency": "usd",
      "interval": "one-time",
      "credits": 300,
      "buttonLabel": "Buy 300 Credits"
    }
  },
  "creditNeeded": 10,
  "currency": "usd"
};
    
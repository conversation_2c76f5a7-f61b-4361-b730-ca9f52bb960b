const Notification = {
  
    "workflowPostCreatePostWorkflowParams": (id) => ({
        link: `/posts/${id}`,
        title: {
            fr: "Génération de l'article",
            en: "Post Generation"
        },
        message: {
            fr: "Votre brouillon d'article de blog est en cours de génération.",
            en: "Your blog post draft is being generated."
        },
        type: "email"
    })
,
  userLike: {
    link: '/profile',
    message: undefined,
    title: {
      fr: 'Vous avez de nouveaux likes sur votre profile',
      en: 'you have received a news like on your profile',
    },
    type: 'info',
  },
  commentAnswer: commentId => ({
    link: `/comment/${commentId}`,
    message: undefined,
    title: {
      fr: 'Vous avez recue une réponse a votre commentaire',
      en: 'you have received an answer on your comment',
    },
    type: 'info',
  }),
};

export default Notification;

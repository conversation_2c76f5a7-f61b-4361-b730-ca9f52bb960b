const Notification = {
  
    "workflowPostCreatePostWorkflowParams": (id) => ({
        link: `/workflow/${id}`,
        title: {
            fr: "Génération de publication initiée",
            en: "Post Generation Initiated"
        },
        message: {
            fr: "Votre article de blog est en cours de génération par l'IA. Nous vous informerons lorsqu'il sera prêt !",
            en: "Your blog post is being generated by AI. We'll notify you when it's ready!"
        },
        type: "email"
    }),
    "commentCreatePostParams": (id) => ({
        link: `/posts/${id}`,
        title: {
            fr: "Nouveau commentaire sur votre publication",
            en: "New Comment on Your Post"
        },
        message: {
            fr: "Un nouveau commentaire a été ajouté à votre publication.",
            en: "A new comment has been added to your post."
        },
        type: "email"
    })
,
  
    "workflowPostCreatePostWorkflowParams": (id) => ({
        link: `/posts/${id}`,
        title: {
            fr: "Génération de l'article",
            en: "Post Generation"
        },
        message: {
            fr: "Votre brouillon d'article de blog est en cours de génération.",
            en: "Your blog post draft is being generated."
        },
        type: "email"
    })
,
  userLike: {
    link: '/profile',
    message: undefined,
    title: {
      fr: 'Vous avez de nouveaux likes sur votre profile',
      en: 'you have received a news like on your profile',
    },
    type: 'info',
  },
  commentAnswer: commentId => ({
    link: `/comment/${commentId}`,
    message: undefined,
    title: {
      fr: 'Vous avez recue une réponse a votre commentaire',
      en: 'you have received an answer on your comment',
    },
    type: 'info',
  }),
};

export default Notification;

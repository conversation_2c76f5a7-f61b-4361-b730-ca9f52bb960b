import {Seed} from 'specs/specs.interface';

import {userAlreadyRegistered} from 'specs/seed/auth/user/userAlreadyRegistered';
import { findToFind } from 'specs/seed/find/findToFind';
import { :idToRead } from 'specs/seed/:id/:idToRead';
import { :idTo } from 'specs/seed/:id/:idTo';
import { postToFind } from 'specs/seed/post/postToFind';
import { postTo } from 'specs/seed/post/postTo';

import {user} from 'specs/seed/auth/user/user';
import {test} from 'specs/seed/auth/user/test';
import {userPasswordForgot} from 'specs/seed/auth/user/userPasswordForgot';
import {userWantingToChangePassword} from 'specs/seed/auth/user/userWantingToChangePassword';

import {admin} from 'specs/seed/auth/admin/admin';
import {adminPasswordForgot} from 'specs/seed/auth/admin/adminPasswordForgot';
import {adminWantingToChangePassword} from 'specs/seed/auth/admin/adminWantingToChangePassword';
import {adminToUpdate} from 'specs/seed/admin/adminToUpdate';
import {adminToDelete} from 'specs/seed/admin/adminToDelete';
import {adminToFind1} from 'specs/seed/admin/adminToFind1';
import {adminToFind2} from 'specs/seed/admin/adminToFind2';
import {adminToRead} from 'specs/seed/admin/adminToRead';

import {exempleToUpdate} from 'specs/seed/exemple/exempleToUpdate';
import {exempleToDelete} from 'specs/seed/exemple/exempleToDelete';
import {exempleToFind1} from 'specs/seed/exemple/exempleToFind1';
import {exempleToFind2} from 'specs/seed/exemple/exempleToFind2';
import {exempleToRead} from 'specs/seed/exemple/exempleToRead';

import {homeToUpdate} from 'specs/seed/home/<USER>';
import {homeToDelete} from 'specs/seed/home/<USER>';
import {homeToList} from 'specs/seed/home/<USER>';
import {homeToRead} from 'specs/seed/home/<USER>';

import {highlightToUpdate} from 'specs/seed/highlight/highlightToUpdate';
import {highlightToDelete} from 'specs/seed/highlight/highlightToDelete';
import {highlightToList} from 'specs/seed/highlight/highlightToList';
import {highlightToRead} from 'specs/seed/highlight/highlightToRead';

import {fieldDynamic} from 'specs/seed/field';
import {ratingOnFormation} from 'specs/seed/rating/ratingOnFormation';
import {ratingToRead} from 'specs/seed/rating/ratingToRead';
import {commentToDelete} from 'specs/seed/comment/commentToDelete';
import {commentToFind1} from 'specs/seed/comment/commentToFind1';
import {commentToFind2} from 'specs/seed/comment/commentToFind2';
import {commentToRead} from 'specs/seed/comment/commentToRead';
import {commentToUpdate} from 'specs/seed/comment/commentToUpdate';
import {commentToUpdateByAdmin} from 'specs/seed/comment/commentToUpdateByAdmin';
import {commentToDeleteByAdmin} from 'specs/seed/comment/commentToDeleteByAdmin';
import {commentToReport} from 'specs/seed/comment/commentToReport';

import {likeOnNews} from 'specs/seed/like/likeOnNews';
import {likeOnProfile} from 'specs/seed/like/likeOnProfile';
import {commentDemo} from 'specs/demo/comment.demo';
import {dynamicFieldToRead} from 'specs/seed/field/dynamicFieldToRead';
import {dynamicFieldToDelete} from 'specs/seed/field/dynamicFieldToDelete';
import {newsToDelete} from 'specs/seed/news/newsToDelete';
import {newsToFind} from 'specs/seed/news/newsToFind';
import {newsToRead} from 'specs/seed/news/newsToRead';
import {newsToUpdate} from 'specs/seed/news/newsToUpdate';
import {profileToRead} from 'specs/seed/profile/profileToRead';
import {profileToUpdate} from 'specs/seed/profile/profileToUpdate';

import {contentToUpdate} from 'specs/seed/content/contentToUpdate';
import {contentToDelete} from 'specs/seed/content/contentToDelete';
import {contentToFind1} from 'specs/seed/content/contentToFind1';
import {contentToFind2} from 'specs/seed/content/contentToFind2';
import {contentToRead} from 'specs/seed/content/contentToRead';

import {newsToShowOnHome} from './news/newsToShowOnHome';

import {analyticToUpdate} from 'specs/seed/analytic/analyticToUpdate';
import {analyticToRead} from 'specs/seed/analytic/analyticToRead';
import {analyticToFind1} from 'specs/seed/analytic/analyticToFind1';
import {analyticToFind2} from 'specs/seed/analytic/analyticToFind2';
import {analyticToDelete} from 'specs/seed/analytic/analyticToDelete';
import {adminUserDisplayedCount} from 'specs/seed/admin/adminUserDisplayedCount';
import {notificationToRead} from 'specs/seed/notification/notificationToRead';
import {creatorEvent} from 'specs/seed/auth/user/creatorEvent';
import {ratingOnFormationModule} from 'specs/seed/rating/ratingOnFormationModule';
import {agentToCreate} from 'specs/seed/agent/agentToCreate';
import {usersForDataTable} from 'specs/seed/user/usersForDataTable';
// import {projectToRead} from './project/projectToRead';

export const testSeedConfig: Seed = {
    findToFind,
    :idToRead,
    :idTo,
    postToFind,
    postTo,
  dynamicFieldToRead,
  dynamicFieldToDelete,
  fieldDynamic,
  user,
  creatorEvent,
  admin,
  test,
  userPasswordForgot,
  userWantingToChangePassword,
  userAlreadyRegistered,
  profileToRead,
  profileToUpdate,
  adminPasswordForgot,
  adminWantingToChangePassword,
  adminToUpdate,
  adminToRead,
  adminToDelete,
  adminToFind1,
  adminToFind2,
  exempleToUpdate,
  exempleToRead,
  exempleToDelete,
  exempleToFind1,
  exempleToFind2,
  adminUserDisplayedCount,
  newsToShowOnHome,

  likeOnNews,
  likeOnProfile,

  ratingOnFormation,
  ratingToRead,
  ratingOnFormationModule,
  newsToDelete,
  newsToFind,
  newsToRead,
  newsToUpdate,

  contentToUpdate,
  contentToRead,
  contentToDelete,
  contentToFind1,
  contentToFind2,

  commentToDelete,
  commentToFind1,
  commentToFind2,
  commentToRead,
  commentToUpdate,
  commentToUpdateByAdmin,
  commentToDeleteByAdmin,
  commentToReport,

  homeToUpdate,
  homeToDelete,
  homeToList,
  homeToRead,

  highlightToUpdate,
  highlightToDelete,
  highlightToList,
  highlightToRead,

  analyticToDelete,
  analyticToFind1,
  analyticToFind2,
  analyticToRead,
  analyticToUpdate,

  notificationToRead,
  // projectToRead,

  // Agent seeds
  agentToCreate,

  // Data Table Example seeds
  usersForDataTable,
};

export default testSeedConfig;


import { StorySpec } from 'specs/specs.interface';

export const findToFind: StorySpec = {
  filePath: __filename,
  route: '/comment/find',
  method: 'POST',
  operation: {
    summary: 'As a user, I want to find a find',
  },
  access: {
    resource: 'find',
    action: 'read',
    owner: false
  },
  codes: {
        '201': {
          response: {
            status: 201,
            description: 'find found',
          },
          story: {
        auth: "user",
        body: {"objectId":"seed.findToFind.result.objectId","objectType":"seed.findToFind.result.objectType"}
      },
          tests: [{"type":"equal","data":{"length":6}}],
        },
        '401': {
          response: {
            status: 401,
            description: 'Incorrect credentials',
          },
        },
              '404': {
          response: {
            status: 404,
            description: "find doesn't exist",
          },
        },
      },
};

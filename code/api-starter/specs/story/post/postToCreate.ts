
import { StorySpec } from 'specs/specs.interface';

export const postToCreate: StorySpec = {
  filePath: __filename,
  route: '/workflow/post',
  method: 'POST',
  operation: {
    summary: 'As a user, I want to create a post',
  },
  access: {
    resource: 'post',
    action: 'create',
    owner: false
  },
  codes: {
        '201': {
          response: {
            status: 201,
            description: 'post created',
          },
          story: {
        auth: "user",
        body: {"topic":"mock.word"}
      },
          tests: [{"type":"equal","data":{"topic":"params.topic"}}],
        },
        '401': {
          response: {
            status: 401,
            description: 'Incorrect credentials',
          },
        },
        
      },
};

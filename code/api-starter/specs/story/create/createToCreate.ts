
import { StorySpec } from 'specs/specs.interface';

export const createToCreate: StorySpec = {
  filePath: __filename,
  route: '/comment',
  method: 'POST',
  operation: {
    summary: 'As a user, I want to create a create',
  },
  access: {
    resource: 'create',
    action: 'create',
    owner: false
  },
  codes: {
        '201': {
          response: {
            status: 201,
            description: 'create created',
          },
          story: {
        auth: "user",
        body: {"content":"string","objectId":"string","objectType":"string"}
      },
          tests: [{"type":"equal","data":{"content":"params.content"}}],
        },
        '401': {
          response: {
            status: 401,
            description: 'Incorrect credentials',
          },
        },
        
      },
};

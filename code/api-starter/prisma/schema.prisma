datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "darwin", "debian-openssl-1.1.x", "linux-arm64-openssl-1.1.x", "debian-openssl-3.0.x"]
  // previewFeatures = ["fullTextSearchPostgres"]
}

model Agent {
  id            String             @id @default(cuid()) @db.VarChar(36)
  userId        String             @db.VarChar(36)
  user          User               @relation("AgentUser", fields: [userId], references: [id])
  name          String?            @db.VarChar(255)
  description   String?            @db.Text
  systemPrompt  String             @db.Text
  starters      Json?
  conversations ChatConversation[] @relation("AgentConversations")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ChatConversation {
  id        String                 @id @default(cuid()) @db.<PERSON>ar<PERSON><PERSON>(36)
  userId    String                 @db.Var<PERSON>har(36)
  user      User                   @relation("ChatConversationUser", fields: [userId], references: [id])
  title     String?                @db.VarChar(255)
  prompt    String                 @db.VarChar(500)
  status    ChatConversationStatus @default(active)
  messages  ChatMessage[]          @relation("ConversationMessages")
  agentId   String                 @db.VarChar(36)
  agent     Agent                  @relation("AgentConversations", fields: [agentId], references: [id])
  createdAt DateTime               @default(now())
  updatedAt DateTime               @updatedAt

  @@index([userId, status])
  @@index([userId])
  @@index([agentId])
}

model ChatMessage {
  id     String @id @default(cuid()) @db.VarChar(36)
  userId String @db.VarChar(36)
  user   User   @relation("ChatMessageUser", fields: [userId], references: [id])

  role        ChatMessageRole
  messageType ChatMessageType @default(text)
  content     String          @db.Text
  metadata    Json? // Additional data like tokens, model used, etc.

  conversationId String           @db.VarChar(36)
  conversation   ChatConversation @relation("ConversationMessages", fields: [conversationId], references: [id], onDelete: Cascade)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  @@index([userId, conversationId])
  @@index([conversationId])
}

model Workflow {
  id         String         @id @default(uuid()) @db.Uuid
  name       String         @db.VarChar(255)
  inputs     Json
  steps      Json
  skill      Skill          @default(project)
  step       Step?          @relation("StepWorkflow")
  ownerId    String         @db.VarChar(36)
  ownerType  OwnerType
  actionType String         @db.VarChar(50)
  objectName String         @db.VarChar(255)
  status     WorkflowStatus @default(PENDING)
  tasks      Task[]         @relation("WorkflowTask")
  items      WorkflowItem[] @relation("WorkflowItems")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([skill, ownerId, ownerType])
}

model WorkflowItem {
  ownerType  String
  ownerId    String  @db.VarChar(36)
  name       String? @db.VarChar(255)
  parentType String? @db.VarChar(50)
  parentId   String? @db.VarChar(36)
  skill      Skill   @default(project)

  workflowId String   @db.Uuid
  workflow   Workflow @relation("WorkflowItems", fields: [workflowId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([ownerType, ownerId])
  @@index([skill, workflowId])
  @@index([parentId, parentType])
}

model Action {
  id           String          @id @default(cuid()) @db.VarChar(36)
  name         String          @db.VarChar(255)
  type         String          @db.VarChar(50)
  skill        Skill           @default(project)
  ownerId      String          @db.VarChar(36)
  ownerType    OwnerType
  objectName   String?         @db.VarChar(255)
  status       WorkflowStatus? @default(PENDING)
  result       Json? // JSON field to store the final result of the action
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  requirements Requirement[]   @relation("ActionRequirements")

  @@index([skill, ownerId, ownerType])
}

model Requirement {
  id       String          @id @default(cuid()) @db.VarChar(36)
  name     String          @db.VarChar(255) // e.g., "Create a Grant"
  status   WorkflowStatus? @default(PENDING) // Overall status of the requirement
  order    Int // Defines the order in which requirements should be executed
  action   Action          @relation("ActionRequirements", fields: [actionId], references: [id], onDelete: Cascade)
  actionId String          @db.VarChar(36) // Foreign key linking to the Action by id
  steps    Step[]          @relation("RequirementSteps")
}

model Step {
  id            String           @id @default(cuid()) @db.VarChar(36)
  name          String           @db.VarChar(255) // Name of the step (e.g., "Create Grant", "Upload Documents")
  order         Int // Order in which steps should be executed
  status        WorkflowStatus?  @default(PENDING) // Status of the step (e.g., "PENDING", "COMPLETED")
  requirement   Requirement      @relation("RequirementSteps", fields: [requirementId], references: [id], onDelete: Cascade)
  requirementId String           @db.VarChar(36) // Foreign key linking to the requirement
  type          ActionStepStatus
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  // Foreign key linking to the Workflow
  workflowId    String?          @unique @db.Uuid // Ensure this is unique, as it's a one-to-one relation
  workflow      Workflow?        @relation("StepWorkflow", fields: [workflowId], references: [id], onDelete: Cascade)
}

model Prompt {
  id      String @id @default(cuid()) @db.VarChar(36)
  name    String @db.VarChar(255)
  content String @db.Text
  result  String @db.Text

  ownerType String?
  ownerId   String? @db.VarChar(36)

  createdAt DateTime @default(now())
}

model Exemple {
  id       String        @id @default(cuid()) @db.VarChar(36)
  userId   String        @db.VarChar(36)
  name     String        @db.VarChar(255)
  imageUrl String?       @db.VarChar(500)
  image    Json?
  files    ExempleFile[] @relation("FileExemple")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model User {
  id                String             @id @default(cuid()) @db.VarChar(36)
  name              String             @db.VarChar(250)
  blocked           Boolean            @default(false)
  inactive          Boolean            @default(false)
  role              UserRole           @default(anonymous)
  language          Locale?            @default(fr)
  imageUrl          String?            @db.VarChar(500)
  image             Json?
  account           Account?           @relation("AccountUser")
  profile           Profile?           @relation("ProfileUser")
  devices           Device[]           @relation("DeviceUser")
  chatConversations ChatConversation[] @relation("ChatConversationUser")
  chatMessages      ChatMessage[]      @relation("ChatMessageUser")
  agents            Agent[]            @relation("AgentUser")
  posts             Post[]             @relation("UserPosts")

  textModel    String @default("gemini-2.0-flash") @db.VarChar(50)
  textProvider String @default("google") @db.VarChar(50)
  textTemp     Float  @default(1)

  audioModel    String @default("gpt-4o-realtime-preview") @db.VarChar(50)
  audioProvider String @default("openai") @db.VarChar(50)
  audioTemp     Float  @default(1)

  imageModel    String @default("black-forest-labs/flux-schnell") @db.VarChar(50)
  imageProvider String @default("replicate") @db.VarChar(50)
  imageTemp     Float  @default(1)

  videoModel    String @default("wavespeedai/wan-2.1-t2v-480p") @db.VarChar(50)
  videoProvider String @default("replicate") @db.VarChar(50)
  videoTemp     Float  @default(1)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Payment {
  id        String   @id @default(cuid()) @db.VarChar(36)
  date      DateTime
  accountId String   @db.VarChar(36)
  account   Account  @relation("AccountPayment", fields: [accountId], references: [userId])
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Subscription {
  id                 String   @id @default(cuid()) @db.VarChar(36)
  userId             String   @db.VarChar(36)
  environment        String   @db.VarChar(100)
  origTxId           String   @unique @db.VarChar(100)
  latestReceipt      String   @db.Text
  startDate          DateTime @default(now())
  endDate            DateTime @default(now())
  app                String   @db.VarChar(100)
  productId          String   @db.VarChar(36)
  isCancelled        Boolean  @default(false)
  validationResponse Json
  fake               Boolean  @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserApiLimit {
  id        String   @id @default(cuid()) @db.VarChar(36)
  userId    String   @unique @db.VarChar(36)
  count     Int      @default(0)
  createdAt DateTime @default(now())
  updateAt  DateTime @updatedAt
}

model Item {
  id       String @id @default(cuid()) @db.VarChar(36)
  itemType String @db.VarChar(50)

  ownerId   String @db.VarChar(36)
  ownerType String

  taskId String? @db.VarChar(36)

  name     String?       @db.VarChar(100)
  summary  String?       @db.Text
  skill    Skill?
  order    String[]      @db.VarChar(500)
  booleans ItemBoolean[] @relation("ItemBooleans")
  texts    ItemText[]    @relation("ItemTexts")
  jsons    ItemJson[]    @relation("ItemJsons")
  fields   ItemField[]   @relation("ItemFields")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  children Item[]  @relation("ItemParent")
  parent   Item?   @relation("ItemParent", fields: [parentId], references: [id])
  parentId String? @db.VarChar(36)

  @@index([ownerId, ownerType, itemType])
  @@index([ownerId, ownerType])
  @@index([ownerId, ownerType, skill])
  @@index([parentId])
}

model ItemText {
  userId String @db.VarChar(36)
  id     String @id @default(cuid()) @db.VarChar(36)

  description String? @db.VarChar(500)
  title       String? @db.VarChar(300)
  text        String  @db.Text
  summary     String? @db.Text

  itemId String @db.VarChar(36)
  item   Item   @relation("ItemTexts", fields: [itemId], references: [id], onDelete: Cascade)

  position     Int?
  fileId       String? @db.VarChar(36)
  parentItemId String? @db.VarChar(36)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([itemId])
}

model ItemJson {
  id   String @id @default(cuid()) @db.VarChar(36)
  json Json

  itemId String @db.VarChar(36)
  item   Item   @relation("ItemJsons", fields: [itemId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([itemId])
}

model ItemField {
  id    String @id @default(cuid()) @db.VarChar(36)
  type  String
  value String @db.VarChar(255)

  itemId String @db.VarChar(36)
  item   Item   @relation("ItemFields", fields: [itemId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([itemId])
}

model ItemBoolean {
  id      String  @id @default(cuid()) @db.VarChar(36)
  boolean Boolean

  itemId String @db.VarChar(36)
  item   Item   @relation("ItemBooleans", fields: [itemId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([itemId])
}

model TransactionValidation {
  id                 String @id @default(cuid()) @db.VarChar(36)
  environment        String @db.VarChar(100)
  origTxId           String @unique @db.VarChar(100)
  latestReceipt      String @db.Text
  app                String @db.VarChar(100)
  productId          String @db.VarChar(36)
  validationResponse Json

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  transaction Transaction @relation("TransactionValidation", fields: [id], references: [id])
}

model Notification {
  id        String   @id @default(cuid()) @db.VarChar(36)
  createdAt DateTime @default(now())
  message   Json?
  title     Json
  data      Json?
  link      String   @db.VarChar(500)
  userId    String   @db.VarChar(36)
  isRead    Boolean  @default(false)
  type      String?  @db.VarChar(100)

  @@index([userId])
}

model Admin {
  id       String       @id @default(cuid()) @db.VarChar(36)
  name     String       @db.VarChar(250)
  email    String?      @unique @db.VarChar(255)
  language Locale?      @default(fr)
  imageUrl String?      @db.VarChar(500)
  image    Json?
  role     AdminRole    @default(manager)
  files    AdminFile[]  @relation("FileAdmin")
  images   AdminImage[] @relation("ImageAdmin")
  contents Content[]    @relation("ContentAdmin")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
}

model Account {
  userId                String             @id @db.VarChar(36)
  user                  User               @relation("AccountUser", fields: [userId], references: [id])
  email                 String?            @unique @db.VarChar(255)
  birthdate             DateTime?
  subscription          SubscriptionStatus @default(free)
  startSubscriptionDate DateTime?
  endSubscriptionDate   DateTime?
  stripeCustomerId      String?            @unique @db.VarChar(255)
  currentTransactionId  String?            @db.VarChar(36) // The ID of the current transaction

  autoChargeEnabled   Boolean @default(false)
  autoChargeThreshold Int     @default(0)
  autoChargePlan      String  @default("smallCreditPack") @db.VarChar(50)

  balance Int @default(50)
  credit  Int @default(0)

  payments Payment[] @relation("AccountPayment")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email, stripeCustomerId])
}

model CreditHistory {
  id        String   @id @default(cuid()) @db.VarChar(36)
  change    Int // Positif pour l'ajout de crédits, négatif pour la décrémentation
  createdAt DateTime @default(now())
  counted   Boolean  @default(false)

  userId String @db.VarChar(36)

  @@index([userId, counted])
}

model Transaction {
  id          String            @id @default(cuid()) @db.VarChar(36)
  isSpent     Boolean           @default(false)
  amount      Float
  status      TransactionStatus @default(PENDING)
  processedAt DateTime?
  description String?           @db.VarChar(500)

  transactionValidation TransactionValidation? @relation("TransactionValidation")
  userId                String?                @db.VarChar(36)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Device {
  id          String  @id @default(cuid()) @db.VarChar(36)
  userId      String  @db.VarChar(36)
  user        User    @relation("DeviceUser", fields: [userId], references: [id])
  platform    String? @db.VarChar(100)
  oneSignalId String? @db.VarChar(255)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, platform])
}

model Profile {
  userId   String    @id @db.VarChar(36)
  user     User      @relation("ProfileUser", fields: [userId], references: [id])
  comments Comment[] @relation("ProfileComment")

  imageUrl      String? @db.VarChar(500)
  image         Json?
  country       String? @db.VarChar(100)
  gender        Gender?
  job           String? @db.VarChar(150)
  instagram     String? @db.VarChar(255)
  facebook      String? @db.VarChar(255)
  upToDate      Boolean @default(true)
  points        Int     @default(0)
  rank          Int     @default(0)
  countryRank   Int     @default(0)
  numberOfViews Int     @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Image {
  id           String    @id @default(cuid()) @db.VarChar(36)
  userId       String    @db.VarChar(36)
  publicId     String    @db.VarChar(255)
  url          String    @db.VarChar(500)
  thumbnailUrl String?   @db.VarChar(500)
  json         Json?
  type         MediaType @default(image)
  workflowId   String?   @db.VarChar(255)
  topic        String?   @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, type])
}

model Video {
  id       String @id @default(cuid()) @db.VarChar(36)
  userId   String @db.VarChar(36)
  publicId String @db.VarChar(255)
  url      String @db.VarChar(500)

  thumbnailUrl String?   @db.VarChar(500)
  json         Json?
  type         MediaType @default(video)
  workflowId   String?   @db.VarChar(255)
  topic        String?   @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, type])
}

model Audio {
  id            String    @id @default(cuid()) @db.VarChar(36)
  userId        String    @db.VarChar(36)
  publicId      String    @db.VarChar(255)
  url           String    @db.VarChar(500)
  json          Json?
  type          MediaType @default(audio)
  workflowId    String?   @db.VarChar(255)
  topic         String?   @db.Text
  name          String    @db.VarChar(255)
  transcription String?   @db.Text
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@index([userId, type])
}

model AdminImage {
  id       String    @id @default(cuid()) @db.VarChar(36)
  adminId  String    @db.VarChar(36)
  admin    Admin     @relation("ImageAdmin", fields: [adminId], references: [id])
  publicId String    @db.VarChar(255)
  url      String    @db.VarChar(500)
  json     Json
  type     MediaType @default(file)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([adminId, type])
}

model ExempleFile {
  id       String    @id @default(cuid()) @db.VarChar(36)
  userId   String    @db.VarChar(36)
  user     Exemple   @relation("FileExemple", fields: [userId], references: [id])
  publicId String    @db.VarChar(255)
  url      String    @db.VarChar(500)
  json     Json
  type     MediaType @default(file)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, type])
}

model File {
  id       String    @id @default(cuid()) @db.VarChar(36)
  userId   String    @db.VarChar(36)
  publicId String    @db.VarChar(255)
  url      String    @db.VarChar(500)
  json     Json
  type     MediaType @default(file)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, type])
}

model Home {
  id    String @id @default(cuid()) @db.VarChar(36)
  value Json?
}

model Highlight {
  id       String @id @default(cuid()) @db.VarChar(36)
  location String @db.VarChar(255)
  value    Json?
}

model AdminFile {
  id       String    @id @default(cuid()) @db.VarChar(36)
  adminId  String    @db.VarChar(36)
  admin    Admin     @relation("FileAdmin", fields: [adminId], references: [id])
  publicId String    @db.VarChar(255)
  url      String    @db.VarChar(500)
  json     Json
  type     MediaType @default(file)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([adminId, type])
}

model Auth {
  userType AuthUserType
  userId   String       @db.VarChar(36)
  mode     AuthMode
  key      String       @db.VarChar(255)
  token    String       @db.VarChar(500)
  data     Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([userType, userId, mode])
}

model Token {
  token String @id @db.VarChar(500)
  type  String @db.VarChar(100)
  data  Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Comment {
  id         String     @id @default(cuid()) @db.VarChar(36)
  userId     String     @db.VarChar(36)
  text       String     @db.Text
  objectType ObjectType
  objectId   String     @db.VarChar(36)
  anwsers    Comment[]  @relation("AnswerComment")
  comment    Comment?   @relation("AnswerComment", fields: [commentId], references: [id])
  commentId  String?    @db.VarChar(36)
  profile    Profile    @relation("ProfileComment", fields: [userId], references: [userId])

  reports ReportComment[] @relation("ReportComment")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([objectType, objectId])
}

model ReportComment {
  id        String  @id @default(cuid()) @db.VarChar(36)
  userId    String  @db.VarChar(36)
  text      String? @db.Text
  content   Json?
  commentId String  @db.VarChar(36)
  comment   Comment @relation("ReportComment", fields: [commentId], references: [id])
  reported  Boolean

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, commentId])
}

model Report {
  id         String     @id @default(cuid()) @db.VarChar(36)
  userId     String     @db.VarChar(36)
  text       String?    @db.Text
  content    Json?
  objectType ObjectType
  objectId   String     @db.VarChar(36)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([objectType, objectId])
}

model Analytic {
  id         String        @id @default(cuid()) @db.VarChar(36)
  userId     String?       @db.VarChar(36)
  event      AnalyticEvent
  name       String?       @db.VarChar(60)
  objectType ObjectType?
  objectId   String?       @db.VarChar(36)
  deviceId   String?       @db.VarChar(36)
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  @@index([userId])
  @@index([deviceId])
  @@index([objectType, objectId, event])
}

model AnalyticView {
  objectType ObjectType
  objectId   String     @db.VarChar(36)
  count      Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([objectType, objectId])
}

model Field {
  id    String @id @default(cuid()) @db.VarChar(36)
  key   String @db.VarChar(255)
  name  String @db.VarChar(255)
  value Json

  locale       Locale? @default(fr)
  translations Json?

  @@index([key])
}

model Rating {
  id         String     @id @default(cuid()) @db.VarChar(36)
  userId     String     @db.VarChar(36)
  objectId   String     @db.VarChar(36)
  objectType ObjectType
  count      Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([objectType, objectId, userId])
}

model Like {
  id         String     @id @default(cuid()) @db.VarChar(36)
  userId     String     @db.VarChar(36)
  objectId   String     @db.VarChar(36)
  objectType ObjectType
  reaction   Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([objectType, objectId, userId])
}

model News {
  id                    String            @id @default(cuid()) @db.VarChar(36)
  title                 String            @db.VarChar(255)
  description           String?           @db.Text
  category              NewsCategory      @default(medias)
  image                 Json?
  imageUrl              String?           @db.VarChar(500)
  payWithCoins          Boolean?
  payWithSubscription   Boolean?
  priceWithCoins        Int?
  priceWithSubscription Int?
  numberOfCoinsIfShared Int?
  url                   String?           @db.VarChar(500)
  author                String?           @db.VarChar(255)
  numberOfViews         Int               @default(0)
  publicationDate       DateTime?
  publicationStatus     PublicationStatus @default(draft)

  locale       Locale? @default(fr)
  translations Json?

  rating Int @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([category, publicationStatus])
}

model Content {
  id        String    @id @default(cuid()) @db.VarChar(36)
  name      Right
  rightType RightType
  owner     String?   @db.VarChar(255)

  adminId String @db.VarChar(36)
  admin   Admin  @relation("ContentAdmin", fields: [adminId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([adminId])
}

model Task {
  id            String         @id @default(uuid()) @db.Uuid
  status        WorkflowStatus @default(PENDING)
  duration      Int            @default(0)
  nbOfExecution Int            @default(0)
  taskName      String         @db.VarChar(255)
  result        Json?
  error         Json?
  next          Json?
  dependencies  Json?

  ownerId   String    @db.VarChar(36)
  ownerType OwnerType
  skill     Skill     @default(project)
  inputs    Json
  podId     String?   @db.VarChar(255)

  workflowId String   @db.Uuid
  workflow   Workflow @relation("WorkflowTask", fields: [workflowId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([skill])
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
}

enum WorkflowStatus {
  PENDING
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  ERROR
}

enum ActionStepStatus {
  form
  upload
  workflow
  create
}

enum AnalyticEvent {
  view
  click
  track
}

enum UserRole {
  anonymous
  user
  customer
}

enum Skill {
  project
  finance
  dev
  brand
  growth
  community
  design
  ads
  post
}

enum CompetitionType {
  inPerson
  online
  external
}

enum AdminRole {
  manager
  admin
  super
}

enum AuthMode {
  email_password
  reset_password
  google
  linkedin
  twitter
  facebook
}

enum AuthUserType {
  user
  admin
  anonymous
}

enum Gender {
  man
  woman
  private
}

enum MediaType {
  pdf
  image
  video
  audio
  file
  reference
  youtube
  raw
}

enum CronStatus {
  computed
  planned
  processing
}

enum Locale {
  en
  fr
}

enum OwnerType {
  profile
  user
  news
  comment
  post
  video
  image
  audio
}

enum ObjectType {
  news
  comment
  profile
  audio
  post
}

enum PublicationStatus {
  published
  draft
  hidden
}

enum Duration {
  minutes
  hours
  days
}

enum NewsCategory {
  medias
  innewss
}

enum Right {
  Users
  Admins
  News
  NumberOfUsers
}

enum RightType {
  Read
  Update
}

enum SubscriptionStatus {
  enterprise
  free
  monthly
  monthlySubscription
  pro
  yearly
  yearlySubscription
}

enum BrandType {
  brand
  identity
  strategy
  messaging
}

enum WorkerType {
  project
  dev
  design
  finance
  ads
  growth
  community
}

model Access {
  objectId   String     @id @db.VarChar(36)
  objectType ObjectType
  userId     String     @db.VarChar(36)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt

  @@index([objectId, objectType])
}

enum ChatConversationStatus {
  active
  archived
  deleted
}

enum ChatMessageRole {
  user
  assistant
  system
  model // google gemini assistant
}

enum ChatMessageType {
  text
  audio
  image
  video
}

model Post {
  id              String   @id @default(cuid()) @db.VarChar(36)
  title           String   @db.VarChar(255)
  summary         String   @db.Text
  content         String   @db.Text
  userId          String   @db.VarChar(36)
  user            User     @relation("UserPosts", fields: [userId], references: [id])
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  topic           String   @db.Text
  workflowId      String?  @db.VarChar(36)
  isPremium       Boolean  @default(false)
  creditsRequired Int
  hasAccess       Boolean  @default(true)

  @@index([userId])
}

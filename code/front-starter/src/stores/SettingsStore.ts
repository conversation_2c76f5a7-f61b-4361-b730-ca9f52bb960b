import { create } from 'zustand';
import { UserService } from 'src/services/api';
import { useAuthStore } from './AuthStore';

type SettingsState = {
  // Interface settings
  theme: 'light' | 'dark' | 'system';
  autoSave: boolean;

  // Notification settings
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  creditAlerts: boolean;

  // Privacy settings
  dataSharing: boolean;
  analytics: boolean;

  // Actions
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  setAutoSave: (autoSave: boolean) => void;
  setEmailNotifications: (enabled: boolean) => void;
  setPushNotifications: (enabled: boolean) => void;
  setMarketingEmails: (enabled: boolean) => void;
  setCreditAlerts: (enabled: boolean) => void;
  setDataSharing: (enabled: boolean) => void;
  setAnalytics: (enabled: boolean) => void;

  // Save settings to backend
  saveInterfaceSettings: () => Promise<void>;
  saveNotificationSettings: () => Promise<void>;
  savePrivacySettings: () => Promise<void>;

  // Initialize settings from user data
  initialize: () => void;
};

export const useSettingsStore = create<SettingsState>((set, get) => ({
  // Default values
  theme: 'light',
  autoSave: true,
  emailNotifications: true,
  pushNotifications: true,
  marketingEmails: true,
  creditAlerts: true,
  dataSharing: true,
  analytics: true,

  // Setters
  setTheme: (theme) => {
    set({ theme });
    localStorage.setItem('theme', theme);

    // Apply theme to document
    if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      document.documentElement.classList.add('dark-theme');
    } else {
      document.documentElement.classList.remove('dark-theme');
    }
  },

  setAutoSave: (autoSave) => set({ autoSave }),
  setEmailNotifications: (emailNotifications) => set({ emailNotifications }),
  setPushNotifications: (pushNotifications) => set({ pushNotifications }),
  setMarketingEmails: (marketingEmails) => set({ marketingEmails }),
  setCreditAlerts: (creditAlerts) => set({ creditAlerts }),
  setDataSharing: (dataSharing) => set({ dataSharing }),
  setAnalytics: (analytics) => set({ analytics }),

  // Save settings to backend
  saveInterfaceSettings: async () => {
    const { theme } = get();
    const { user } = useAuthStore.getState();

    if (!user) return;

    try {
      // Save language preference to backend
      await UserService.userControllerUpdate(user.id, {
        // Only save relevant settings to backend
        // Theme is stored locally
      });

      // Refresh user data
      useAuthStore.getState().refresh();
    } catch (error) {
      console.error('Failed to save interface settings:', error);
    }
  },

  saveNotificationSettings: async () => {
    const { emailNotifications, pushNotifications, marketingEmails, creditAlerts } = get();
    const { user } = useAuthStore.getState();

    if (!user) return;

    try {
      // In a real implementation, we would save these to the backend
      console.log('Saving notification settings:', {
        emailNotifications,
        pushNotifications,
        marketingEmails,
        creditAlerts,
      });

      // Refresh user data
      useAuthStore.getState().refresh();
    } catch (error) {
      console.error('Failed to save notification settings:', error);
    }
  },

  savePrivacySettings: async () => {
    const { dataSharing, analytics } = get();
    const { user } = useAuthStore.getState();

    if (!user) return;

    try {
      // In a real implementation, we would save these to the backend
      console.log('Saving privacy settings:', {
        dataSharing,
        analytics,
      });

      // Refresh user data
      useAuthStore.getState().refresh();
    } catch (error) {
      console.error('Failed to save privacy settings:', error);
    }
  },

  // Initialize settings from user data and local storage
  initialize: () => {
    // Load theme from local storage
    const storedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'system' | null;
    if (storedTheme) {
      get().setTheme(storedTheme);
    } else {
      // Default to system preference
      get().setTheme('system');
    }

    // In a real implementation, we would load other settings from the backend
    // For now, we'll use the defaults
  },
}));

// Initialize settings when the store is first imported
useSettingsStore.getState().initialize();

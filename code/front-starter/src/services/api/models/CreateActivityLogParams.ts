/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateActivityLogParams = {
  /**
   * User ID
   */
  userId: string;
  /**
   * Type of action performed
   */
  actionType:
    | 'CREATE'
    | 'READ'
    | 'UPDATE'
    | 'DELETE'
    | 'LOGIN'
    | 'LOGOUT'
    | 'UPLOAD'
    | 'DOWNLOAD'
    | 'SHARE'
    | 'COMMENT'
    | 'LIKE'
    | 'PAYMENT'
    | 'SUBSCRIPTION'
    | 'SETTINGS_CHANGE'
    | 'OTHER';
  /**
   * Type of entity affected
   */
  entityType: string;
  /**
   * ID of the entity affected
   */
  entityId?: string;
  /**
   * Description of the action
   */
  description: string;
  /**
   * Additional metadata about the action
   */
  metadata?: any;
  /**
   * IP address of the user
   */
  ipAddress?: string;
  /**
   * User agent of the browser/device
   */
  userAgent?: string;
};

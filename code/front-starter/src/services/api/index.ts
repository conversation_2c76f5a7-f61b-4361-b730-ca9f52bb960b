/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export { ApiError } from './core/ApiError';
export { CancelablePromise, CancelError } from './core/CancelablePromise';
export { OpenAPI } from './core/OpenAPI';

export type { AutoChargeSettingsParams } from './models/AutoChargeSettingsParams';
export type { Buffer } from './models/Buffer';
export type { CheckRatingParams } from './models/CheckRatingParams';
export type { CreateAccessParams } from './models/CreateAccessParams';
export type { CreateActionParams } from './models/CreateActionParams';
export type { CreateActivityLogParams } from './models/CreateActivityLogParams';
export type { CreateAdminParams } from './models/CreateAdminParams';
export type { CreateAgentParams } from './models/CreateAgentParams';
export type { CreateAnalyticParams } from './models/CreateAnalyticParams';
export type { CreateCartPaymentParams } from './models/CreateCartPaymentParams';
export type { CreateChatMessageParams } from './models/CreateChatMessageParams';
export type { CreateCommentAnswerParams } from './models/CreateCommentAnswerParams';
export type { CreateCommentParams } from './models/CreateCommentParams';
export type { CreateContentParams } from './models/CreateContentParams';
export type { CreateConversationParams } from './models/CreateConversationParams';
export type { CreateExempleParams } from './models/CreateExempleParams';
export type { CreateHighlightParams } from './models/CreateHighlightParams';
export type { CreateHomeParams } from './models/CreateHomeParams';
export type { CreateImageParams } from './models/CreateImageParams';
export type { CreateItemParams } from './models/CreateItemParams';
export type { CreateLikeParams } from './models/CreateLikeParams';
export type { CreateNewsParams } from './models/CreateNewsParams';
export type { CreateOneTimePaymentParams } from './models/CreateOneTimePaymentParams';
export type { CreatePaymentParams } from './models/CreatePaymentParams';
export type { CreateRatingParams } from './models/CreateRatingParams';
export type { CreateReportParams } from './models/CreateReportParams';
export type { CreateSubscriptionParams } from './models/CreateSubscriptionParams';
export type { CreateUserParams } from './models/CreateUserParams';
export type { CreditPurchaseParams } from './models/CreditPurchaseParams';
export type { DeleteAccountParams } from './models/DeleteAccountParams';
export type { FieldCreateOptionsParams } from './models/FieldCreateOptionsParams';
export type { FindAccessParams } from './models/FindAccessParams';
export type { FindAdminParams } from './models/FindAdminParams';
export type { FindAgentParams } from './models/FindAgentParams';
export type { FindAnalyticParams } from './models/FindAnalyticParams';
export type { FindCommentParams } from './models/FindCommentParams';
export type { FindContentParams } from './models/FindContentParams';
export type { FindConversationsParams } from './models/FindConversationsParams';
export type { FindExempleParams } from './models/FindExempleParams';
export type { FindImageParams } from './models/FindImageParams';
export type { FindLikeParams } from './models/FindLikeParams';
export type { FindMessagesParams } from './models/FindMessagesParams';
export type { FindNewsParams } from './models/FindNewsParams';
export type { FindReportParams } from './models/FindReportParams';
export type { FindUserParams } from './models/FindUserParams';
export type { FindWorkflowParams } from './models/FindWorkflowParams';
export type { ForgotPasswordAdminParams } from './models/ForgotPasswordAdminParams';
export type { ForgotPasswordParams } from './models/ForgotPasswordParams';
export type { GetAnalyticViewParams } from './models/GetAnalyticViewParams';
export type { getTaskParams } from './models/getTaskParams';
export type { ListCommentParams } from './models/ListCommentParams';
export type { ManualChargeParams } from './models/ManualChargeParams';
export type { RequestTask } from './models/RequestTask';
export type { ResetPasswordAdminParams } from './models/ResetPasswordAdminParams';
export type { ResetPasswordParams } from './models/ResetPasswordParams';
export type { SaveReceiptParams } from './models/SaveReceiptParams';
export type { SendMessageParams } from './models/SendMessageParams';
export type { SetAnalyticViewParams } from './models/SetAnalyticViewParams';
export type { SetPaymentMethodParams } from './models/SetPaymentMethodParams';
export type { SignalCommentParams } from './models/SignalCommentParams';
export type { SignInAdminParams } from './models/SignInAdminParams';
export type { SignInParams } from './models/SignInParams';
export type { SignUpParams } from './models/SignUpParams';
export type { UpdateAccessParams } from './models/UpdateAccessParams';
export type { UpdateAccountParams } from './models/UpdateAccountParams';
export type { UpdateActionParams } from './models/UpdateActionParams';
export type { UpdateAdminParams } from './models/UpdateAdminParams';
export type { UpdateCommentParams } from './models/UpdateCommentParams';
export type { UpdateContentParams } from './models/UpdateContentParams';
export type { UpdateConversationParams } from './models/UpdateConversationParams';
export type { UpdateExempleParams } from './models/UpdateExempleParams';
export type { UpdateHighlightParams } from './models/UpdateHighlightParams';
export type { UpdateHomeParams } from './models/UpdateHomeParams';
export type { UpdateItemJsonBody } from './models/UpdateItemJsonBody';
export type { UpdateItemTextBody } from './models/UpdateItemTextBody';
export type { UpdateLikeParams } from './models/UpdateLikeParams';
export type { UpdateNewsParams } from './models/UpdateNewsParams';
export type { UpdatePasswordAminParams } from './models/UpdatePasswordAminParams';
export type { UpdatePasswordParams } from './models/UpdatePasswordParams';
export type { UpdateProfileParams } from './models/UpdateProfileParams';
export type { UpdateReportParams } from './models/UpdateReportParams';
export type { UpdateTaskStatus } from './models/UpdateTaskStatus';
export type { UpdateUserCountParams } from './models/UpdateUserCountParams';
export type { UpdateUserParams } from './models/UpdateUserParams';

export * as AccessService from './services/AccessService';
export * as AccountService from './services/AccountService';
export * as ActionService from './services/ActionService';
export * as ActivityLogService from './services/ActivityLogService';
export * as AdminService from './services/AdminService';
export * as AnalyticService from './services/AnalyticService';
export * as AppService from './services/AppService';
export * as AuthService from './services/AuthService';
export * as ChatAgentService from './services/ChatAgentService';
export * as ChatChatConversationService from './services/ChatChatConversationService';
export * as ChatChatMessageService from './services/ChatChatMessageService';
export * as ChatOpenaiRealtimeService from './services/ChatOpenaiRealtimeService';
export * as CommentService from './services/CommentService';
export * as ContentService from './services/ContentService';
export * as CronService from './services/CronService';
export * as ExempleService from './services/ExempleService';
export * as FieldService from './services/FieldService';
export * as HighlightService from './services/HighlightService';
export * as HomeService from './services/HomeService';
export * as IapService from './services/IapService';
export * as ItemService from './services/ItemService';
export * as ItemJsonService from './services/ItemJsonService';
export * as ItemTextsService from './services/ItemTextsService';
export * as LikeService from './services/LikeService';
export * as NewsService from './services/NewsService';
export * as PaymentService from './services/PaymentService';
export * as ProfileService from './services/ProfileService';
export * as RatingService from './services/RatingService';
export * as ReportService from './services/ReportService';
export * as SeedService from './services/SeedService';
export * as SetupService from './services/SetupService';
export * as UserService from './services/UserService';
export * as WorkflowService from './services/WorkflowService';

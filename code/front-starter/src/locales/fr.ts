export default {
  setup: {
    common: {
      step: 'Étape {{current}} sur {{total}}',
      next: 'Suivant',
      back: 'Retour',
      skip: 'Passer',
    },
    welcome: {
      title: "Bienvenue dans l'assistant de configuration",
      description:
        'Cet assistant vous aidera à configurer votre compte et vos préférences. Cela ne prendra que quelques minutes.',
      getStarted: 'Commencer',
    },
    profile: {
      title: 'Informations du profil',
      description: 'Parlez-nous un peu de vous. Ces informations seront visibles sur votre profil.',
      nameLabel: 'Nom',
      namePlaceholder: 'Entrez votre nom',
      jobLabel: 'Profession',
      jobPlaceholder: 'Entrez votre profession',
      countryLabel: 'Pays',
      countryPlaceholder: 'Sélectionnez votre pays',
      bioLabel: 'Bio',
      bioPlaceholder: 'Parlez-nous de vous',
    },
    preferences: {
      title: 'Préférences',
      description: "Définissez vos préférences pour l'application.",
      languageLabel: 'Langue',
      themeLabel: 'Thème',
      themeLight: 'Clair',
      themeDark: 'Sombre',
      themeSystem: 'Système',
    },
    complete: {
      title: 'Configuration terminée !',
      description:
        'Votre compte est maintenant configuré et prêt à être utilisé. Vous pouvez modifier ces paramètres à tout moment depuis votre profil.',
      finishSetup: 'Aller au tableau de bord',
    },
  },
  page: {
    profile: {
      title: 'Profil Utilisateur',
      info: 'Informations du Profil',
      avatar: 'Photo de Profil',
      bio: 'Biographie',
      social: 'Réseaux Sociaux',
    },
    account: {
      info: 'Infos du compte',
    },
    activityLog: {
      title: "Journal d'activité",
      description: "Historique de vos actions sur l'application",
      noLogs: "Aucune activité n'a été enregistrée",
      loadMore: "Charger plus d'activités",
      entity: 'Entité',
    },
    settings: {
      info: 'Préférences',
      tabs: {
        profile: 'Profil',
        aiModels: 'Modèles IA',
        interface: 'Interface',
        notifications: 'Notifications',
        privacy: 'Confidentialité',
      },
      aiModel: {
        textGeneration: 'Génération de texte',
        imageGeneration: "Génération d'images",
        videoGeneration: 'Génération de vidéos',
        audioGeneration: 'Génération audio',
        model: 'Modèle',
        provider: 'Fournisseur',
        temperature: 'Température',
      },
      interface: {
        appearance: 'Apparence',
        language: 'Langue',
        theme: 'Thème',
        behavior: 'Comportement',
        autoSave: "Sauvegarde automatique pendant l'édition",
      },
      notifications: {
        emailNotifications: 'Notifications par email',
        receiveEmails: 'Recevoir des notifications par email',
        marketingEmails: 'Recevoir des emails marketing',
        appNotifications: "Notifications de l'application",
        pushNotifications: 'Activer les notifications push',
        creditAlerts: 'Recevoir des alertes de solde de crédit',
      },
    },
    dashboard: {
      title: 'Tableau de bord',
      activityFeed: "Flux d'activité",
      noActivities: 'Aucune activité trouvée',
      noSummary: "Aucun résumé d'activité disponible",
      activitySummary: "Résumé d'activité",
      filters: 'Filtres',
      resetFilters: 'Réinitialiser',
      actionTypes: "Types d'action",
      entityTypes: "Types d'entité",
      listView: 'Liste',
      summaryView: 'Résumé',
      calendarView: 'Calendrier',
      totalActivities: 'Total des activités',
      lastLogin: 'Dernière connexion',
      mostCommonAction: 'Action la plus courante',
      never: 'Jamais',
      none: 'Aucune',
      loading: 'Chargement...',
      refresh: 'Actualiser',
    },
    notifications: {
      title: 'Notifications',
      empty: "Vous n'avez aucune notification",
      loginRequired: 'Veuillez vous connecter pour voir vos notifications',
      markAllAsRead: 'Tout marquer comme lu',
      deleteAll: 'Tout supprimer',
      deleted: 'Notification supprimée',
      allMarkedAsRead: 'Toutes les notifications ont été marquées comme lues',
      allDeleted: 'Toutes les notifications ont été supprimées',
      filters: {
        status: 'Statut',
        all: 'Tous',
        read: 'Lus',
        unread: 'Non lus',
        type: 'Type',
        info: 'Information',
        success: 'Succès',
        warning: 'Avertissement',
        error: 'Erreur',
        system: 'Système',
      },
      privacy: {
        dataSharing: 'Partage de données',
        allowDataSharing: 'Autoriser le partage de données pour améliorer les services',
        dataSharingDescription:
          'Nous utilisons vos données pour améliorer nos services et vous offrir une meilleure expérience. Vos données ne sont jamais vendues à des tiers.',
        analytics: 'Analytiques',
        allowAnalytics: 'Autoriser le suivi analytique',
        analyticsDescription:
          "Nous collectons des données d'utilisation anonymes pour améliorer nos services. Cela nous aide à comprendre comment vous utilisez l'application.",
        dangerZone: 'Zone de danger',
        deleteAccount: 'Supprimer le compte',
        deleteAccountDescription:
          'Supprimer définitivement votre compte et toutes les données associées. Cette action ne peut pas être annulée.',
        confirmDeleteTitle: 'Confirmer la suppression du compte',
        confirmDeleteMessage:
          'Êtes-vous sûr de vouloir supprimer votre compte ? Cette action ne peut pas être annulée et toutes vos données seront définitivement perdues.',
      },
    },
    event: {
      participants: 'Participants',
      trends: 'Tendance des participants',
      numberOfParticipants: 'Nombre de participants au fil du temps',
      details: 'Détails de la manifestation',
      people: 'Nombre de personnes ayant participé',
      ending: 'Fin',
      status: {
        ongoing: 'Personnes en train de manifester',
        upcoming: 'Nombre de personnes qui participeront',
        ended: 'Nombre de personnes ayant participé',
        unknown: '',
      },
      mapButtonText: "D'où manifestent-ils ?",
    },
    home: {
      live: 'En direct',
      all: 'Tous',
      ongoing: 'En cours',
      upcoming: 'À venir',
      ended: 'Terminé',
      protests: 'Manifestations',
      currentParticipants: 'Manifestent actuellement',
      participants: 'Manifestants comptabilisés',
      browse: 'Parcourir toutes les manifestations tendances',
      join: "Aperçu rapide d'une manifestation",
    },
    login: {
      welcome: 'Bon retour',
      haveAccount: "Vous n'avez pas de compte ?",
      signUp: "S'inscrire",
      forgotPassword: 'Mot de passe oublié',
    },
    register: {
      joinUs: 'Rejoignez-nous',
      signIn: 'Se connecter',
      alreadyAccount: 'Vous avez déjà un compte ?',
    },
  },
  modal: {},
  form: {
    profile: {
      bio: {
        label: 'Bio',
        placeholder: 'Parlez-nous de vous...',
      },
      job: {
        label: 'Profession',
        placeholder: 'Votre profession ou titre de poste',
      },
      country: {
        label: 'Pays',
        placeholder: 'Votre pays',
      },
      gender: {
        label: 'Genre',
        placeholder: 'Sélectionnez votre genre',
        options: {
          male: 'Homme',
          female: 'Femme',
          other: 'Autre',
          preferNotToSay: 'Préfère ne pas dire',
        },
      },
      instagram: {
        label: 'Instagram',
        placeholder: "Votre nom d'utilisateur Instagram",
      },
      facebook: {
        label: 'Facebook',
        placeholder: 'Votre profil Facebook',
      },
    },
    name: {
      label: 'Nom',
      placeholder: 'Entrez votre nom',
      required: 'Le nom est obligatoire',
      format: 'Le nom est invalide',
    },
    firstName: {
      label: 'Prénom',
      placeholder: 'Entrez votre prénom',
      required: 'Le prénom est requis',
      format: 'Le prénom est invalide',
    },
    lastName: {
      label: 'Nom de famille',
      placeholder: 'Entrez votre nom de famille',
      required: 'Le nom de famille est requis',
      format: 'Le nom de famille est invalide',
    },
    location: {
      label: 'Lieu',
      placeholder: 'Entrez votre lieu',
      required: 'Le lieu est requis',
      format: 'Le lieu est invalide',
    },
    slogan: {
      label: 'Veuillez écrire votre slogan/message pour le monde',
      placeholder: 'Entrez votre slogan',
      required: 'Le slogan est requis',
      format: 'Le slogan est invalide',
    },

    anonymous: {
      label: 'Anonyme',
      remain: 'Rester anonyme',
      required: "L'anonymat est requis",
      format: "L'anonymat est invalide",
    },
    age: {
      label: 'Âge',
      placeholder: 'Entrez votre âge',
      required: "L'âge est requis",
      format: "L'âge est invalide",
    },
    gender: {
      label: 'Genre',
      placeholder: 'Entrez votre genre',
      required: 'Le genre est requis',
      format: 'Le genre est invalide',
      values: {
        man: 'Homme',
        woman: 'Femme',
      },
    },
    email: {
      label: 'Email',
      placeholder: '<EMAIL>',
      required: "L'email est requis",
      format: "L'email est invalide",
    },
    password: {
      label: 'Mot de passe',
      placeholder: '******',
      required: 'Le mot de passe est requis',
      short: 'Mot de passe trop court',
      match: 'Les mots de passe ne correspondent pas',
    },
    birthdate: {
      label: 'Date de naissance',
      placeholder: 'Entrez votre date de naissance',
      required: 'La date de naissance est requise',
      format: 'La date de naissance est invalide',
    },
  },
  button: {
    join: 'Rejoindre maintenant',
    login: 'Se connecter',
    leave: 'Quitter',
    viewProfile: 'Voir le Profil',
    register: 'Créer un compte',
    here: 'Je suis toujours là',
    uploadImage: 'Télécharger une image',
    backtoHome: "Retour à l'accueil",
    updateAccount: 'Enregistrer',
    updateProfile: 'Mettre à jour le profil',
    saveSettings: 'Enregistrer les paramètres',
    deleteAccount: 'Supprimer le compte',
    confirmDelete: 'Oui, supprimer mon compte',
  },
  format: {
    date: 'JJ/MM/AAAA',
    day: 'JJ MMMM AAAA',
    time: 'hh:mm',
    datetime: 'JJ/MM/AAAA - hh:mm',
  },
  error: {
    error: 'Erreur',
    warning: 'Avertissement',
    info: 'Info',
    network: 'Pas de connexion internet',
  },
  action: {
    ok: 'Ok',
    confirm: 'Confirmer',
    cancel: 'Annuler',
    yes: 'oui',
    no: 'non',
    save: 'Enregistrer',
    send: 'Envoyer',
    validate: 'Valider',
    logout: 'Déconnexion',
    edit: 'Modifier',
    remove: 'Supprimer',
    accept: 'Accepter',
    decline: 'Refuser',
  },
  activity: {
    title: "Journal d'activité",
    description: "Historique horodaté de vos actions sur l'application",
    no_logs: "Aucune activité n'a été enregistrée",
    load_more: "Charger plus d'activités",
    entity: 'Entité',
    action: {
      create: 'Création',
      read: 'Consultation',
      update: 'Modification',
      delete: 'Suppression',
      login: 'Connexion',
      logout: 'Déconnexion',
      upload: 'Téléversement',
      download: 'Téléchargement',
      share: 'Partage',
      comment: 'Commentaire',
      like: "J'aime",
      payment: 'Paiement',
      subscription: 'Abonnement',
      settings_change: 'Modification des paramètres',
      other: 'Autre action',
    },
  },
};

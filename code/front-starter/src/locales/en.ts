export default {
  "setup": {
    "common": {
      "step": "Step {{current}} of {{total}}",
      "next": "Next",
      "back": "Back",
      "skip": "Skip"
    },
    "welcome": {
      "title": "Welcome to the Setup Wizard",
      "description": "This wizard will help you set up your account and preferences. It will only take a few minutes to complete.",
      "getStarted": "Get Started"
    },
    "profile": {
      "title": "Profile Information",
      "description": "Tell us a bit about yourself. This information will be visible on your profile.",
      "nameLabel": "Name",
      "namePlaceholder": "Enter your name",
      "jobLabel": "Job Title",
      "jobPlaceholder": "Enter your job title",
      "countryLabel": "Country",
      "countryPlaceholder": "Select your country",
      "bioLabel": "Bio",
      "bioPlaceholder": "Tell us about yourself"
    },
    "preferences": {
      "title": "Preferences",
      "description": "Set your preferences for the application.",
      "languageLabel": "Language",
      "themeLabel": "Theme",
      "themeLight": "Light",
      "themeDark": "Dark",
      "themeSystem": "System"
    },
    "complete": {
      "title": "Setup Complete!",
      "description": "Your account is now set up and ready to use. You can change these settings at any time from your profile.",
      "finishSetup": "Go to Dashboard"
    }
  },
  "workflow": {
    "application": "Answering"
  },
  "page": {
    "profile": {
      "title": "User Profile",
      "info": "Profile Information",
      "avatar": "Profile Picture",
      "bio": "Biography",
      "social": "Social Media"
    },
    "workflow": {
      "title": "Processing"
    },
    "checkoutPage": {
      "title": "Checkout",
      "cart": "Your cart is empty.",
      "clear": "Clear all",
      "header": {
        "product": "Product",
        "quantity": "Quantity",
        "price": "Price",
        "total": "Total"
      }
    },
    "editor": {
      "form": {
        "buttonAddInput": "Add an input"
      }
    },
    "growth": {
      "title": "Growth",
      "funnel": {
        "header": "Funnel",
        "actionCreate": "Create a Funnel"
      },
      "strategy": {
        "header": "Strategy",
        "actionCreate": "Create a Strategy"
      }
    },
    "ads": {
      "title": "Ads",
      "campaign": {
        "header": "Campaign",
        "actionCreate": "Create a Campaign"
      },
      "visualAds": {
        "header": "Visual Ads",
        "actionCreate": "Create a Visual Ads"
      },
      "videoAds": {
        "header": "Video Ads",
        "actionCreate": "Create a Visual Ads"
      }
    },
    "community": {
      "title": "Community",
      "post": {
        "header": "Posts",
        "actionCreate": "Create a Post "
      },
      "planning": {
        "header": "Planning",
        "actionCreate": "Plan my Posts"
      }
    },
    "design": {
      "visual": {
        "header": "Visuals",
        "actionCreate": "Create a Visual "
      }
    },
    "dev": {
      "landing": {
        "header": "Landing Pages",
        "actionCreate": "Create a Landing Page"
      },
      "product": {
        "header": "Product Pages",
        "actionCreate": "Create a product Page"
      },
      "form": {
        "header": "Form Pages",
        "actionCreate": "Create a form"
      }
    },
    "finance": {
      "grant": {
        "workflow": {
          "title": "Analyzing"
        }
      },
      "ownerType": {
        "grant": "Grant",
        "pitchDeck": "Pitch Deck",
        "application": "Application"
      },
      "managerCards": {
        "grant": {
          "title": "Apply for a Grant/Batch"
        },
        "pitchDeck": {
          "title": "Create a Pitch Deck"
        },
        "financialForecast": {
          "title": "Create a Financial Forecast"
        }
      }
    },
    "overview": {
      "actionCards": {
        "createPitchDeck": "Create a Pitch Deck",
        "createLandingPage": "Create a Landing Page",
        "applyForGrant": "Apply for a Grant/Batch",
        "createNewsletter": "Create a Newsletter",
        "generateLogo": "Generate a new Logo"
      }
    },
    "visual": {
      "header": "Visual Identity",
      "logo": "Logo",
      "color": "Theme"
    },
    "account": {
      "info": "Account info"
    },
    "messaging": {
      "header": "Messsaging"
    },
    "identity": {
      "header": "Identity"
    },
    "strategy": {
      "header": "Positioning"
    },
    "brand": {
      "header": "Brand"
    },
    "product": {
      "header": "Product"
    },
    "offer": {
      "headerMain": "Offers",
      "header": "Offer"
    },
    "customer": {
      "headerMain": "Customers",
      "header": "Customer"
    },
    "market": {
      "header": "Market"
    },
    "projectConfig": {
      "title": "Project Configuration"
    },
    "newsletter": {
      "prompts": [
        {
          "id": "1",
          "prompt": "Add a meme",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod et sodales vehicula, pretium nec leo. Maecenas condimentum enim ut viverra ornare. Nulla sit amet lobortis justo. Vestibulum at rhoncus metus, at aliquet justo. Morbi vulputate eros at dui aliquam, in interdum lectus hendrerit. Aenean tristique pharetra dolor sed aliquam. Donec quis purus a turpis auctor mollis. Ut sodales fermentum mauris, et consequat dui imperdiet ac. Nunc nec libero quis est molestie ultricies eget et nulla. Vivamus feugiat mauris ultricies mi suscipit, non tincidunt lacus imperdiet. Ut vitae lacus in arcu volutpat rutrum. Maecenas ut euismod arcu. Maecenas quis lacus finibus, tristique elit nec, tempor est. Curabitur tincidunt, nisl in vulputate ultricies, tortor tortor faucibus sem, dapibus euismod ligula dolor eget tellus. Integer laoreet commodo ultrices. Suspendisse rutrum tempus volutpat. Donec et malesuada sem, vel fringilla augue. Nulla ornare auctor imperdiet. Vivamus suscipit purus at lorem vestibulum vestibulum. Praesent aliquet pharetra nibh id feugiat. Pellentesque pulvinar ultricies elit vel auctor. Quisque maximus mauris eget arcu tristique iaculis vel non odio. Aenean eu scelerisque tellus. Pellentesque blandit aliquam tortor ut semper. Etiam consectetur eros mi, eu mattis sem facilisis ac. Curabitur rutrum turpis ut maximus elementum. Suspendisse vitae magna aliquam, vestibulum justo mollis, dignissim elit. Morbi diam lacus, pretium vitae nisl sit amet, posuere maximus lorem. Integer ultricies tempor malesuada. Aenean in porttitor mi. Pellentesque sodales augue non odio venenatis, vitae auctor sem porta. Praesent congue auctor dui rhoncus tincidunt. Fusce et dui non orci vestibulum aliquet. Duis condimentum turpis augue, ac elementum libero sagittis non. Donec vel sodales magna, id dapibus lacus. Vestibulum lorem purus, ultrices sit amet tempor non, molestie fringilla elit. Donec tortor nulla, feugiat efficitur eros bibendum, consequat consectetur ligula. Etiam eget pellentesque nulla, sed dapibus nibh. In at arcu dapibus, eleifend velit in, molestie urna. Vestibulum diam lacus, egestas a nibh et, vestibulum dapibus dui. Mauris porta, nunc placerat porttitor dignissim, ipsum tortor auctor risus, vitae suscipit tellus quam eget ex. Aenean posuere vulputate tincidunt. Ut aliquam justo at quam blandit, et rutrum augue varius. Integer aliquet justo eget finibus scelerisque. Sed feugiat vel quam sit amet hendrerit. Suspendisse interdum tellus metus. Integer maximus condimentum quam, a pulvinar erat elementum eget. Phasellus scelerisque elit quam, sed ultricies tellus varius non. Maecenas libero urna, imperdiet at mauris sed, elementum scelerisque eros. Phasellus vitae blandit erat."
        },
        {
          "id": "2",
          "prompt": "Be funnier",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod et sodales vehicula, pretium nec leo. Maecenas condimentum enim ut viverra ornare. Nulla sit amet lobortis justo. Vestibulum at rhoncus metus, at aliquet justo. Morbi vulputate eros at dui aliquam, in interdum lectus hendrerit. Aenean tristique pharetra dolor sed aliquam. Donec quis purus a turpis auctor mollis. Ut sodales fermentum mauris, et consequat dui imperdiet ac. Nunc nec libero quis est molestie ultricies eget et nulla. Vivamus feugiat mauris ultricies mi suscipit, non tincidunt lacus imperdiet. Ut vitae lacus in arcu volutpat rutrum. Maecenas ut euismod arcu. Maecenas quis lacus finibus, tristique elit nec, tempor est. Curabitur tincidunt, nisl in vulputate ultricies, tortor tortor faucibus sem, dapibus euismod ligula dolor eget tellus. Integer laoreet commodo ultrices. Suspendisse rutrum tempus volutpat. Donec et malesuada sem, vel fringilla augue. Nulla ornare auctor imperdiet. Vivamus suscipit purus at lorem vestibulum vestibulum. Praesent aliquet pharetra nibh id feugiat. Pellentesque pulvinar ultricies elit vel auctor. Quisque maximus mauris eget arcu tristique iaculis vel non odio. Aenean eu scelerisque tellus. Pellentesque blandit aliquam tortor ut semper. Etiam consectetur eros mi, eu mattis sem facilisis ac. Curabitur rutrum turpis ut maximus elementum. Suspendisse vitae magna aliquam, vestibulum justo mollis, dignissim elit. Morbi diam lacus, pretium vitae nisl sit amet, posuere maximus lorem. Integer ultricies tempor malesuada. Aenean in porttitor mi. Pellentesque sodales augue non odio venenatis, vitae auctor sem porta. Praesent congue auctor dui rhoncus tincidunt. Fusce et dui non orci vestibulum aliquet. Duis condimentum turpis augue, ac elementum libero sagittis non. Donec vel sodales magna, id dapibus lacus. Vestibulum lorem purus, ultrices sit amet tempor non, molestie fringilla elit. Donec tortor nulla, feugiat efficitur eros bibendum, consequat consectetur ligula. Etiam eget pellentesque nulla, sed dapibus nibh. In at arcu dapibus, eleifend velit in, molestie urna. Vestibulum diam lacus, egestas a nibh et, vestibulum dapibus dui. Mauris porta, nunc placerat porttitor dignissim, ipsum tortor auctor risus, vitae suscipit tellus quam eget ex. Aenean posuere vulputate tincidunt. Ut aliquam justo at quam blandit, et rutrum augue varius. Integer aliquet justo eget finibus scelerisque. Sed feugiat vel quam sit amet hendrerit. Suspendisse interdum tellus metus. Integer maximus condimentum quam, a pulvinar erat elementum eget. Phasellus scelerisque elit quam, sed ultricies tellus varius non. Maecenas libero urna, imperdiet at mauris sed, elementum scelerisque eros. Phasellus vitae blandit erat."
        }
      ],
      "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod et sodales vehicula, pretium nec leo. Maecenas condimentum enim ut viverra ornare. Nulla sit amet lobortis justo. Vestibulum at rhoncus metus, at aliquet justo. Morbi vulputate eros at dui aliquam, in interdum lectus hendrerit. Aenean tristique pharetra dolor sed aliquam. Donec quis purus a turpis auctor mollis. Ut sodales fermentum mauris, et consequat dui imperdiet ac. Nunc nec libero quis est molestie ultricies eget et nulla. Vivamus feugiat mauris ultricies mi suscipit, non tincidunt lacus imperdiet. Ut vitae lacus in arcu volutpat rutrum. Maecenas ut euismod arcu. Maecenas quis lacus finibus, tristique elit nec, tempor est. Curabitur tincidunt, nisl in vulputate ultricies, tortor tortor faucibus sem, dapibus euismod ligula dolor eget tellus. Integer laoreet commodo ultrices. Suspendisse rutrum tempus volutpat. Donec et malesuada sem, vel fringilla augue. Nulla ornare auctor imperdiet. Vivamus suscipit purus at lorem vestibulum vestibulum. Praesent aliquet pharetra nibh id feugiat. Pellentesque pulvinar ultricies elit vel auctor. Quisque maximus mauris eget arcu tristique iaculis vel non odio. Aenean eu scelerisque tellus. Pellentesque blandit aliquam tortor ut semper. Etiam consectetur eros mi, eu mattis sem facilisis ac. Curabitur rutrum turpis ut maximus elementum. Suspendisse vitae magna aliquam, vestibulum justo mollis, dignissim elit. Morbi diam lacus, pretium vitae nisl sit amet, posuere maximus lorem. Integer ultricies tempor malesuada. Aenean in porttitor mi. Pellentesque sodales augue non odio venenatis, vitae auctor sem porta. Praesent congue auctor dui rhoncus tincidunt. Fusce et dui non orci vestibulum aliquet. Duis condimentum turpis augue, ac elementum libero sagittis non. Donec vel sodales magna, id dapibus lacus. Vestibulum lorem purus, ultrices sit amet tempor non, molestie fringilla elit. Donec tortor nulla, feugiat efficitur eros bibendum, consequat consectetur ligula. Etiam eget pellentesque nulla, sed dapibus nibh. In at arcu dapibus, eleifend velit in, molestie urna. Vestibulum diam lacus, egestas a nibh et, vestibulum dapibus dui. Mauris porta, nunc placerat porttitor dignissim, ipsum tortor auctor risus, vitae suscipit tellus quam eget ex. Aenean posuere vulputate tincidunt. Ut aliquam justo at quam blandit, et rutrum augue varius. Integer aliquet justo eget finibus scelerisque. Sed feugiat vel quam sit amet hendrerit. Suspendisse interdum tellus metus. Integer maximus condimentum quam, a pulvinar erat elementum eget. Phasellus scelerisque elit quam, sed ultricies tellus varius non. Maecenas libero urna, imperdiet at mauris sed, elementum scelerisque eros. Phasellus vitae blandit erat.",
      "initial": "{{initial}}",
      "workspaces": "All workspaces",
      "members": "{{member}} member",
      "headers": [
        "Name",
        "Sources",
        "Created",
        "Edited"
      ],
      "newsletter": "Newsletter",
      "title": "Newsletter {{separator}} {{title}}",
      "lipSync": "Lip Sync",
      "dragDrop": "Drag and drop a video file here or click to upload",
      "maxSize": "max size : 1GB",
      "upload": "Upload",
      "percentage": "{{percentage}}% Uploaded",
      "settings": "Settings",
      "help": "Help",
      "ready": "Video Ready",
      "create": "Create a newsletter",
      "name": "Name of the newsletter",
      "languages": [
        {
          "id": "en",
          "name": "English"
        },
        {
          "id": "fr",
          "name": "French"
        }
      ],
      "sourceHeaders": [
        "Url",
        "Status",
        "Last Updated",
        "Creation Date",
        " ",
        " "
      ],
      "addSource": "Add a Source",
      "sourcePlaceholder": "Url",
      "targetLanguage": "Target Language:",
      "status": [
        {
          "id": "processing",
          "value": "Processing"
        },
        {
          "id": "completed",
          "value": "Completed"
        }
      ]
    },
    "planAndUsage": {
      "plan": "Plan & Usage",
      "paymentMethod": "Payment Methods",
      "recharge": "Auto recharge settings",
      "periods": [
        "Monthly",
        "Yearly"
      ],
      "sideMenuUsage": {
        "title": "Billing & Usage",
        "subTitle": "Credits"
      },
      "mainUsage": {
        "title": "Free Plan - Current month Usage",
        "reset": "Resets on the 1st of each month",
        "date": "{{date}}",
        "credits": "Credits",
        "projects": "Projects"
      },
      "plans": {
        "buy": {
          "plan_1": {
            "id": "plan_1",
            "name": "Basic",
            "description": "Add 100 credit balance",
            "price": "5",
            "total": 100,
            "buttonLabel": "Buy",
            "backgroundColor": "#F9F9FB",
            "icon": "FreeSvg"
          },
          "plan_2": {
            "id": "plan_2",
            "name": "Pro",
            "description": "Add 500 credit balance",
            "price": "20",
            "total": 500,
            "buttonLabel": "Buy",
            "backgroundColor": "#E8FADC",
            "icon": "ProSvg"
          },
          "plan_3": {
            "id": "plan_3",
            "name": "Team",
            "description": "Add 3000 credit balance",
            "price": "100",
            "total": 3000,
            "buttonLabel": "Buy",
            "backgroundColor": "#CDD1FF",
            "icon": "EnterpriseSvg"
          }
        },
        "autoCharge": {
          "plan_1": {
            "id": "plan_1",
            "name": "Basic",
            "description": "Bring 100 credit balance",
            "price": "5",
            "buttonLabel": "Select",
            "backgroundColor": "#F9F9FB",
            "icon": "FreeSvg"
          },
          "plan_2": {
            "id": "plan_2",
            "name": "Pro",
            "description": "Bring 500 credit balance",
            "price": "20",
            "buttonLabel": "Select",
            "backgroundColor": "#E8FADC",
            "icon": "ProSvg"
          },
          "plan_3": {
            "id": "plan_3",
            "name": "Team",
            "description": "Bring 3000 credit balance",
            "price": "100",
            "buttonLabel": "Select",
            "backgroundColor": "#CDD1FF",
            "icon": "EnterpriseSvg"
          }
        }
      },
      "onDemandPlan": {
        "name": "On Demand",
        "description": "Best for on demand usage",
        "price": "${{price}} / usage",
        "features": [
          "Advantage 1",
          "Advantage 2"
        ],
        "buttonLabel": "Add Your Card"
      },
      "monthly": "Monthly",
      "yearly": "Yearly",
      "features": "Features"
    },
    "settings": {
      "info": "Settings",
      "tabs": {
        "profile": "Profile",
        "aiModels": "AI Models",
        "interface": "Interface",
        "notifications": "Notifications",
        "privacy": "Privacy"
      },
      "aiModel": {
        "textGeneration": "Text Generation",
        "imageGeneration": "Image Generation",
        "videoGeneration": "Video Generation",
        "audioGeneration": "Audio Generation",
        "model": "Model",
        "provider": "Provider",
        "temperature": "Temperature"
      },
      "interface": {
        "appearance": "Appearance",
        "language": "Language",
        "theme": "Theme",
        "behavior": "Behavior",
        "autoSave": "Auto-save content while editing"
      },
      "notifications": {
        "emailNotifications": "Email Notifications",
        "receiveEmails": "Receive email notifications",
        "marketingEmails": "Receive marketing emails",
        "appNotifications": "App Notifications",
        "pushNotifications": "Enable push notifications",
        "creditAlerts": "Receive credit balance alerts"
      }
    },
    "dashboard": {
      "title": "Dashboard",
      "activityFeed": "Activity Feed",
      "noActivities": "No activities found",
      "noSummary": "No activity summary available",
      "activitySummary": "Activity Summary",
      "filters": "Filters",
      "resetFilters": "Reset",
      "actionTypes": "Action Types",
      "entityTypes": "Entity Types",
      "listView": "List",
      "summaryView": "Summary",
      "calendarView": "Calendar",
      "totalActivities": "Total Activities",
      "lastLogin": "Last Login",
      "mostCommonAction": "Most Common Action",
      "never": "Never",
      "none": "None",
      "loading": "Loading...",
      "refresh": "Refresh"
    },
    "notifications": {
      "title": "Notifications",
      "empty": "You have no notifications",
      "loginRequired": "Please log in to view your notifications",
      "markAllAsRead": "Mark all as read",
      "deleteAll": "Delete all",
      "deleted": "Notification deleted",
      "allMarkedAsRead": "All notifications marked as read",
      "allDeleted": "All notifications deleted",
      "filters": {
        "status": "Status",
        "all": "All",
        "read": "Read",
        "unread": "Unread",
        "type": "Type",
        "info": "Information",
        "success": "Success",
        "warning": "Warning",
        "error": "Error",
        "system": "System"
      },
      "privacy": {
        "dataSharing": "Data Sharing",
        "allowDataSharing": "Allow data sharing to improve services",
        "dataSharingDescription": "We use your data to improve our services and provide a better experience. Your data is never sold to third parties.",
        "analytics": "Analytics",
        "allowAnalytics": "Allow analytics tracking",
        "analyticsDescription": "We collect anonymous usage data to improve our services. This helps us understand how you use the app.",
        "dangerZone": "Danger Zone",
        "deleteAccount": "Delete Account",
        "deleteAccountDescription": "Permanently delete your account and all associated data. This action cannot be undone.",
        "confirmDeleteTitle": "Confirm Account Deletion",
        "confirmDeleteMessage": "Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.",
        "confirmDeleteWarning": "Warning: This action is permanent and cannot be undone.",
        "confirmDeleteDataLoss": "All your data, including profile information, preferences, and content will be permanently deleted.",
        "confirmDeleteFinal": "To confirm deletion, please enter your password and type \"DELETE\" in the field below.",
        "typeDelete": "Type DELETE to confirm:"
      }
    },
    "application": {
      "application": "Application",
      "fileDetails": "{{name}} {{separator}} {{fileName}}",
      "create": "Create a application",
      "placeholder": "Name of the application",
      "descrip": "Description du application",
      "labelGrant": "Select your grant",
      "labelProject": "Select your project",
      "apply": "Apply for An Application"
    },
    "tableHeader": [
      "Name",
      "Created",
      "Edited",
      " ",
      " "
    ],
    "tableFileHeader": [
      "Url",
      "Created",
      "Edited",
      " ",
      " "
    ],
    "project": {
      "element": {
        "failedToGenerate": {
          "title": "Failed to generate: {{category}}",
          "description": "Would you like to regenerate it ?"
        },
        "impossibleToGenerate": {
          "title": "Impossible to generate: {{category}}",
          "description": "Use an other model  :"
        }
      },
      "customer": {
        "header": "Customers",
        "actionCreate": "Create a Customer"
      },
      "product": {
        "header": "Products",
        "actionCreate": "Create a Product"
      },
      "project": "Project",
      "create": "Create a project",
      "placeholder": "Name of the project",
      "descrip": "Description du projet",
      "titles": [
        "Enter your project name",
        "Describe your Project",
        "Generating Project"
      ],
      "generate": "Generating Project",
      "projectName": "Project Name",
      "sections": [
        {
          "id": "csect123",
          "title": "Description du Projet",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod"
        },
        {
          "id": "csect1234",
          "title": "Avantages",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod"
        },
        {
          "id": "csect1235",
          "title": "Fonctionnement",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod"
        },
        {
          "id": "csect1236",
          "title": "Pricing",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod"
        },
        {
          "id": "csect1237",
          "title": "Cas d'utilisation",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod"
        }
      ]
    },
    "grant": {
      "addQuestion": "Add a question",
      "addSection": "ADD SECTION",
      "section": "Section",
      "question": "Question",
      "description": "Description",
      "name": "Name",
      "secName": "Section Name",
      "secDes": "Section Description",
      "quesName": "Question",
      "quesDes": "Question Description",
      "sectionDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod",
      "questionDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod",
      "prompts": [
        {
          "id": "1",
          "prompt": "Add a meme",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod et sodales vehicula, pretium nec leo. Maecenas condimentum enim ut viverra ornare. Nulla sit amet lobortis justo. Vestibulum at rhoncus metus, at aliquet justo. Morbi vulputate eros at dui aliquam, in interdum lectus hendrerit. Aenean tristique pharetra dolor sed aliquam. Donec quis purus a turpis auctor mollis. Ut sodales fermentum mauris, et consequat dui imperdiet ac. Nunc nec libero quis est molestie ultricies eget et nulla. Vivamus feugiat mauris ultricies mi suscipit, non tincidunt lacus imperdiet. Ut vitae lacus in arcu volutpat rutrum. Maecenas ut euismod arcu. Maecenas quis lacus finibus, tristique elit nec, tempor est. Curabitur tincidunt, nisl in vulputate ultricies, tortor tortor faucibus sem, dapibus euismod ligula dolor eget tellus. Integer laoreet commodo ultrices. Suspendisse rutrum tempus volutpat. Donec et malesuada sem, vel fringilla augue. Nulla ornare auctor imperdiet. Vivamus suscipit purus at lorem vestibulum vestibulum. Praesent aliquet pharetra nibh id feugiat. Pellentesque pulvinar ultricies elit vel auctor. Quisque maximus mauris eget arcu tristique iaculis vel non odio. Aenean eu scelerisque tellus. Pellentesque blandit aliquam tortor ut semper. Etiam consectetur eros mi, eu mattis sem facilisis ac. Curabitur rutrum turpis ut maximus elementum. Suspendisse vitae magna aliquam, vestibulum justo mollis, dignissim elit. Morbi diam lacus, pretium vitae nisl sit amet, posuere maximus lorem. Integer ultricies tempor malesuada. Aenean in porttitor mi. Pellentesque sodales augue non odio venenatis, vitae auctor sem porta. Praesent congue auctor dui rhoncus tincidunt. Fusce et dui non orci vestibulum aliquet. Duis condimentum turpis augue, ac elementum libero sagittis non. Donec vel sodales magna, id dapibus lacus. Vestibulum lorem purus, ultrices sit amet tempor non, molestie fringilla elit. Donec tortor nulla, feugiat efficitur eros bibendum, consequat consectetur ligula. Etiam eget pellentesque nulla, sed dapibus nibh. In at arcu dapibus, eleifend velit in, molestie urna. Vestibulum diam lacus, egestas a nibh et, vestibulum dapibus dui. Mauris porta, nunc placerat porttitor dignissim, ipsum tortor auctor risus, vitae suscipit tellus quam eget ex. Aenean posuere vulputate tincidunt. Ut aliquam justo at quam blandit, et rutrum augue varius. Integer aliquet justo eget finibus scelerisque. Sed feugiat vel quam sit amet hendrerit. Suspendisse interdum tellus metus. Integer maximus condimentum quam, a pulvinar erat elementum eget. Phasellus scelerisque elit quam, sed ultricies tellus varius non. Maecenas libero urna, imperdiet at mauris sed, elementum scelerisque eros. Phasellus vitae blandit erat."
        },
        {
          "id": "2",
          "prompt": "Be funnier",
          "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod et sodales vehicula, pretium nec leo. Maecenas condimentum enim ut viverra ornare. Nulla sit amet lobortis justo. Vestibulum at rhoncus metus, at aliquet justo. Morbi vulputate eros at dui aliquam, in interdum lectus hendrerit. Aenean tristique pharetra dolor sed aliquam. Donec quis purus a turpis auctor mollis. Ut sodales fermentum mauris, et consequat dui imperdiet ac. Nunc nec libero quis est molestie ultricies eget et nulla. Vivamus feugiat mauris ultricies mi suscipit, non tincidunt lacus imperdiet. Ut vitae lacus in arcu volutpat rutrum. Maecenas ut euismod arcu. Maecenas quis lacus finibus, tristique elit nec, tempor est. Curabitur tincidunt, nisl in vulputate ultricies, tortor tortor faucibus sem, dapibus euismod ligula dolor eget tellus. Integer laoreet commodo ultrices. Suspendisse rutrum tempus volutpat. Donec et malesuada sem, vel fringilla augue. Nulla ornare auctor imperdiet. Vivamus suscipit purus at lorem vestibulum vestibulum. Praesent aliquet pharetra nibh id feugiat. Pellentesque pulvinar ultricies elit vel auctor. Quisque maximus mauris eget arcu tristique iaculis vel non odio. Aenean eu scelerisque tellus. Pellentesque blandit aliquam tortor ut semper. Etiam consectetur eros mi, eu mattis sem facilisis ac. Curabitur rutrum turpis ut maximus elementum. Suspendisse vitae magna aliquam, vestibulum justo mollis, dignissim elit. Morbi diam lacus, pretium vitae nisl sit amet, posuere maximus lorem. Integer ultricies tempor malesuada. Aenean in porttitor mi. Pellentesque sodales augue non odio venenatis, vitae auctor sem porta. Praesent congue auctor dui rhoncus tincidunt. Fusce et dui non orci vestibulum aliquet. Duis condimentum turpis augue, ac elementum libero sagittis non. Donec vel sodales magna, id dapibus lacus. Vestibulum lorem purus, ultrices sit amet tempor non, molestie fringilla elit. Donec tortor nulla, feugiat efficitur eros bibendum, consequat consectetur ligula. Etiam eget pellentesque nulla, sed dapibus nibh. In at arcu dapibus, eleifend velit in, molestie urna. Vestibulum diam lacus, egestas a nibh et, vestibulum dapibus dui. Mauris porta, nunc placerat porttitor dignissim, ipsum tortor auctor risus, vitae suscipit tellus quam eget ex. Aenean posuere vulputate tincidunt. Ut aliquam justo at quam blandit, et rutrum augue varius. Integer aliquet justo eget finibus scelerisque. Sed feugiat vel quam sit amet hendrerit. Suspendisse interdum tellus metus. Integer maximus condimentum quam, a pulvinar erat elementum eget. Phasellus scelerisque elit quam, sed ultricies tellus varius non. Maecenas libero urna, imperdiet at mauris sed, elementum scelerisque eros. Phasellus vitae blandit erat."
        }
      ],
      "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod et sodales vehicula, pretium nec leo. Maecenas condimentum enim ut viverra ornare. Nulla sit amet lobortis justo. Vestibulum at rhoncus metus, at aliquet justo. Morbi vulputate eros at dui aliquam, in interdum lectus hendrerit. Aenean tristique pharetra dolor sed aliquam. Donec quis purus a turpis auctor mollis. Ut sodales fermentum mauris, et consequat dui imperdiet ac. Nunc nec libero quis est molestie ultricies eget et nulla. Vivamus feugiat mauris ultricies mi suscipit, non tincidunt lacus imperdiet. Ut vitae lacus in arcu volutpat rutrum. Maecenas ut euismod arcu. Maecenas quis lacus finibus, tristique elit nec, tempor est. Curabitur tincidunt, nisl in vulputate ultricies, tortor tortor faucibus sem, dapibus euismod ligula dolor eget tellus. Integer laoreet commodo ultrices. Suspendisse rutrum tempus volutpat. Donec et malesuada sem, vel fringilla augue. Nulla ornare auctor imperdiet. Vivamus suscipit purus at lorem vestibulum vestibulum. Praesent aliquet pharetra nibh id feugiat. Pellentesque pulvinar ultricies elit vel auctor. Quisque maximus mauris eget arcu tristique iaculis vel non odio. Aenean eu scelerisque tellus. Pellentesque blandit aliquam tortor ut semper. Etiam consectetur eros mi, eu mattis sem facilisis ac. Curabitur rutrum turpis ut maximus elementum. Suspendisse vitae magna aliquam, vestibulum justo mollis, dignissim elit. Morbi diam lacus, pretium vitae nisl sit amet, posuere maximus lorem. Integer ultricies tempor malesuada. Aenean in porttitor mi. Pellentesque sodales augue non odio venenatis, vitae auctor sem porta. Praesent congue auctor dui rhoncus tincidunt. Fusce et dui non orci vestibulum aliquet. Duis condimentum turpis augue, ac elementum libero sagittis non. Donec vel sodales magna, id dapibus lacus. Vestibulum lorem purus, ultrices sit amet tempor non, molestie fringilla elit. Donec tortor nulla, feugiat efficitur eros bibendum, consequat consectetur ligula. Etiam eget pellentesque nulla, sed dapibus nibh. In at arcu dapibus, eleifend velit in, molestie urna. Vestibulum diam lacus, egestas a nibh et, vestibulum dapibus dui. Mauris porta, nunc placerat porttitor dignissim, ipsum tortor auctor risus, vitae suscipit tellus quam eget ex. Aenean posuere vulputate tincidunt. Ut aliquam justo at quam blandit, et rutrum augue varius. Integer aliquet justo eget finibus scelerisque. Sed feugiat vel quam sit amet hendrerit. Suspendisse interdum tellus metus. Integer maximus condimentum quam, a pulvinar erat elementum eget. Phasellus scelerisque elit quam, sed ultricies tellus varius non. Maecenas libero urna, imperdiet at mauris sed, elementum scelerisque eros. Phasellus vitae blandit erat.",
      "initial": "{{initial}}",
      "workspaces": "Ai {{name}}",
      "members": "{{member}} member",
      "headers": [
        "Name",
        "Created",
        "Edited",
        " ",
        " "
      ],
      "grant": "Grant",
      "headerStatus": "Status",
      "project": "Project",
      "title": "Grant",
      "lipSync": "Lip Sync",
      "maxSize": "max size : 1GB",
      "upload": "Upload",
      "uploadFile": "Upload a File",
      "percentage": "{{percentage}}% Uploaded",
      "settings": "Settings",
      "help": "Help",
      "url": "Url",
      "apply": "Apply for a Grant",
      "subtitle": "Enter the Grant Name",
      "created": "Created",
      "edited": "Edited",
      "ready": "Video Ready",
      "create": "Create a grant",
      "titleGrant": "Name of the grant",
      "languages": [
        {
          "id": "en",
          "name": "English"
        },
        {
          "id": "fr",
          "name": "French"
        }
      ],
      "sourceHeaders": [
        "Url",
        "Status",
        "Last Updated",
        "Creation Date",
        " ",
        " "
      ],
      "addSource": "Add a Source",
      "sourcePlaceholder": "Url",
      "targetLanguage": "Target Language:",
      "status": [
        {
          "id": "processing",
          "value": "Processing"
        },
        {
          "id": "completed",
          "value": "Completed"
        }
      ]
    },
    "enterprise": {
      "enterprise": "Enterprise",
      "company": "Company"
    },
    "translate": {
      "initial": "{{initial}}",
      "translateVideo": "Translate Video",
      "translation": "Video Translation",
      "translate": "Translate",
      "lipSync": "Lip Sync",
      "dragDrop": "Drag and drop a grant application file here or click to upload",
      "maxSize": "max size : 1GB",
      "upload": "Upload",
      "percentage": "{{percentage}}% Uploaded",
      "settings": "Settings",
      "help": "Help",
      "ready": "Video Ready",
      "languages": [
        {
          "id": "en",
          "name": "English"
        },
        {
          "id": "fr",
          "name": "French"
        }
      ],
      "targetLanguage": "Target Language:",
      "status": [
        {
          "id": "processing",
          "value": "Processing"
        },
        {
          "id": "completed",
          "value": "Completed"
        }
      ]
    },
    "event": {
      "participants": "Participants",
      "trends": "Participants trend",
      "numberOfParticipants": "The number of participants over times",
      "details": "Protest details",
      "people": "Number of people who participated",
      "ending": "Ending",
      "status": {
        "ongoing": "Currently demonstrating peoples",
        "upcoming": "Number of people who will participate",
        "ended": "Number of people who participated",
        "unknown": ""
      },
      "mapButtonText": "Where are they protesting from?"
    },
    "home": {
      "live": "Live",
      "all": "All",
      "ongoing": "Ongoing",
      "upcoming": "Upcoming",
      "ended": "Ended"
    },
    "login": {
      "welcome": "Welcome",
      "haveAccount": "Don’t you have an account?",
      "signUp": "Sign up",
      "forgotPassword": "Forget password"
    },
    "register": {
      "joinUs": "Join us",
      "signIn": "Sign in",
      "alreadyAccount": "Already have an account?"
    },
    "landing": {
      "interactionEditor": "Please select a button or a link to configure it",
      "contentEditor": "Please select a Text",
      "title": "Landing Pages",
      "offer": "Choose Offer",
      "chat": {
        "header": "Demo App"
      },
      "footer": [
        "Services",
        "Pricing",
        "About",
        "Testimonials",
        "Careers"
      ],
      "hero": {
        "pretitle": "Elevate Your Social Media Presence with",
        "surtitle": "SocialSpark",
        "button": "Get Started Today",
        "subtitle": "Reach new heights with authentic, rapid follower growth."
      }
    }
  },
  "modal": {
    "logout": {
      "title": "Logout Confirmation",
      "description": "Are you sure you want to logout? You will be logged out of your account."
    },
    "mustConnect": {
      "title": "Restricted Feature",
      "message": "Please log in \nto use this feature?",
      "login": "Log In",
      "register": "Register"
    },
    "insufficientCredit": {
      "title": "Insufficient Credits",
      "description": "You do not have enough credits. Would you like to buy more credits ?",
      "confirm": "Buy Credits",
      "minimum": "minmum costs"
    },
    "confirmPurchase": {
      "title": "Confirm payment",
      "description": "You are about to purchase :",
      "conditions": "By continuing you agree to our service credit terms. Paid credits are non-refundable and expire one year from purchase date.",
      "confirm": "Purchase"
    },
    "worker": {
      "title": "Choose an Ai Worker to add to your team :"
    },
    "fakeFeature": {
      "title": "✨ Coming Soon: Create a {{featureName}} ",
      "description": "We're always striving to enhance your experience. We're considering adding a new feature {{featureName}} that will allow you to {{description}}"
    },
    "addComponent": {
      "header": "Add a component"
    },
    "listProjectModal": {
      "header": "Select a project",
      "addProject": "Create a Project"
    },
    "offer": {
      "describe": "Describe your new offer"
    },
    "customer": {
      "describe": "Describe your new customer"
    },
    "project": {
      "customer": {
        "idealCustomer": "Choose your Ideal Customer",
        "defineCustomer": "Define your ideal Customer"
      },
      "loader": {
        "text": "Analyzing Project"
      }
    },
    "chat": "How do you want this to change?",
    "model": {
      "title": "Choose your Model"
    },
    "documentPreview": {
      "title": "Document Preview",
      "loading": "Loading document...",
      "download": "Download"
    },
    "feedbackRating": {
      "title": "Rate Your Experience",
      "subtitle": "How would you rate your experience with our service?",
      "feedbackPlaceholder": "Share your thoughts with us (optional)",
      "submit": "Submit",
      "cancel": "Cancel",
      "dontShowAgain": "Don't show again",
      "rating1": "1 out of 5",
      "rating2": "2 out of 5",
      "rating3": "3 out of 5",
      "rating4": "4 out of 5",
      "rating5": "5 out of 5"
    },
    "quickAdd": {
      "title": "Quick Add",
      "submit": "Add",
      "cancel": "Cancel",
      "back": "Back",
      "selectOption": "Select an option"
    },
    "socialShare": {
      "title": "Share",
      "subtitle": "Share this content with your network",
      "copyLink": "Copy",
      "linkCopied": "Link copied to clipboard!"
    },
    "systemPermission": {
      "allow": "Allow",
      "deny": "Deny",
      "privacyNote": "Your privacy is important to us. We only request permissions that are necessary for the app to function properly.",
      "camera": {
        "title": "Camera Access",
        "subtitle": "This app needs access to your camera to take photos and videos."
      },
      "microphone": {
        "title": "Microphone Access",
        "subtitle": "This app needs access to your microphone to record audio."
      },
      "location": {
        "title": "Location Access",
        "subtitle": "This app needs access to your location to provide location-based services."
      },
      "notifications": {
        "title": "Notification Access",
        "subtitle": "This app needs permission to send you notifications for important updates."
      },
      "storage": {
        "title": "Storage Access",
        "subtitle": "This app needs access to your device storage to save files."
      }
    },
    "whatsNew": {
      "title": "What's New",
      "subtitle": "Check out the latest updates and improvements",
      "version": "Version",
      "dontShowAgain": "Don't show again",
      "close": "Close",
      "new": "New",
      "improvement": "Improvement",
      "fix": "Fix"
    },
    "genericConfirm": {
      "title": "Confirmation",
      "message": "Are you sure you want to perform this action?",
      "confirm": "Confirm",
      "cancel": "Cancel"
    }
  },
  "component": {
    "video": {
      "error": "Your browser does not support the video tag.",
      "uploadFile": "Upload a File",
      "maxSize": "Max — Video: 1GB / Image: 10MB",
      "upload": "Upload"
    },
    "sideMenu": {
      "text": "Model: {{modelName}}",
      "headerText": "My Account"
    },
    "failedSection": {
      "headline": "Failed To create",
      "subheadline": "Would you like to recreate it ?"
    },
    "denied": {
      "headline": "Impossible to create"
    },
    "chatBot": {
      "title": "AI Assistant",
      "placeholder": "Ask your question...",
      "defaultBotMessage": "I am a demo chatbot. I can't really answer your questions yet.",
      "agentConversation": "How can I assist you today? Let's set up your agent.",
      "starters": {
        "spanish": {
          "title": "Spanish Language Assistant",
          "subtitle": "I'm your Spanish language teacher. Choose a topic below to start learning, or ask me anything about Spanish language and culture.",
          "customPrompt": "Or type your own question",
          "starter1": {
            "title": "Basic Spanish Greetings",
            "description": "Can you teach me some basic Spanish greetings and how to introduce myself?"
          },
          "starter2": {
            "title": "Spanish Verb Conjugation",
            "description": "I'm struggling with Spanish verb conjugation. Can you explain how it works with some examples?"
          },
          "starter3": {
            "title": "Spanish Culture",
            "description": "Tell me about Spanish culture and traditions. What are some important cultural aspects I should know?"
          },
          "starter4": {
            "title": "Travel Phrases",
            "description": "I'm traveling to Spain next month. What are some essential phrases I should know?"
          }
        }
      },
      "conversationList": {
        "title": "Conversations",
        "newConversation": "New Conversation",
        "noConversations": "No conversations yet"
      },
      "deleteChat": {
        "title": "Delete conversation",
        "message": "Are you sure you want to delete the entire conversation?",
        "confirm": "Delete",
        "cancel": "Cancel"
      },
      "settings": {
        "title": "Chat Settings",
        "apiKey": "API Key",
        "model": "Model",
        "temperature": "Temperature",
        "maxTokens": "Max Tokens",
        "streamResponse": "Stream Response",
        "save": "Save Settings",
        "cancel": "Cancel",
        "setting": "Settings",
        "theme": "Theme",
        "light": "Light",
        "dark": "Dark",
        "deleteChat": "Delete conversation"
      },
      "form": {
        "messagePlaceholder": "Type your message...",
        "send": "Send"
      },
      "typing": "AI is typing...",
      "validation": {
        "apiKeyRequired": "API Key is required",
        "modelRequired": "Model is required",
        "temperatureRange": "Temperature must be between 0 and 2",
        "maxTokensRange": "Max tokens must be between 1 and 4096",
        "messageRequired": "Message cannot be empty"
      }
    }
  },
  "navigation": {
    "login": "Login",
    "register": "Register"
  },
  "header": {
    "menu": {
      "services": "Services",
      "pricing": "Pricing",
      "faqs": "FAQs",
      "testimonials": "Testimonials"
    }
  },
  "form": {
    "profile": {
      "bio": {
        "label": "Bio",
        "placeholder": "Tell us about yourself..."
      },
      "job": {
        "label": "Job Title",
        "placeholder": "Your profession or job title"
      },
      "country": {
        "label": "Country",
        "placeholder": "Your country"
      },
      "gender": {
        "label": "Gender",
        "placeholder": "Select your gender",
        "options": {
          "male": "Male",
          "female": "Female",
          "other": "Other",
          "preferNotToSay": "Prefer not to say"
        }
      },
      "instagram": {
        "label": "Instagram",
        "placeholder": "Your Instagram username"
      },
      "facebook": {
        "label": "Facebook",
        "placeholder": "Your Facebook profile"
      }
    },
    "createConfigForm": {
      "title": "Create a Form",
      "name": {
        "label": "Name",
        "placeholder": "Lorem ipsum dolor sit amet"
      },
      "description": {
        "label": "Description du formulaire",
        "placeholder": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vulputate, augue ut aliquet ultrices, orci magna ornare urna, eget interdum ex enim in purus. Nam volutpat eros ligula, eu tempor mauris varius a. Morbi sit amet tortor ut augue luctus sollicitudin. Quisque ipsum quam, euismod et sodales vehicula, pretium nec leo. Maecenas condimentum enim ut viverra ornare. Nulla sit amet lobortis justo. Vestibulum at rhoncus metus, at aliquet justo. Morbi vulputate eros at dui aliquam, in interdum lectus hendrerit. Aenean tristique pharetra dolor sed aliquam. Donec quis purus a turpis auctor mollis. Ut sodales fermentum mauris, et consequat dui imperdiet ac. Nunc nec libero quis est molestie ultricies eget et nulla. Vivamus feugiat mauris ultricies mi suscipit, non tincidunt lacus imperdiet."
      }
    },
    "chat": "How do you want this to change?",
    "name": {
      "label": "Name",
      "placeholder": "Enter your name",
      "required": "Name is mandatory",
      "format": "Name is invalid"
    },
    "firstName": {
      "label": "First Name",
      "placeholder": "Enter your first name",
      "required": "First Name is required",
      "format": "First Name is invalid"
    },
    "lastName": {
      "label": "Last Name",
      "placeholder": "Enter your last name",
      "required": "Last Name is required",
      "format": "Last Name is invalid"
    },
    "location": {
      "label": "Location",
      "placeholder": "Enter your location",
      "required": "Location is required",
      "format": "Location is invalid"
    },
    "slogan": {
      "label": "Please write your slogan/message to the world",
      "placeholder": "Enter your slogan",
      "required": "Slogan is required",
      "format": "Slogan is invalid"
    },
    "anonymous": {
      "label": "Anonymous",
      "remain": "Remain anonymous",
      "required": "Anonymous is required",
      "format": "Anonymous is invalid"
    },
    "age": {
      "label": "Age",
      "placeholder": "Enter your age",
      "required": "Age is required",
      "format": "Age is invalid"
    },
    "gender": {
      "label": "Gender",
      "placeholder": "Enter your gender",
      "required": "Gender is required",
      "format": "Gender is invalid",
      "values": {
        "man": "Man",
        "woman": "Woman"
      }
    },
    "email": {
      "label": "Email",
      "placeholder": "<EMAIL>",
      "required": "Email is required",
      "format": "Email is invalid"
    },
    "password": {
      "label": "Password",
      "placeholder": "******",
      "required": "Password required",
      "short": "Password too short",
      "match": "Passwords doesn't match"
    },
    "message": {
      "label": "Message",
      "placeholder": "Type your message...",
      "required": "Message is required"
    },
    "address": {
      "label": "Address",
      "placeholder": "Enter your address",
      "required": "Address required",
      "format": "Address is invalid"
    },
    "SIRETNumber": {
      "label": "Registration Number",
      "placeholder": "Enter your Registration Number",
      "required": "Registration Number required"
    },
    "phone": {
      "label": "Phone Number",
      "placeholder": "Enter your phone number",
      "required": "Phone Number required"
    },
    "urlPath": {
      "label": "Page URL",
      "placeholder": "Enter your page URL",
      "required": "Page URL required"
    },
    "enterpriseName": {
      "label": "Name",
      "placeholder": "Enter your enterprise name",
      "required": "Name is mandatory",
      "format": "Name is invalid"
    },
    "date": {
      "label": "Creation Date",
      "placeholder": "Enter the creation date",
      "required": "Creation Date is mandatory"
    },
    "birthdate": {
      "label": "Birthdate",
      "placeholder": "Enter your birthdate",
      "required": "Birthdate is mandatory"
    }
  },
  "button": {
    "join": "Join Now",
    "login": "Sign in",
    "leave": "Leave",
    "save": "Save",
    "back": "Back",
    "viewProfile": "View Profile",
    "register": "Create an account",
    "here": "I am still here",
    "uploadImage": "Upload image",
    "backtoHome": "Back to home",
    "updateAccount": "Save",
    "saveSettings": "Save Settings",
    "deleteAccount": "Delete Account",
    "confirmDelete": "Yes, Delete My Account",
    "continueDelete": "Continue",
    "permanentlyDeleteAccount": "Permanently Delete Account",
    "updateProfile": "Update Profile",
    "createProtest": "Create a new protest",
    "addSource": "Add a source",
    "add": "Add",
    "deactivate": "Desactivate",
    "remove": "Remove",
    "generate": "Generate",
    "activate": "Activate",
    "edit": "Edit Sources",
    "editNews": "Edit Newletter",
    "regenerate": "Regenerate",
    "create": "Create",
    "logout": "Sign Out",
    "account": "Account",
    "buy": "Acheter",
    "cancel": "Cancel",
    "signUp": "Sign Up"
  },
  "format": {
    "date": "MM/DD/YYYY",
    "day": "DD MMMM YYYY",
    "time": "hh:mm",
    "datetime": "MM/DD/YYYY - hh:mm"
  },
  "error": {
    "error": "Error",
    "warning": "Warning",
    "info": "Info",
    "network": "No internet connection"
  },
  "action": {
    "logout": "Logout",
    "retry": "Retry",
    "ok": "Ok",
    "confirm": "Confirm",
    "cancel": "Cancel",
    "close": "Close",
    "yes": "yes",
    "no": "no",
    "save": "Save",
    "send": "Send",
    "validate": "Validate",
    "edit": "Edit",
    "remove": "Supprimer",
    "accept": "Accept",
    "decline": "Decline",
    "prev": "Prev",
    "next": "Next",
    "submit": "submit",
    "publish": "Publish",
    "generate": "Generate",
    "delete": "Delete",
    "back": "Back",
    "notInterested": "Not Interested",
    "interested": "Yes, I'm Interested!",
    "cart": "Add to Cart",
    "buy": "Acheter",
    "signUp": "Sign Up"
  },
  "planPage": {
    "paymentType": "credit",
    "options": {
      "creditPack1": {
        "id": "creditPack1",
        "name": "Small Pack",
        "description": "20 Credits for image generation",
        "price": "5",
        "currency": "usd",
        "interval": null,
        "credits": "20",
        "buttonLabel": "Buy 20 Credits"
      },
      "creditPack2": {
        "id": "creditPack2",
        "name": "Medium Pack",
        "description": "50 Credits for image generation",
        "price": "10",
        "currency": "usd",
        "interval": null,
        "credits": "50",
        "buttonLabel": "Buy 50 Credits"
      }
    }
  },
  "workflowPage": {
    "title": {}
  },
  "ImageGeneratorHome": {},
  "ImageDetailsPage": {
    "BackButton": {
      "back_to_images": {
        "textId": "back",
        "text": "Back to Images"
      }
    }
  },
  "ImageGalleryScreen": {
    "MediaUploaderElement": {
      "uploadImageButton": {
        "textId": "upload_button_text",
        "text": "Upload Image"
      }
    },
    "ImageListComponent": {
      "image_entry": {
        "textId": "created_at_label",
        "text": "Created At:"
      }
    }
  },
  "UpscaledImageDetails": {
    "ImageCreatedAt": {
      "undefined": {
        "createdAtText": "Created at:"
      }
    }
  },
  "ImageUploadAndList": {},
  "EditedImageScreen": {
    "DetailsContainer": {
      "undefined": {
        "textId": "topicLabel",
        "text": "Topic:"
      }
    },
    "UserInformation": {
      "undefined": {
        "textId": "creationDateLabel",
        "text": "Creation Date:"
      }
    }
  },
  "homeRoutes": {
    "browseBlogPosts": "Browse Blog Posts",
    "generateBlogPost": "Generate Blog Post",
    "purchaseCredits": "Purchase Credits",
    "chatWithAi": "Chat with AI"
  },
  "activity": {
    "title": "Activity Log",
    "description": "Timestamped history of your actions on the application",
    "no_logs": "No activity has been recorded",
    "load_more": "Load more activities",
    "entity": "Entity",
    "action": {
      "create": "Create",
      "read": "View",
      "update": "Edit",
      "delete": "Delete",
      "login": "Login",
      "logout": "Logout",
      "upload": "Upload",
      "download": "Download",
      "share": "Share",
      "comment": "Comment",
      "like": "Like",
      "payment": "Payment",
      "subscription": "Subscription",
      "settings_change": "Settings change",
      "other": "Other action"
    }
  }
};

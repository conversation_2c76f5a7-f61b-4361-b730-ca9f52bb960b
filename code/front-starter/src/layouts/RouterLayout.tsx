import React from 'react';
import { Navigate, Route, Routes, useLocation } from 'react-router-dom';
import { AccessControl } from 'src/components/AccessControl';
import { SetupWizardCheck } from 'src/components/SetupWizardCheck';
import { OnboardingCheck } from 'src/components/OnboardingCheck';
import { RouterConfig } from '../configs/RouterConfig';
import ChatBotPage from 'src/pages/chatbot/chatbot.page';
import ChatBotPageLayout from './ChatBotPageLayout';

// Legal pages
import LegalLayout from 'src/pages/legal/legal.layout';
import TermsPage from 'src/pages/legal/terms.page';
import PrivacyPage from 'src/pages/legal/privacy.page';

// Error pages
import NotFoundPage from 'src/pages/error/not-found.page';
import ServerErrorPage from 'src/pages/error/server-error.page';
import MaintenancePage from 'src/pages/error/maintenance.page';

export const RouterLayout = ({}) => {
  const location = useLocation();
  return (
    <Routes location={location}>
      <Route element={<OnboardingCheck />}>
        <Route path="/" element={<Navigate to="/auth/login" replace />} />
        {RouterConfig.map((item: any, index) => {
          return item.requiresAccess ? (
            <Route key={index} path={item.path} element={<AccessControl />}>
              <Route index element={item.element} />
            </Route>
          ) : (
            <Route key={index} path={item.path} element={item.element} />
          );
        })}
        <Route path="/chatbot" element={<ChatBotPageLayout />}>
          <Route index element={<ChatBotPage />} />
          <Route path=":id" element={<ChatBotPage />} />
        </Route>
      </Route>
      <Route element={<SetupWizardCheck />}>
        <Route path="/setup" element={<Navigate to="/setup" replace />} />
      </Route>

      {/* Legal pages */}
      <Route element={<OnboardingCheck />}>
        <Route path="/legal" element={<LegalLayout />}>
          <Route index element={<Navigate to="/legal/terms" replace />} />
          <Route path="terms" element={<TermsPage />} />
          <Route path="privacy" element={<PrivacyPage />} />
        </Route>
      </Route>

      {/* Error pages */}
      <Route path="/error/404" element={<NotFoundPage />} />
      <Route path="/error/500" element={<ServerErrorPage />} />
      <Route path="/maintenance" element={<MaintenancePage />} />

      {/* Catch-all route for 404 */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

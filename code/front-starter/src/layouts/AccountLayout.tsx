import React from 'react';
import { Horizontal, Vertical, View } from 'app-studio';
import { Link } from '@app-studio/web';
import { useLocation } from 'react-router-dom';
import i18n from 'src/utils/locale';
import { H2 } from 'src/components/Text';
import PageLayout from './PageLayout';

interface MenuItemProps {
  to: string;
  label: string;
  active: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({ to, label, active }) => (
  <Link to={to} style={{ textDecoration: 'none' }}>
    <View
      padding={10}
      borderRadius={8}
      backgroundColor={active ? '#f0f0f0' : 'transparent'}
      color={active ? '#000' : '#666'}
      fontWeight={active ? 'bold' : 'normal'}
      _hover={{ backgroundColor: '#f0f0f0' }}
      width="100%"
    >
      {label}
    </View>
  </Link>
);

export const AccountLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();
  const path = location.pathname;

  return (
    <PageLayout>
      <Vertical width="100%" maxWidth={1200} margin="0 auto" padding={20}>
        <H2 marginBottom={20}>{i18n.t('page.account.info')}</H2>
        <Horizontal width="100%" gap={20} alignItems="flex-start">
          <Vertical width={250} padding={15} borderRadius={8} backgroundColor="#f9f9f9" gap={5}>
            <MenuItem to="/account" label={i18n.t('page.profile.info')} active={path === '/account'} />
            <MenuItem
              to="/account/settings"
              label={i18n.t('page.settings.info')}
              active={path === '/account/settings'}
            />
            <MenuItem
              to="/account/activity-log"
              label={i18n.t('activity.title')}
              active={path === '/account/activity-log'}
            />
            <MenuItem
              to="/account/notifications"
              label={i18n.t('page.notifications.title')}
              active={path === '/account/notifications'}
            />
          </Vertical>
          <View flex={1}>{children}</View>
        </Horizontal>
      </Vertical>
    </PageLayout>
  );
};

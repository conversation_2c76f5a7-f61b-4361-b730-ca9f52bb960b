import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { View } from 'app-studio';
import { Loader, MessageLayout, Modal } from '@app-studio/web';
import { Center, Horizontal, Vertical } from 'src/components/Layout';
import LeftSide from 'src/layouts/LeftLayout';
import PageLayout from 'src/layouts/PageLayout';
import Footer from 'src/components/Footer';

import modals from 'src/modals';

import { useAppStore } from 'src/stores/AppStore';
import { useLocation } from 'react-router';
import { APP_NAME } from 'src/configs/AppConfig';
import { useLocaleStore } from 'src/stores/LocaleStore';
import { GoogleFonts } from 'src/assets/fonts';
import { useAuthStore } from 'src/stores/AuthStore';
import LoadingLayout from './LoadingLayout';
import { NavigationLayout } from './NavigationLayout';
require('react-web-vector-icons/fonts');

const AppLayout = (props: { children: React.ReactNode; online: boolean; status: string }) => {
  const { children, online, status } = props;
  const location = useLocation();

  const { ready, server } = useAppStore(({ ready, server, setServer }) => ({
    ready,
    server,
    setServer,
  }));

  const { language } = useLocaleStore(({ language }) => ({
    language,
  }));

  const { isAuthentificated, loadAccount } = useAuthStore(({ isAuthentificated, checkAccount, loadAccount }) => ({
    isAuthentificated,
    checkAccount,
    loadAccount,
  }));

  const metaprops: any = {
    htmlAttributes: { lang: language },
    title: APP_NAME,
    meta: [
      { charset: 'UTF-8' },
      { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge' },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
    ],
    link: GoogleFonts.map((font) => ({
      href:
        'https://fonts.googleapis.com/css2?family=' +
        font.replace(' ', '+') +
        ':ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap',
      rel: 'stylesheet',
    })).concat([
      {
        rel: 'preconnect',
        href: 'https://fonts.googleapis.com',
      },
      {
        rel: 'preconnect',
        href: 'https://fonts.gstatic.com',
        // crossorigin,
      },
    ]),
  };

  const showLeftSide = location.pathname.indexOf('/auth') >= 0;
  const isWorkflowPage = location.pathname.indexOf('/workflow') >= 0;
  const isTest = location.pathname.indexOf('/test') >= 0;

  const isWebsite = !isAuthentificated || showLeftSide || isWorkflowPage || isTest;

  useEffect(() => {
    if (location.pathname.indexOf('/test') >= 0) return;

    if (isAuthentificated) {
      // getProjectListRequest.run();

      setInterval(() => {
        if (useAuthStore.getState().checkAccount === true) loadAccount();
      }, 3000);
    }
  }, [isAuthentificated]);

  // const spaceApiKey = '1492e646-b54f-4109-8aaa-021d0080bb15';

  // loadSpace(spaceApiKey).then((api) => {
  //   api.init();
  // });
  return (
    <>
      <Helmet {...metaprops} />
      {ready && server && (
        <View width="100vw">
          {isWebsite && (
            <>
              <Horizontal wrap="nowrap">
                {showLeftSide && <LeftSide />}
                <Vertical wrap="nowrap" flex={1} overflowX="hidden">
                  <PageLayout width={showLeftSide ? '50vw' : '100%'} backgroundColor="white">
                    {children}
                  </PageLayout>
                  {!showLeftSide && <Footer />}
                </Vertical>
              </Horizontal>
            </>
          )}
          {!isWebsite && (
            <Vertical width="100vw" backgroundColor="white" height="100vh" maxHeight="100vh" overflow="hidden">
              <Horizontal flex={1} overflow="hidden">
                <NavigationLayout>
                  <PageLayout width={'100%'} backgroundColor="white">
                    {children}
                  </PageLayout>
                </NavigationLayout>
              </Horizontal>
            </Vertical>
          )}
        </View>
      )}
      {!ready && (
        <Center backgroundColor="white" height="100%" width="100%">
          <Loader size={20} />
        </Center>
      )}
      <Modal.Layout modals={modals} />
      {!isTest && <LoadingLayout online={online} status={status} server={server} />}
      <MessageLayout />
    </>
  );
};

export default AppLayout;

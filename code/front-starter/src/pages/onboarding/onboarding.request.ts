import { SetupService } from 'src/services/api';
import { OnboardingStep, ChecklistItem } from './onboarding.types';

export const useOnboardingRequests = ({
  onStepsLoaded,
  onStatusLoaded,
  onProgressUpdated,
  onOnboardingCompleted,
  onChecklistItemUpdated,
}: {
  onStepsLoaded?: (steps: OnboardingStep[]) => void;
  onStatusLoaded?: Function;
  onProgressUpdated?: Function;
  onOnboardingCompleted?: Function;
  onChecklistItemUpdated?: (stepId: string, itemId: string, completed: boolean) => void;
}) => {
  const getStepsRequest = SetupService.useSetupControllerGetSetupStepsService({
    onSuccess: (data) => {
      if (onStepsLoaded) {
        // Convert API response to OnboardingStep[]
        const steps: OnboardingStep[] = data.map((step: any) => ({
          id: step.id,
          title: step.title,
          description: step.description,
          order: step.order,
          type: step.type,
          icon: step.icon,
          checklistItems: step.checklistItems?.map((item: any) => ({
            id: item.id,
            title: item.title,
            description: item.description,
            completed: item.completed || false,
            url: item.url,
          })),
          slideContent: step.slideContent,
          slideImageUrl: step.slideImageUrl,
        }));

        onStepsLoaded(steps);
      }
    },
  });

  const getStatusRequest = SetupService.useSetupControllerGetUserSetupStatusService({
    onSuccess: (data) => {
      if (onStatusLoaded) {
        onStatusLoaded(data);
      }
    },
  });

  const updateProgressRequest = SetupService.useSetupControllerUpdateSetupProgressService({
    onSuccess: (data) => {
      if (onProgressUpdated) {
        onProgressUpdated(data);
      }
    },
  });

  const completeSetupRequest = SetupService.useSetupControllerCompleteSetupService({
    onSuccess: (data) => {
      if (onOnboardingCompleted) {
        onOnboardingCompleted(data);
      }
    },
  });

  const updateChecklistItem = (stepId: string, itemId: string, completed: boolean) => {
    updateProgressRequest.run({
      stepId,
      progress: 0, // Will be calculated on the server
      checklistItemId: itemId,
      checklistItemCompleted: completed,
    });

    if (onChecklistItemUpdated) {
      onChecklistItemUpdated(stepId, itemId, completed);
    }
  };

  return {
    getStepsRequest,
    getStatusRequest,
    updateProgressRequest,
    completeSetupRequest,
    updateChecklistItem,
  };
};

import React from 'react';
import { Vertical, Horizontal, View, Text, Center, Image } from 'app-studio';
import { Button, FormikTextField, FormikTextArea, FormikSelect, FormikForm, Checkbox } from '@app-studio/web';
import { Formik } from 'formik';
import { ProgressBar } from 'src/components/ProgressBar';
import i18n from 'src/utils/locale';
import { SVGIcon } from 'src/components/Icon';
import { OnboardingStepType } from './onboarding.types';

// Slide component for onboarding
export const OnboardingSlide = ({
  title,
  content,
  imageUrl,
  onNext,
  onPrev,
  isFirstStep,
  isLastStep,
}: {
  title: string;
  content: string;
  imageUrl?: string;
  onNext: () => void;
  onPrev: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}) => {
  return (
    <Vertical gap={20} alignItems="center" width="100%" padding={20}>
      <Text fontSize={24} fontWeight="bold">
        {title}
      </Text>

      {imageUrl && (
        <View width="100%" maxWidth={500} marginY={20}>
          <Image src={imageUrl} width="100%" borderRadius={8} />
        </View>
      )}

      <Text textAlign="center" maxWidth={600} marginBottom={20}>
        {content}
      </Text>

      <Horizontal gap={10} justifyContent="space-between" width="100%" maxWidth={500}>
        {!isFirstStep && (
          <Button variant="outline" onClick={onPrev}>
            {i18n.t('onboarding.common.back')}
          </Button>
        )}
        <Button onClick={onNext}>
          {isLastStep ? i18n.t('onboarding.common.finish') : i18n.t('onboarding.common.next')}
        </Button>
      </Horizontal>
    </Vertical>
  );
};

// Checklist component for onboarding
export const OnboardingChecklist = ({
  title,
  items,
  onNext,
  onPrev,
  onItemClick,
  isFirstStep,
  isLastStep,
}: {
  title: string;
  items: any[];
  onNext: () => void;
  onPrev: () => void;
  onItemClick: (itemId: string, completed: boolean) => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}) => {
  return (
    <Vertical gap={20} alignItems="center" width="100%" padding={20}>
      <Text fontSize={24} fontWeight="bold">
        {title}
      </Text>

      <Vertical gap={15} width="100%" maxWidth={600} marginY={20}>
        {items.map((item) => (
          <Horizontal
            key={item.id}
            gap={10}
            padding={15}
            borderRadius={8}
            backgroundColor={item.completed ? 'rgba(0, 200, 0, 0.1)' : 'white'}
            borderWidth={1}
            borderColor={item.completed ? 'rgba(0, 200, 0, 0.5)' : 'rgba(0, 0, 0, 0.1)'}
            alignItems="flex-start"
            _hover={{
              backgroundColor: item.completed ? 'rgba(0, 200, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              cursor: 'pointer',
            }}
            onClick={() => onItemClick(item.id, !item.completed)}
          >
            <Checkbox checked={item.completed} onChange={() => onItemClick(item.id, !item.completed)} />
            <Vertical gap={5}>
              <Text fontWeight="bold">{item.title}</Text>
              <Text fontSize={14} color="rgba(0, 0, 0, 0.6)">
                {item.description}
              </Text>
              {item.url && (
                <Button
                  variant="link"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(item.url, '_blank');
                  }}
                >
                  {i18n.t('onboarding.checklist.viewDetails')}
                </Button>
              )}
            </Vertical>
          </Horizontal>
        ))}
      </Vertical>

      <Horizontal gap={10} justifyContent="space-between" width="100%" maxWidth={600}>
        {!isFirstStep && (
          <Button variant="outline" onClick={onPrev}>
            {i18n.t('onboarding.common.back')}
          </Button>
        )}
        <Button onClick={onNext}>
          {isLastStep ? i18n.t('onboarding.common.finish') : i18n.t('onboarding.common.next')}
        </Button>
      </Horizontal>
    </Vertical>
  );
};

// Form components for profile and preferences
export const ProfileForm = ({ onNext, onPrev, initialValues, onSubmit }: any) => {
  return (
    <Vertical gap={20} alignItems="center" width="100%" padding={20}>
      <Text fontSize={24} fontWeight="bold">
        {i18n.t('setup.profile.title')}
      </Text>
      <Text textAlign="center" maxWidth={600} marginBottom={20}>
        {i18n.t('setup.profile.description')}
      </Text>

      <Formik initialValues={initialValues} onSubmit={onSubmit}>
        {(formikProps) => (
          <FormikForm>
            <Vertical gap={20} width="100%" maxWidth={500}>
              <FormikTextField
                name="name"
                label={i18n.t('setup.profile.nameLabel')}
                placeholder={i18n.t('setup.profile.namePlaceholder')}
              />
              <FormikTextField
                name="job"
                label={i18n.t('setup.profile.jobLabel')}
                placeholder={i18n.t('setup.profile.jobPlaceholder')}
              />
              <FormikSelect
                name="country"
                label={i18n.t('setup.profile.countryLabel')}
                placeholder={i18n.t('setup.profile.countryPlaceholder')}
                options={[
                  { label: 'France', value: 'FR' },
                  { label: 'United States', value: 'US' },
                  { label: 'United Kingdom', value: 'UK' },
                  { label: 'Germany', value: 'DE' },
                  { label: 'Spain', value: 'ES' },
                ]}
              />
              <FormikTextArea
                name="bio"
                label={i18n.t('setup.profile.bioLabel')}
                placeholder={i18n.t('setup.profile.bioPlaceholder')}
              />
              <Horizontal gap={10} justifyContent="space-between" width="100%">
                <Button variant="outline" onClick={onPrev}>
                  {i18n.t('setup.common.back')}
                </Button>
                <Button onClick={formikProps.handleSubmit}>{i18n.t('setup.common.next')}</Button>
              </Horizontal>
            </Vertical>
          </FormikForm>
        )}
      </Formik>
    </Vertical>
  );
};

// Wizard container
export const OnboardingContainer = ({
  children,
  progress,
  currentStep,
  totalSteps,
}: {
  children: React.ReactNode;
  progress: number;
  currentStep: number;
  totalSteps: number;
}) => {
  return (
    <Vertical
      width="100%"
      height="100vh"
      backgroundColor="white"
      alignItems="center"
      justifyContent="center"
      padding={20}
    >
      <Vertical
        width="100%"
        maxWidth={800}
        minHeight={500}
        backgroundColor="white"
        borderRadius={10}
        boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
        padding={20}
        gap={20}
      >
        <Horizontal width="100%" justifyContent="space-between" alignItems="center">
          <Text fontSize={18} fontWeight="bold">
            {i18n.t('onboarding.common.step', { current: currentStep + 1, total: totalSteps })}
          </Text>
          <Text>{progress}%</Text>
        </Horizontal>
        <ProgressBar usageNumber={progress} totalUsage={100} />
        <Center flex={1} width="100%">
          {children}
        </Center>
      </Vertical>
    </Vertical>
  );
};

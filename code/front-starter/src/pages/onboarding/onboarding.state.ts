import { useState } from 'react';
import { OnboardingStep, ChecklistItem } from './onboarding.types';

export const useOnboardingState = () => {
  const [steps, setSteps] = useState<OnboardingStep[]>([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [onboardingData, setOnboardingData] = useState<Record<string, any>>({});
  const [onboardingCompleted, setOnboardingCompleted] = useState(false);
  const [progress, setProgress] = useState(0);

  // Track checklist items completion status
  const [checklistItems, setChecklistItems] = useState<Record<string, ChecklistItem[]>>({});

  const updateOnboardingData = (stepId: string, data: any) => {
    setOnboardingData((prevData) => ({
      ...prevData,
      [stepId]: data,
    }));
  };

  const goToNextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const getCurrentStep = () => {
    return steps[currentStepIndex] || null;
  };

  const calculateProgress = () => {
    return Math.round(((currentStepIndex + 1) / steps.length) * 100);
  };

  const updateChecklistItem = (stepId: string, itemId: string, completed: boolean) => {
    setChecklistItems((prevItems) => {
      const stepItems = [...(prevItems[stepId] || [])];
      const itemIndex = stepItems.findIndex((item) => item.id === itemId);

      if (itemIndex !== -1) {
        stepItems[itemIndex] = {
          ...stepItems[itemIndex],
          completed,
        };
      }

      return {
        ...prevItems,
        [stepId]: stepItems,
      };
    });
  };

  const initializeChecklistItems = (stepId: string, items: ChecklistItem[]) => {
    setChecklistItems((prevItems) => ({
      ...prevItems,
      [stepId]: items,
    }));
  };

  const getChecklistItems = (stepId: string) => {
    return checklistItems[stepId] || [];
  };

  const areAllChecklistItemsCompleted = (stepId: string) => {
    const items = checklistItems[stepId] || [];
    return items.length > 0 && items.every((item) => item.completed);
  };

  return {
    steps,
    setSteps,
    currentStepIndex,
    setCurrentStepIndex,
    isLoading,
    setIsLoading,
    onboardingData,
    updateOnboardingData,
    goToNextStep,
    goToPreviousStep,
    getCurrentStep,
    onboardingCompleted,
    setOnboardingCompleted,
    progress,
    setProgress,
    calculateProgress,
    checklistItems,
    updateChecklistItem,
    initializeChecklistItems,
    getChecklistItems,
    areAllChecklistItemsCompleted,
  };
};

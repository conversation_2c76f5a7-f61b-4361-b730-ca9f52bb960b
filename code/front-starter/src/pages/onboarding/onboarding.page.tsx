import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMount } from 'app-studio';
import { Loader } from '@app-studio/web';
import { useOnboardingState } from './onboarding.state';
import { useOnboardingRequests } from './onboarding.request';
import { OnboardingContainer, OnboardingSlide, OnboardingChecklist, ProfileForm } from './onboarding.element';
import { useAuthStore } from 'src/stores/AuthStore';
import { OnboardingStep, OnboardingStepType } from './onboarding.types';
import { PreferencesStep } from 'src/pages/setup/setup.element';

const OnboardingPage = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const {
    steps,
    setSteps,
    currentStepIndex,
    setCurrentStepIndex,
    isLoading,
    setIsLoading,
    onboardingData,
    updateOnboardingData,
    goToNextStep,
    goToPreviousStep,
    getCurrentStep,
    setOnboardingCompleted,
    progress,
    setProgress,
    calculateProgress,
    updateChecklistItem,
    initializeChecklistItems,
    getChecklistItems,
    areAllChecklistItemsCompleted,
  } = useOnboardingState();

  const {
    getStepsRequest,
    getStatusRequest,
    updateProgressRequest,
    completeSetupRequest,
    updateChecklistItem: updateChecklistItemRequest,
  } = useOnboardingRequests({
    onStepsLoaded: (data: OnboardingStep[]) => {
      setSteps(data);

      // Initialize checklist items for each step
      data.forEach((step) => {
        if (step.type === 'checklist' && step.checklistItems) {
          initializeChecklistItems(step.id, step.checklistItems);
        }
      });
    },
    onStatusLoaded: (data) => {
      if (data.completed) {
        // If onboarding is already completed, redirect to home
        navigate('/home');
        return;
      }

      // Find the index of the current step
      const stepIndex = steps.findIndex((step) => step.id === data.currentStep);
      if (stepIndex !== -1) {
        setCurrentStepIndex(stepIndex);
      }
      setProgress(data.progress);
    },
    onProgressUpdated: (data) => {
      setProgress(data.progress);
    },
    onOnboardingCompleted: () => {
      setOnboardingCompleted(true);
      navigate('/home');
    },
    onChecklistItemUpdated: (stepId, itemId, completed) => {
      updateChecklistItem(stepId, itemId, completed);
    },
  });

  useMount(() => {
    setIsLoading(true);
    getStepsRequest.run();
  });

  useEffect(() => {
    if (steps.length > 0) {
      getStatusRequest.run();
      setIsLoading(false);
    }
  }, [steps]);

  useEffect(() => {
    if (steps.length > 0) {
      const newProgress = calculateProgress();
      setProgress(newProgress);
    }
  }, [currentStepIndex, steps]);

  const handleStepSubmit = (stepId: string, values: any) => {
    updateOnboardingData(stepId, values);

    // Update progress on the server
    const newProgress = calculateProgress();
    updateProgressRequest.run({
      stepId: stepId,
      progress: newProgress,
    });

    goToNextStep();
  };

  const handleComplete = () => {
    completeSetupRequest.run();
  };

  const handleChecklistItemClick = (stepId: string, itemId: string, completed: boolean) => {
    updateChecklistItemRequest(stepId, itemId, completed);
  };

  const renderCurrentStep = () => {
    const currentStep = getCurrentStep();
    if (!currentStep) return null;

    const isFirstStep = currentStepIndex === 0;
    const isLastStep = currentStepIndex === steps.length - 1;

    switch (currentStep.type) {
      case 'slide':
        return (
          <OnboardingSlide
            title={currentStep.title}
            content={currentStep.slideContent || ''}
            imageUrl={currentStep.slideImageUrl}
            onNext={isLastStep ? handleComplete : goToNextStep}
            onPrev={goToPreviousStep}
            isFirstStep={isFirstStep}
            isLastStep={isLastStep}
          />
        );
      case 'checklist':
        return (
          <OnboardingChecklist
            title={currentStep.title}
            items={getChecklistItems(currentStep.id)}
            onNext={isLastStep ? handleComplete : goToNextStep}
            onPrev={goToPreviousStep}
            onItemClick={(itemId, completed) => handleChecklistItemClick(currentStep.id, itemId, completed)}
            isFirstStep={isFirstStep}
            isLastStep={isLastStep}
          />
        );
      case 'form':
        if (currentStep.id === OnboardingStepType.PROFILE) {
          return (
            <ProfileForm
              onNext={goToNextStep}
              onPrev={goToPreviousStep}
              initialValues={
                onboardingData.profile || {
                  name: user?.name || '',
                  job: '',
                  country: '',
                  bio: '',
                }
              }
              onSubmit={(values) => handleStepSubmit(OnboardingStepType.PROFILE, values)}
            />
          );
        } else if (currentStep.id === OnboardingStepType.PREFERENCES) {
          return (
            <PreferencesStep
              onNext={goToNextStep}
              onPrev={goToPreviousStep}
              initialValues={
                onboardingData.preferences || {
                  language: user?.language || 'en',
                  theme: 'light',
                }
              }
              onSubmit={(values) => handleStepSubmit(OnboardingStepType.PREFERENCES, values)}
            />
          );
        }
        return null;
      default:
        return null;
    }
  };

  if (isLoading || getStepsRequest.loading) {
    return (
      <OnboardingContainer progress={0} currentStep={0} totalSteps={1}>
        <Loader size="xl" />
      </OnboardingContainer>
    );
  }

  return (
    <OnboardingContainer progress={progress} currentStep={currentStepIndex} totalSteps={steps.length}>
      {renderCurrentStep()}
    </OnboardingContainer>
  );
};

export default OnboardingPage;

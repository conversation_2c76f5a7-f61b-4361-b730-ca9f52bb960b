export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  order: number;
  icon?: string;
  type?: 'slide' | 'checklist' | 'form';
  checklistItems?: ChecklistItem[];
  slideContent?: string;
  slideImageUrl?: string;
}

export interface ChecklistItem {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  url?: string;
}

export interface OnboardingWizardStatus {
  completed: boolean;
  currentStep: string;
  progress: number;
  checklistProgress?: Record<string, boolean[]>;
}

export interface UpdateOnboardingProgressDto {
  stepId: string;
  progress: number;
  checklistItemId?: string;
  checklistItemCompleted?: boolean;
}

export enum OnboardingStepType {
  WELCOME = 'welcome',
  FEATURES = 'features',
  INTERFACE = 'interface',
  PROFILE = 'profile',
  PREFERENCES = 'preferences',
  COMPLETE = 'complete',
}

import React from 'react';
import { Vertical, useMount } from 'app-studio';
import { showModal, Loader } from '@app-studio/web';
import 'src/components';
import * as AsyncStorage from 'src/utils/localstorage';
import { useAuthStore } from 'src/stores/AuthStore';
import { PostGenerationForm } from './blogPostCreator.elements';
import { useBlogPostCreatorRequests } from './blogPostCreator.requests';
import { useNavigate } from 'react-router-dom';

export default function BlogPostCreatorPage() {
    const navigate = useNavigate();
    const { postWorkflowControllerCreate }: any = useBlogPostCreatorRequests();

    const eventProps = {
        postWorkflowControllerCreate: postWorkflowControllerCreate
    };

    const requests = {
        postWorkflowControllerCreate: postWorkflowControllerCreate
    };

    useMount(() => {
        const redirectData = AsyncStorage.read('@redirectData');

        if (Object.keys(requests) && redirectData && redirectData.name && (redirectData.params && requests[redirectData.name])) {
            requests[redirectData.name].run(...redirectData.params);
            AsyncStorage.remove('@redirectData');
        }

        postWorkflowControllerCreate.run();
    });

    return <Vertical height='100vh' alignItems='center' gap='50px'><PostGenerationForm requests={requests} {...eventProps} /></Vertical>;
}
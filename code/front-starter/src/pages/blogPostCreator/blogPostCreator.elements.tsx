import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader } from '@app-studio/web';
import { TopicInputForm } from 'src/forms';
import i18n from 'src/utils/locale';

export const PostGenerationForm = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.postWorkflowControllerCreate.data && requests.postWorkflowControllerCreate.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}><TopicInputForm {...props} {...requests} /></Vertical>;
};
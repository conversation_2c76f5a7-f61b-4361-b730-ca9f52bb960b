import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader, Text } from '@app-studio/web';
import { GeneratePostFormForm } from 'src/forms';
import i18n from 'src/utils/locale';

export const GeneratePostSection = (
    {
        requests: requests,
        ...props
    }
) => {
    return <Vertical alignItems='center' gap={25}><PageTitle {...props} /><Instructions {...props} /><GeneratePostFormForm {...props} {...requests} /></Vertical>;
};

export const PageTitle = (
    {
        ...props
    }
) => {
    return <Text>{i18n.t('GenerateBlogPost.PageTitle.undefined.titleText')}</Text>;
};

export const Instructions = (
    {
        ...props
    }
) => {
    return <Text>{i18n.t('GenerateBlogPost.Instructions.undefined.instructionText')}</Text>;
};
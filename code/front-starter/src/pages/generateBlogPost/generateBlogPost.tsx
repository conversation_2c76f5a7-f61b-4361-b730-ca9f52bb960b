import React from 'react';
import { Vertical, useMount } from 'app-studio';
import { showModal, Loader } from '@app-studio/web';
import 'src/components';
import * as AsyncStorage from 'src/utils/localstorage';
import { useAuthStore } from 'src/stores/AuthStore';
import { GeneratePostSection } from './generateBlogPost.elements';
import { useGenerateBlogPostRequests } from './generateBlogPost.requests';
import { useNavigate } from 'react-router-dom';

export default function GenerateBlogPostPage() {
    const navigate = useNavigate();

    const handleSuccessPostWorkflowControllerCreate = data => {
        navigate(`/workflow/${data}`);
    };

    const { postWorkflowControllerCreate }: any = useGenerateBlogPostRequests(handleSuccessPostWorkflowControllerCreate);

    const eventProps = {
        postWorkflowControllerCreate: postWorkflowControllerCreate
    };

    const requests = {
        postWorkflowControllerCreate: postWorkflowControllerCreate
    };

    useMount(() => {
        const redirectData = AsyncStorage.read('@redirectData');

        if (Object.keys(requests) && redirectData && redirectData.name && (redirectData.params && requests[redirectData.name])) {
            requests[redirectData.name].run(...redirectData.params);
            AsyncStorage.remove('@redirectData');
        }
    });

    return <Vertical height='100vh' alignItems='center' gap='50px'><GeneratePostSection requests={requests} {...eventProps} /></Vertical>;
}
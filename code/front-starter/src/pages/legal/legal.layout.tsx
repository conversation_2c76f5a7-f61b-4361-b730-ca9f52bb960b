import React from 'react';
import { Vertical, Horizontal, View } from 'app-studio';
import { H3, C3 } from 'src/components/Text';
import { useNavigate, Outlet, Link } from 'react-router-dom';
import { SVGIcon } from 'src/components/Icon';

const LegalLayout = () => {
  const navigate = useNavigate();

  const legalLinks = [
    { path: '/legal/terms', label: 'Terms of Service' },
    { path: '/legal/privacy', label: 'Privacy Policy' },
  ];

  return (
    <Vertical minHeight="100vh">
      {/* Header */}
      <Horizontal
        padding={20}
        backgroundColor="theme.primary"
        color="white"
        justifyContent="space-between"
        alignItems="center"
      >
        <Horizontal gap={10} alignItems="center" onClick={() => navigate('/')} style={{ cursor: 'pointer' }}>
          <SVGIcon name="HomeSvg" size={24} color="white" />
          <H3 color="white">Company Name</H3>
        </Horizontal>
      </Horizontal>

      {/* Content */}
      <Horizontal flex={1}>
        {/* Sidebar */}
        <View
          width="250px"
          backgroundColor="theme.gray.10"
          padding={20}
          borderRight="1px solid"
          borderColor="theme.gray.20"
          media={{
            mobile: {
              display: 'none',
            },
          }}
        >
          <Vertical gap={15}>
            <H3>Legal Documents</H3>

            {legalLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                style={{
                  textDecoration: 'none',
                  color: 'inherit',
                }}
              >
                <C3 padding={10} borderRadius={4} _hover={{ backgroundColor: 'theme.gray.20' }}>
                  {link.label}
                </C3>
              </Link>
            ))}
          </Vertical>
        </View>

        {/* Main Content */}
        <View flex={1} overflowY="auto">
          <Outlet />
        </View>
      </Horizontal>

      {/* Footer */}
      <Horizontal
        padding={20}
        backgroundColor="theme.gray.10"
        borderTop="1px solid"
        borderColor="theme.gray.20"
        justifyContent="center"
      >
        <C3 color="theme.gray.60">© {new Date().getFullYear()} Company Name. All rights reserved.</C3>
      </Horizontal>
    </Vertical>
  );
};

export default LegalLayout;

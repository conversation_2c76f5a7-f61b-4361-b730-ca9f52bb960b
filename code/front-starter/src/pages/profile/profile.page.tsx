import React, { useEffect, useState } from 'react';
import { useAuthStore } from 'src/stores/AuthStore';
import { useMount, View, Horizontal, Vertical } from 'app-studio';
import { Button } from '@app-studio/web';
import { useNavigate } from 'react-router-dom';
import i18n from 'src/utils/locale';
import { AuthService, ProfileService } from 'src/services/api';
import { H3, C2, C3 } from 'src/components/Text';
import { Right } from 'src/components/Layout';
import AvatarUploader from 'src/components/profile/AvatarUploader';
import ProfileForm from 'src/forms/ProfileForm';

function ProfilePage() {
  const MeService = AuthService.useAuthUserControllerMeService();
  const { logout } = useAuthStore();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const { run: fetchProfile } = ProfileService.useProfileControllerMeService({
    onSuccess: (data) => {
      setProfile(data);
      setLoading(false);
    },
    onError: () => {
      setLoading(false);
    },
  });

  useMount(() => {
    MeService.run();
    fetchProfile();
  });

  if (!user || loading) return <View padding={60}>Loading...</View>;

  return (
    <>
      <ProfileContainer>
        <Horizontal justifyContent="space-between" alignItems="center">
          <H3>{i18n.t('page.profile.title')}</H3>
          <Button
            onClick={() => {
              logout();
              navigate('/');
            }}
          >
            {i18n.t('action.logout')}
          </Button>
        </Horizontal>

        <ProfileCard>
          <Horizontal gap={40} alignItems="flex-start" wrap="wrap">
            <View flex="0 0 auto" minWidth={200}>
              <SectionTitle>{i18n.t('page.profile.avatar')}</SectionTitle>
              <AvatarUploader imageUrl={user.imageUrl || profile?.imageUrl} />
            </View>

            <View flex="1 1 500px">
              <SectionTitle>{i18n.t('page.profile.info')}</SectionTitle>
              <InfoItem label={i18n.t('form.name.label')} value={user.name} />
              <InfoItem label={i18n.t('form.email.label')} value={user.email} />

              <SectionTitle marginTop={30}>{i18n.t('page.profile.bio')}</SectionTitle>
              <ProfileForm />
            </View>
          </Horizontal>
        </ProfileCard>
      </ProfileContainer>
    </>
  );
}

const InfoItem = ({ label, value }) => (
  <View marginBottom={10}>
    <C3 fontWeight={600} color="rgba(100, 100, 100, 1)">
      {label}
    </C3>
    <C2 marginTop={5}>{value || '-'}</C2>
  </View>
);

const SectionTitle = (props) => (
  <View font={'600 18px Work Sans'} color={'rgba(19, 15, 38, 1)'} marginBottom={15} {...props} />
);

const ProfileContainer = (props) => (
  <View
    display={'flex'}
    flex={'0 0 auto'}
    flexDirection={'column'}
    alignItems={'stretch'}
    justifyContent={'flex-start'}
    marginTop={32}
    padding={20}
    maxWidth={1200}
    marginLeft="auto"
    marginRight="auto"
    {...props}
  />
);

const ProfileCard = (props) => (
  <View
    display={'flex'}
    flex={'0 0 auto'}
    flexDirection={'column'}
    alignItems={'stretch'}
    justifyContent={'flex-start'}
    padding={'30px'}
    marginTop={20}
    background={'rgba(255, 255, 255, 1)'}
    border={'1px solid rgba(237, 237, 238, 1)'}
    borderRadius={8}
    boxShadow={'0px 1px 2px rgba(19, 15, 38, 0.04)'}
    {...props}
  />
);

export default ProfilePage;

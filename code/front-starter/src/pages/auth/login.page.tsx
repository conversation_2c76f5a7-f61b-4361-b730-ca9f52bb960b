import { HorizontalResponsive, View, Text, Horizontal, Vertical } from 'app-studio';
import { Button } from '@app-studio/web';

import { useNavigate } from 'react-router-dom';

import { i18n } from 'src/utils/locale';
import { API_URL } from 'src/configs/AppConfig';
import LoginForm from 'src/forms/LoginForm';
import { Left, Right } from 'src/components/Layout';

function AuthContainer() {
  const navigate = useNavigate();

  const authenficationMethods = {
  "email_password": "Email and Password",
  "google": "Google Account",
  "facebook": "Facebook Account"
};

  const onAuthMethodClick = {
    google: () => {
      window.location.href = `${API_URL}/auth/google`;
    },
    facebook: () => {
      window.location.href = `${API_URL}/auth/facebook`;
    },
    linkedin: () => {
      window.location.href = `${API_URL}/auth/linkedin`;
    },
    twitter: () => {
      window.location.href = `${API_URL}/auth/twitter`;
    },
  };

  return (
    <LoginContainer>
      <Text>{i18n.t('page.login.welcome')}</Text>
      <LoginForm />
      <Horizontal margin={10}>
        <Left>
          <HorizontalResponsive onClick={() => navigate('/auth/register')}>
            <PasswordLabel>{i18n.t('page.login.haveAccount')}</PasswordLabel>
            <SignUpLink>{i18n.t('page.login.signUp')}</SignUpLink>
          </HorizontalResponsive>
        </Left>
        <Right onClick={() => navigate('/security/reset-password')}>
          <PasswordLabel>{i18n.t('page.login.forgotPassword')}</PasswordLabel>
        </Right>
      </Horizontal>
      <Vertical gap={30} alignItems="center" justifyContent='="space-between'>
        <Text fontWeight={500}>Login with</Text>
        {authenficationMethods &&
          Object.keys(authenficationMethods).length > 0 &&
          Object.keys(authenficationMethods).map(
            (key) =>
              key !== 'email_password' && (
                <Button onClick={onAuthMethodClick[key]} width="100%">
                  {authenficationMethods[key]}
                </Button>
              )
          )}
      </Vertical>
    </LoginContainer>
  );
}

export default AuthContainer;

const LoginContainer = (props) => (
  <View padding={20} flexDirection={'column'} justifyContent={'flex-start'} {...props} />
);

const PasswordLabel = (props) => <View font={'400 16px '} color={'rgba(19, 15, 38, 1)'} marginRight={10} {...props} />;

const SignUpLink = (props) => (
  <View font={'500 16px '} color={'rgba(85, 52, 165, 1)'} textDecorationLine={'underline'} {...props} />
);

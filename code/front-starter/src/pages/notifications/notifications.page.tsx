import React, { useEffect, useState } from 'react';
import { Vertical, Horizontal, View } from 'app-studio';
import { H2, C3 } from 'src/components/Text';
import { But<PERSON>, Loader, Pagination, showMessage } from '@app-studio/web';
import { useNavigate } from 'react-router-dom';
import i18n from 'src/utils/locale';
import { AccountService } from 'src/services/api';
import { useAuthStore } from 'src/stores/AuthStore';
import { NotificationList, NotificationFilters } from './notifications.element';
import { useNotificationsRequest } from './notifications.request';

const NotificationsPage = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [filter, setFilter] = useState<{
    isRead?: boolean;
    type?: string;
  }>({});

  const { getNotificationsRequest, markAllAsReadRequest, deleteAllNotificationsRequest } = useNotificationsRequest({
    onGetNotificationsSuccess: (data) => {
      setTotalItems(data.total);
    },
    onMarkAllAsReadSuccess: () => {
      showMessage('success', 'Notification', i18n.t('page.notifications.allMarkedAsRead'));
      getNotificationsRequest.run({
        skip: (currentPage - 1) * pageSize,
        take: pageSize,
        ...filter,
      });
    },
    onDeleteAllNotificationsSuccess: () => {
      showMessage('success', 'Notification', i18n.t('page.notifications.allDeleted'));
      getNotificationsRequest.run({
        skip: (currentPage - 1) * pageSize,
        take: pageSize,
        ...filter,
      });
    },
  });

  useEffect(() => {
    if (user) {
      getNotificationsRequest.run({
        skip: (currentPage - 1) * pageSize,
        take: pageSize,
        ...filter,
      });
    }
  }, [user, currentPage, pageSize, filter]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleFilterChange = (newFilter: { isRead?: boolean; type?: string }) => {
    setFilter(newFilter);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  const handleMarkAllAsRead = () => {
    markAllAsReadRequest.run();
  };

  const handleDeleteAll = () => {
    deleteAllNotificationsRequest.run(filter.isRead);
  };

  if (!user) {
    return (
      <View padding={20}>
        <C3>{i18n.t('page.notifications.loginRequired')}</C3>
        <Button marginTop={20} onClick={() => navigate('/auth/login')}>
          {i18n.t('button.login')}
        </Button>
      </View>
    );
  }

  return (
    <View padding={20} maxWidth="1200px" margin="0 auto">
      <Horizontal justifyContent="space-between" alignItems="center" marginBottom={20}>
        <H2>{i18n.t('page.notifications.title')}</H2>
        <Horizontal gap={10}>
          <Button variant="outline" onClick={handleMarkAllAsRead} isLoading={markAllAsReadRequest.loading}>
            {i18n.t('page.notifications.markAllAsRead')}
          </Button>
          <Button
            variant="outline"
            colorScheme="theme.error"
            onClick={handleDeleteAll}
            isLoading={deleteAllNotificationsRequest.loading}
          >
            {i18n.t('page.notifications.deleteAll')}
          </Button>
        </Horizontal>
      </Horizontal>

      <NotificationFilters filter={filter} onFilterChange={handleFilterChange} />

      {getNotificationsRequest.loading ? (
        <View padding={50} display="flex" justifyContent="center">
          <Loader size={40} />
        </View>
      ) : getNotificationsRequest.data?.notifications?.length > 0 ? (
        <Vertical gap={20}>
          <NotificationList
            notifications={getNotificationsRequest.data.notifications}
            onRefresh={() => {
              getNotificationsRequest.run({
                skip: (currentPage - 1) * pageSize,
                take: pageSize,
                ...filter,
              });
            }}
          />
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            pageSize={pageSize}
            totalPages={Math.ceil(totalItems / pageSize)}
            onPageChange={handlePageChange}
          />
        </Vertical>
      ) : (
        <View
          padding={50}
          display="flex"
          justifyContent="center"
          alignItems="center"
          backgroundColor="theme.gray.10"
          borderRadius={8}
        >
          <C3>{i18n.t('page.notifications.empty')}</C3>
        </View>
      )}
    </View>
  );
};

export default NotificationsPage;

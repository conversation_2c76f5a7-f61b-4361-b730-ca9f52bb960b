import { AccountService } from 'src/services/api';
import { UseRequestOption } from '@app-studio/react-request';

interface NotificationsRequestCallbacks {
  onGetNotificationsSuccess?: (data: any) => void;
  onGetNotificationsError?: (error: any) => void;
  onMarkAsReadSuccess?: (data: any) => void;
  onMarkAsReadError?: (error: any) => void;
  onMarkAllAsReadSuccess?: (data: any) => void;
  onMarkAllAsReadError?: (error: any) => void;
  onDeleteNotificationSuccess?: (data: any) => void;
  onDeleteNotificationError?: (error: any) => void;
  onDeleteAllNotificationsSuccess?: (data: any) => void;
  onDeleteAllNotificationsError?: (error: any) => void;
  onGetUnreadCountSuccess?: (data: any) => void;
  onGetUnreadCountError?: (error: any) => void;
}

export const useNotificationsRequest = (callbacks: NotificationsRequestCallbacks = {}) => {
  const {
    onGetNotificationsSuccess,
    onGetNotificationsError,
    onMarkAsReadSuccess,
    onMarkAsReadError,
    onMarkAllAsReadSuccess,
    onMarkAllAsReadError,
    onDeleteNotificationSuccess,
    onDeleteNotificationError,
    onDeleteAllNotificationsSuccess,
    onDeleteAllNotificationsError,
    onGetUnreadCountSuccess,
    onGetUnreadCountError,
  } = callbacks;

  // Get notifications with pagination and filtering
  const getNotificationsRequest = AccountService.useAccountControllerNotificationsService({
    onSuccess: onGetNotificationsSuccess,
    onError: onGetNotificationsError,
  });

  // Mark a notification as read
  const markAsReadRequest = AccountService.useAccountControllerNotificationService({
    onSuccess: onMarkAsReadSuccess,
    onError: onMarkAsReadError,
  });

  // Mark all notifications as read
  const markAllAsReadRequest = AccountService.useAccountControllerMarkAllNotificationsAsReadService({
    onSuccess: onMarkAllAsReadSuccess,
    onError: onMarkAllAsReadError,
  });

  // Delete a notification
  const deleteNotificationRequest = AccountService.useAccountControllerDeleteNotificationService({
    onSuccess: onDeleteNotificationSuccess,
    onError: onDeleteNotificationError,
  });

  // Delete all notifications
  const deleteAllNotificationsRequest = AccountService.useAccountControllerDeleteAllNotificationsService({
    onSuccess: onDeleteAllNotificationsSuccess,
    onError: onDeleteAllNotificationsError,
  });

  // Get unread notification count
  const getUnreadCountRequest = AccountService.useAccountControllerUnreadNotificationCountService({
    onSuccess: onGetUnreadCountSuccess,
    onError: onGetUnreadCountError,
  });

  return {
    getNotificationsRequest,
    markAsReadRequest,
    markAllAsReadRequest,
    deleteNotificationRequest,
    deleteAllNotificationsRequest,
    getUnreadCountRequest,
  };
};

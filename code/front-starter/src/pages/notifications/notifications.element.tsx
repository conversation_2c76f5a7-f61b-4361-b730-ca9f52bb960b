import React from 'react';
import { Vertical, Horizontal, View, Text } from 'app-studio';
import { C3, C4 } from 'src/components/Text';
import { Button, Select, Switch, showMessage } from '@app-studio/web';
import { SVGIcon } from 'src/components/Icon';
import i18n from 'src/utils/locale';
import { formatDistanceToNow } from 'date-fns';
import { fr, enUS } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import { useNotificationsRequest } from './notifications.request';
import { useLocaleStore } from 'src/stores/LocaleStore';

interface Notification {
  id: string;
  title: {
    fr: string;
    en: string;
  };
  message?: {
    fr: string;
    en: string;
  };
  link: string;
  isRead: boolean;
  type: string;
  createdAt: string;
}

interface NotificationItemProps {
  notification: Notification;
  onRefresh: () => void;
}

export const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onRefresh }) => {
  const navigate = useNavigate();
  const { language } = useLocaleStore();
  const dateLocale = language === 'fr' ? fr : enUS;

  const { markAsReadRequest, deleteNotificationRequest } = useNotificationsRequest({
    onMarkAsReadSuccess: () => {
      onRefresh();
    },
    onDeleteNotificationSuccess: () => {
      showMessage('success', 'Success', i18n.t('page.notifications.deleted'));
      onRefresh();
    },
  });

  const handleClick = () => {
    if (!notification.isRead) {
      markAsReadRequest.run(notification.id);
    }
    if (notification.link) {
      navigate(notification.link);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    deleteNotificationRequest.run(notification.id);
  };

  const getIconForType = (type: string) => {
    switch (type) {
      case 'info':
        return { name: 'InfoSvg', color: 'theme.info' };
      case 'success':
        return { name: 'DoneSvg', color: 'theme.success' };
      case 'warning':
        return { name: 'WarningTriangleSvg', color: 'theme.warning' };
      case 'error':
        return { name: 'ErrorSvg', color: 'theme.error' };
      case 'system':
        return { name: 'SettingSvg', color: 'theme.primary' };
      default:
        return { name: 'MessageSvg', color: 'theme.primary' };
    }
  };

  const icon = getIconForType(notification.type);
  const title = notification.title[language] || notification.title.en;
  const message = notification.message ? notification.message[language] || notification.message.en : '';
  const timeAgo = formatDistanceToNow(new Date(notification.createdAt), {
    addSuffix: true,
    locale: dateLocale,
  });

  return (
    <Horizontal
      padding={15}
      borderRadius={8}
      backgroundColor={notification.isRead ? 'white' : 'theme.gray.10'}
      boxShadow="0 1px 3px rgba(0,0,0,0.1)"
      alignItems="flex-start"
      gap={15}
      cursor="pointer"
      onClick={handleClick}
      _hover={{ backgroundColor: 'theme.gray.20' }}
      transition="background-color 0.2s"
    >
      {/* <SVGIcon name={icon.name} size={24} color={icon.color} /> */}

      <Vertical flex={1} gap={5}>
        <Horizontal justifyContent="space-between" alignItems="center">
          <C3 fontWeight={notification.isRead ? '400' : '600'}>{title}</C3>
          <C4 color="theme.gray.60">{timeAgo}</C4>
        </Horizontal>

        {message && <C4 color="theme.gray.80">{message}</C4>}
      </Vertical>

      <Button variant="ghost" size="sm" onClick={handleDelete} isLoading={deleteNotificationRequest.loading}>
        <SVGIcon name="GarbageSvg" size={16} color="theme.error" />
      </Button>
    </Horizontal>
  );
};

interface NotificationListProps {
  notifications: Notification[];
  onRefresh: () => void;
}

export const NotificationList: React.FC<NotificationListProps> = ({ notifications, onRefresh }) => {
  return (
    <Vertical gap={10}>
      {notifications.map((notification) => (
        <NotificationItem key={notification.id} notification={notification} onRefresh={onRefresh} />
      ))}
    </Vertical>
  );
};

interface NotificationFiltersProps {
  filter: {
    isRead?: boolean;
    type?: string;
  };
  onFilterChange: (filter: { isRead?: boolean; type?: string }) => void;
}

export const NotificationFilters: React.FC<NotificationFiltersProps> = ({ filter, onFilterChange }) => {
  const handleReadFilterChange = (value?: boolean) => {
    onFilterChange({ ...filter, isRead: value });
  };

  const handleTypeFilterChange = (value: string) => {
    onFilterChange({ ...filter, type: value === 'all' ? undefined : value });
  };

  return (
    <Horizontal
      padding={15}
      backgroundColor="white"
      borderRadius={8}
      boxShadow="0 1px 3px rgba(0,0,0,0.1)"
      justifyContent="space-between"
      alignItems="center"
      marginBottom={20}
      flexWrap="wrap"
      gap={15}
    >
      <Horizontal alignItems="center" gap={10}>
        <C4>{i18n.t('page.notifications.filters.status')}:</C4>
        <Select
          value={filter.isRead === undefined ? 'all' : filter.isRead ? 'read' : 'unread'}
          onChange={(value) => {
            if (value === 'all') {
              handleReadFilterChange(undefined as any);
            } else if (value === 'read') {
              handleReadFilterChange(true);
            } else {
              handleReadFilterChange(false);
            }
          }}
          options={[
            { label: i18n.t('page.notifications.filters.all'), value: 'all' },
            { label: i18n.t('page.notifications.filters.read'), value: 'read' },
            { label: i18n.t('page.notifications.filters.unread'), value: 'unread' },
          ]}
          width={150}
        />
      </Horizontal>

      <Horizontal alignItems="center" gap={10}>
        <C4>{i18n.t('page.notifications.filters.type')}:</C4>
        <Select
          value={filter.type || 'all'}
          onChange={handleTypeFilterChange}
          options={[
            { label: i18n.t('page.notifications.filters.all'), value: 'all' },
            { label: i18n.t('page.notifications.filters.info'), value: 'info' },
            { label: i18n.t('page.notifications.filters.success'), value: 'success' },
            { label: i18n.t('page.notifications.filters.warning'), value: 'warning' },
            { label: i18n.t('page.notifications.filters.error'), value: 'error' },
            { label: i18n.t('page.notifications.filters.system'), value: 'system' },
          ]}
          width={150}
        />
      </Horizontal>
    </Horizontal>
  );
};

import React, { useEffect } from 'react';
import { Vertical, Horizontal, View } from 'app-studio';
import { H2, H3, C3 } from 'src/components/Text';
import { Button, Pagination, showMessage } from '@app-studio/web';
import { SVGIcon } from 'src/components/Icon';
import i18n from 'src/utils/locale';
import { useAuthStore } from 'src/stores/AuthStore';
import { useDashboardRequests } from './dashboard.request';
import { useDashboardStates } from './dashboard.state';
import { ActivityList, ActivitySummary, DashboardStats, FilterControls } from './dashboard.element';
import { ActivityActionType } from 'src/types/activity';

const DashboardPage = () => {
  const { user } = useAuthStore();
  const { dashboardActivityRequest, userActivityLogsRequest, refreshDashboard } = useDashboardRequests();

  const {
    selectedActionTypes,
    setSelectedActionTypes,
    selectedEntityTypes,
    setSelectedEntityTypes,
    viewMode,
    setViewMode,
    resetFilters,
  } = useDashboardStates();

  // Apply filters when they change
  useEffect(() => {
    if (user?.id) {
      dashboardActivityRequest.run({
        limit: 10,
        groupByDate: true,
        actionTypes: selectedActionTypes.length > 0 ? selectedActionTypes : undefined,
        entityTypes: selectedEntityTypes.length > 0 ? selectedEntityTypes : undefined,
      });
    }
  }, [selectedActionTypes, selectedEntityTypes, user?.id]);

  // Calculate stats
  const stats = [
    {
      label: i18n.t('page.dashboard.totalActivities'),
      value: userActivityLogsRequest.data?.total?.toString() || '0',
      icon: 'ActivitySvg',
      color: 'theme.primary',
    },
    {
      label: i18n.t('page.dashboard.lastLogin'),
      value: userActivityLogsRequest.data?.data?.find((a) => a.actionType === ActivityActionType.LOGIN)
        ? new Date(
            userActivityLogsRequest.data.data.find((a) => a.actionType === ActivityActionType.LOGIN)!.createdAt
          ).toLocaleDateString()
        : i18n.t('page.dashboard.never'),
      icon: 'LoginSvg',
      color: 'theme.info',
    },
    {
      label: i18n.t('page.dashboard.mostCommonAction'),
      value: dashboardActivityRequest.data?.summary?.actionTypeCounts
        ? Object.entries(dashboardActivityRequest.data.summary.actionTypeCounts).sort(
            (a: any, b: any) => b[1] - a[1]
          )[0]?.[0] || i18n.t('page.dashboard.none')
        : i18n.t('page.dashboard.loading'),
      icon: 'ChartSvg',
      color: 'theme.success',
    },
  ];

  return (
    <View padding={20} maxWidth="1200px" margin="0 auto">
      <Horizontal justifyContent="space-between" alignItems="center" marginBottom={20}>
        <H2>{i18n.t('page.dashboard.title')}</H2>
        <Button
          leftIcon={<SVGIcon name="RefreshSvg" size={16} color="white" />}
          onClick={refreshDashboard}
          isLoading={dashboardActivityRequest.loading || userActivityLogsRequest.loading}
        >
          {i18n.t('page.dashboard.refresh')}
        </Button>
      </Horizontal>

      {/* Dashboard Stats */}
      <DashboardStats stats={stats} />

      <Horizontal gap={20} marginTop={20} alignItems="flex-start" flexWrap="wrap">
        {/* Left Column - Filters */}
        <Vertical gap={20} flex="1" minWidth="250px">
          <FilterControls
            selectedActionTypes={selectedActionTypes}
            setSelectedActionTypes={setSelectedActionTypes}
            selectedEntityTypes={selectedEntityTypes}
            setSelectedEntityTypes={setSelectedEntityTypes}
            resetFilters={resetFilters}
          />

          {/* Activity Summary */}
          <ActivitySummary
            actionTypeCounts={dashboardActivityRequest.data?.summary?.actionTypeCounts}
            loading={dashboardActivityRequest.loading}
          />
        </Vertical>

        {/* Right Column - Activity Feed */}
        <Vertical gap={20} flex="2" minWidth="300px">
          <Vertical
            padding={16}
            backgroundColor="white"
            borderRadius={8}
            boxShadow="0 2px 4px rgba(0, 0, 0, 0.05)"
            gap={16}
          >
            <Horizontal justifyContent="space-between" alignItems="center">
              <H3>{i18n.t('page.dashboard.activityFeed')}</H3>
              <Horizontal gap={8}>
                <Button
                  size="sm"
                  variant={viewMode === 'list' ? 'solid' : 'outline'}
                  onClick={() => setViewMode('list')}
                >
                  {i18n.t('page.dashboard.listView')}
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === 'summary' ? 'solid' : 'outline'}
                  onClick={() => setViewMode('summary')}
                >
                  {i18n.t('page.dashboard.summaryView')}
                </Button>
              </Horizontal>
            </Horizontal>

            {/* Activity List */}
            <ActivityList
              activities={dashboardActivityRequest.data?.data || []}
              loading={dashboardActivityRequest.loading}
            />
          </Vertical>
        </Vertical>
      </Horizontal>
    </View>
  );
};

export default DashboardPage;

import { useState } from 'react';
import { ActivityActionType } from 'src/types/activity';

export const useDashboardStates = () => {
  // Filter states
  const [selectedActionTypes, setSelectedActionTypes] = useState<ActivityActionType[]>([]);
  const [selectedEntityTypes, setSelectedEntityTypes] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{ startDate?: Date; endDate?: Date }>({});

  // Activity view states
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'summary'>('list');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  return {
    // Filter states
    selectedActionTypes,
    setSelectedActionTypes,
    selectedEntityTypes,
    setSelectedEntityTypes,
    dateRange,
    setDateRange,

    // Activity view states
    viewMode,
    setViewMode,

    // Pagination states
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,

    // Helper functions
    resetFilters: () => {
      setSelectedActionTypes([]);
      setSelectedEntityTypes([]);
      setDateRange({});
    },
  };
};

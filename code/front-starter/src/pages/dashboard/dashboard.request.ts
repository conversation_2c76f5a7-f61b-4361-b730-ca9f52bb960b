import { useEffect } from 'react';
import { ActivityLogService } from 'src/services/api';
import { useAuthStore } from 'src/stores/AuthStore';

export const useDashboardRequests = () => {
  const { user } = useAuthStore();

  // Request to get dashboard activity logs
  const dashboardActivityRequest = ActivityLogService.useActivityLogControllerGetDashboardActivityService();

  // Request to get user activity logs
  const userActivityLogsRequest = ActivityLogService.useActivityLogControllerGetUserActivityLogsService();

  // Load dashboard data on component mount
  useEffect(() => {
    if (user?.id) {
      // Fetch dashboard activity data
      dashboardActivityRequest.run({
        limit: 10,
        groupByDate: true,
      });

      // Fetch user activity logs
      userActivityLogsRequest.run(user.id, 0, 5);
    }
  }, [user?.id]);

  return {
    dashboardActivityRequest,
    userActivityLogsRequest,
    refreshDashboard: () => {
      if (user?.id) {
        dashboardActivityRequest.run({
          limit: 10,
          groupByDate: true,
        });
        userActivityLogsRequest.run(user.id, 0, 5);
      }
    },
  };
};

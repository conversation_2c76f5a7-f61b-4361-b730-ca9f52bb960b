import React from 'react';
import { Horizontal, Vertical, View, Center } from 'app-studio';
import { H3, H4, C3, C4 } from 'src/components/Text';
import { <PERSON><PERSON>, Badge, Loader } from '@app-studio/web';
import { SVGIcon } from 'src/components/Icon';
import { ActivityLog, ActivityActionType } from 'src/types/activity';
import { formatDistanceToNow } from 'date-fns';
import i18n from 'src/utils/locale';

// Activity card component
export const ActivityCard = ({ activity }: { activity: ActivityLog }) => {
  // Function to get icon based on action type
  const getActionIcon = (actionType: ActivityActionType) => {
    switch (actionType) {
      case ActivityActionType.CREATE:
        return 'PlusSvg';
      case ActivityActionType.UPDATE:
        return 'EditSvg';
      case ActivityActionType.DELETE:
        return 'TrashSvg';
      case ActivityActionType.LOGIN:
        return 'LoginSvg';
      case ActivityActionType.LOGOUT:
        return 'LogoutSvg';
      case ActivityActionType.UPLOAD:
        return 'UploadSvg';
      case ActivityActionType.DOWNLOAD:
        return 'DownloadSvg';
      case ActivityActionType.SHARE:
        return 'ShareSvg';
      case ActivityActionType.COMMENT:
        return 'CommentSvg';
      case ActivityActionType.LIKE:
        return 'HeartSvg';
      case ActivityActionType.PAYMENT:
        return 'CreditCardSvg';
      case ActivityActionType.SUBSCRIPTION:
        return 'SubscriptionSvg';
      case ActivityActionType.SETTINGS_CHANGE:
        return 'SettingsSvg';
      default:
        return 'InfoSvg';
    }
  };

  // Function to get color based on action type
  const getActionColor = (actionType: ActivityActionType) => {
    switch (actionType) {
      case ActivityActionType.CREATE:
        return 'theme.success';
      case ActivityActionType.UPDATE:
        return 'theme.primary';
      case ActivityActionType.DELETE:
        return 'theme.error';
      case ActivityActionType.LOGIN:
      case ActivityActionType.LOGOUT:
        return 'theme.info';
      case ActivityActionType.UPLOAD:
      case ActivityActionType.DOWNLOAD:
        return 'theme.warning';
      default:
        return 'theme.gray.60';
    }
  };

  return (
    <Horizontal
      padding={16}
      borderRadius={8}
      backgroundColor="white"
      boxShadow="0 2px 4px rgba(0, 0, 0, 0.05)"
      gap={12}
      alignItems="center"
      borderWidth={1}
      borderColor="theme.gray.20"
      _hover={{ boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}
      transition="all 0.3s ease"
    >
      <Center width={40} height={40} borderRadius="50%" backgroundColor={`${getActionColor(activity.actionType)}15`}>
        <SVGIcon
          name={getActionIcon(activity.actionType) as any}
          size={20}
          color={getActionColor(activity.actionType)}
        />
      </Center>
      <Vertical flex={1}>
        <C3 fontWeight={600}>{activity.description}</C3>
        <Horizontal justifyContent="space-between" alignItems="center">
          <C4 color="theme.gray.60">
            {activity.entityType} {activity.entityId ? `#${activity.entityId.substring(0, 8)}` : ''}
          </C4>
          <C4 color="theme.gray.60">{formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}</C4>
        </Horizontal>
      </Vertical>
    </Horizontal>
  );
};

// Activity list component
export const ActivityList = ({ activities, loading }: { activities: ActivityLog[]; loading: boolean }) => {
  if (loading) {
    return (
      <Center padding={40}>
        <Loader size="lg" />
      </Center>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <Center padding={40} backgroundColor="theme.gray.10" borderRadius={8}>
        <Vertical alignItems="center" gap={16}>
          <SVGIcon name="InfoSvg" size={32} color="theme.gray.60" />
          <C3 color="theme.gray.60">{i18n.t('page.dashboard.noActivities')}</C3>
        </Vertical>
      </Center>
    );
  }

  return (
    <Vertical gap={12}>
      {activities.map((activity) => (
        <ActivityCard key={activity.id} activity={activity} />
      ))}
    </Vertical>
  );
};

// Activity summary component
export const ActivitySummary = ({
  actionTypeCounts,
  loading,
}: {
  actionTypeCounts?: Record<ActivityActionType, number>;
  loading: boolean;
}) => {
  if (loading) {
    return (
      <Center padding={40}>
        <Loader size="lg" />
      </Center>
    );
  }

  if (!actionTypeCounts) {
    return (
      <Center padding={40} backgroundColor="theme.gray.10" borderRadius={8}>
        <C3 color="theme.gray.60">{i18n.t('page.dashboard.noSummary')}</C3>
      </Center>
    );
  }

  return (
    <Vertical gap={16} padding={16} backgroundColor="white" borderRadius={8} boxShadow="0 2px 4px rgba(0, 0, 0, 0.05)">
      <H4>{i18n.t('page.dashboard.activitySummary')}</H4>
      <Horizontal flexWrap="wrap" gap={12}>
        {Object.entries(actionTypeCounts).map(([actionType, count]) => (
          <Badge key={actionType} colorScheme="theme.primary" content={`${actionType}: ${count}`} />
        ))}
      </Horizontal>
    </Vertical>
  );
};

// Dashboard stats component
export const DashboardStats = ({
  stats,
}: {
  stats: { label: string; value: string; icon: string; color: string }[];
}) => {
  return (
    <Horizontal gap={16} flexWrap="wrap">
      {stats.map((stat, index) => (
        <View
          key={index}
          flex="1"
          minWidth="200px"
          padding={16}
          backgroundColor="white"
          borderRadius={8}
          boxShadow="0 2px 4px rgba(0, 0, 0, 0.05)"
          borderWidth={1}
          borderColor="theme.gray.20"
        >
          <Horizontal alignItems="center" gap={12}>
            <Center width={40} height={40} borderRadius="50%" backgroundColor={`${stat.color}15`}>
              <SVGIcon name={stat.icon as any} size={20} color={stat.color} />
            </Center>
            <Vertical>
              <C4 color="theme.gray.60">{stat.label}</C4>
              <H3>{stat.value}</H3>
            </Vertical>
          </Horizontal>
        </View>
      ))}
    </Horizontal>
  );
};

// Filter controls component
export const FilterControls = ({
  selectedActionTypes,
  setSelectedActionTypes,
  selectedEntityTypes,
  setSelectedEntityTypes,
  resetFilters,
}: {
  selectedActionTypes: ActivityActionType[];
  setSelectedActionTypes: (types: ActivityActionType[]) => void;
  selectedEntityTypes: string[];
  setSelectedEntityTypes: (types: string[]) => void;
  resetFilters: () => void;
}) => {
  // Common entity types
  const commonEntityTypes = ['user', 'profile', 'comment', 'post'];

  return (
    <Vertical gap={16} padding={16} backgroundColor="white" borderRadius={8} boxShadow="0 2px 4px rgba(0, 0, 0, 0.05)">
      <Horizontal justifyContent="space-between" alignItems="center">
        <H4>{i18n.t('page.dashboard.filters')}</H4>
        <Button variant="outline" size="sm" onClick={resetFilters}>
          {i18n.t('page.dashboard.resetFilters')}
        </Button>
      </Horizontal>

      <Vertical gap={8}>
        <C3 fontWeight={600}>{i18n.t('page.dashboard.actionTypes')}</C3>
        <Horizontal flexWrap="wrap" gap={8}>
          {Object.values(ActivityActionType).map((actionType) => (
            <Badge
              key={actionType}
              colorScheme={selectedActionTypes.includes(actionType) ? 'theme.primary' : 'theme.gray.40'}
              cursor="pointer"
              onClick={() => {
                if (selectedActionTypes.includes(actionType)) {
                  setSelectedActionTypes(selectedActionTypes.filter((type) => type !== actionType));
                } else {
                  setSelectedActionTypes([...selectedActionTypes, actionType]);
                }
              }}
              content={actionType}
            />
          ))}
        </Horizontal>
      </Vertical>

      <Vertical gap={8}>
        <C3 fontWeight={600}>{i18n.t('page.dashboard.entityTypes')}</C3>
        <Horizontal flexWrap="wrap" gap={8}>
          {commonEntityTypes.map((entityType) => (
            <Badge
              key={entityType}
              colorScheme={selectedEntityTypes.includes(entityType) ? 'theme.primary' : 'theme.gray.40'}
              cursor="pointer"
              onClick={() => {
                if (selectedEntityTypes.includes(entityType)) {
                  setSelectedEntityTypes(selectedEntityTypes.filter((type) => type !== entityType));
                } else {
                  setSelectedEntityTypes([...selectedEntityTypes, entityType]);
                }
              }}
              content={entityType}
            />
          ))}
        </Horizontal>
      </Vertical>
    </Vertical>
  );
};

import React, { useEffect, useState } from 'react';
import { Center, Horizontal, Vertical, Text, View, Button } from 'app-studio';
import { useAuthStore } from 'src/stores/AuthStore';
import { ActivityLogService } from 'src/services/api';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import i18n from 'src/utils/locale';
import { useMount } from 'app-studio';
import { H1, H2, C1, C2 } from 'src/components/Text';
import { AccountLayout } from 'src/layouts/AccountLayout';
import { Loader } from '@app-studio/web';

const ActivityLogPage = () => {
  const { user } = useAuthStore();
  const [activityLogs, setActivityLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10;

  const activityLogRequest: any = ActivityLogService.useActivityLogControllerListService({
    onSuccess: (data) => {
      if (page === 0) {
        setActivityLogs(data.data);
      } else {
        setActivityLogs([...activityLogs, ...data.data]);
      }
      setHasMore(data.data.length === pageSize);
      setLoading(false);
    },
    onError: (error) => {
      console.error('Error fetching activity logs:', error);
      setLoading(false);
    },
  });

  useMount(() => {
    if (user) {
      loadActivityLogs();
    }
  });

  const loadActivityLogs = () => {
    setLoading(true);
    activityLogRequest.run(
      undefined, // userId - not needed as the API will filter by the authenticated user
      undefined, // actionType
      undefined, // entityType
      undefined, // entityId
      undefined, // startDate
      undefined, // endDate
      page * pageSize, // skip
      pageSize // take
    );
  };

  const loadMore = () => {
    setPage(page + 1);
  };

  useEffect(() => {
    if (page > 0) {
      loadActivityLogs();
    }
  }, [page]);

  const getActionTypeLabel = (actionType) => {
    const actionTypes = {
      CREATE: i18n.t('activity.action.create'),
      READ: i18n.t('activity.action.read'),
      UPDATE: i18n.t('activity.action.update'),
      DELETE: i18n.t('activity.action.delete'),
      LOGIN: i18n.t('activity.action.login'),
      LOGOUT: i18n.t('activity.action.logout'),
      UPLOAD: i18n.t('activity.action.upload'),
      DOWNLOAD: i18n.t('activity.action.download'),
      SHARE: i18n.t('activity.action.share'),
      COMMENT: i18n.t('activity.action.comment'),
      LIKE: i18n.t('activity.action.like'),
      PAYMENT: i18n.t('activity.action.payment'),
      SUBSCRIPTION: i18n.t('activity.action.subscription'),
      SETTINGS_CHANGE: i18n.t('activity.action.settings_change'),
      OTHER: i18n.t('activity.action.other'),
    };
    return actionTypes[actionType] || actionType;
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPpp', { locale: fr });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <AccountLayout>
      <Vertical width="100%" gap={20} paddingVertical={20}>
        <H1>{i18n.t('activity.title')}</H1>
        <C1>{i18n.t('activity.description')}</C1>

        {loading && page === 0 ? (
          <Center padding={40}>
            <Loader size={40} />
          </Center>
        ) : activityLogs.length === 0 ? (
          <View padding={20} borderRadius={8} backgroundColor="#f5f5f5">
            <C1>{i18n.t('activity.no_logs')}</C1>
          </View>
        ) : (
          <Vertical gap={10} width="100%">
            {activityLogs.map((log: any) => (
              <View key={log.id} padding={15} borderRadius={8} backgroundColor="#f5f5f5" width="100%">
                <Vertical gap={8}>
                  <Horizontal justifyContent="space-between" alignItems="center">
                    <View>
                      <C1 fontWeight="bold">{getActionTypeLabel(log.actionType)}</C1>
                    </View>
                    <View>
                      <C2 color="#666">{formatDate(log.createdAt)}</C2>
                    </View>
                  </Horizontal>
                  <View>
                    <C1>{log.description}</C1>
                  </View>
                  {log.entityType && (
                    <View>
                      <C2>
                        {i18n.t('activity.entity')}: {log.entityType}
                        {log.entityId ? ` (${log.entityId})` : ''}
                      </C2>
                    </View>
                  )}
                </Vertical>
              </View>
            ))}

            {hasMore && (
              <Center paddingVertical={20}>
                <Button variant="outline" onClick={loadMore} disabled={loading} loading={loading && page > 0}>
                  {i18n.t('activity.load_more')}
                </Button>
              </Center>
            )}
          </Vertical>
        )}
      </Vertical>
    </AccountLayout>
  );
};

export default ActivityLogPage;

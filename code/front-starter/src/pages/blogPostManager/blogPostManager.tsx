import React from 'react';
import { Vertical, useMount } from 'app-studio';
import { showModal, Loader } from '@app-studio/web';
import 'src/components';
import * as AsyncStorage from 'src/utils/localstorage';
import { useAuthStore } from 'src/stores/AuthStore';
import { PostDetails, CommentSection } from './blogPostManager.elements';
import { useBlogPostManagerRequests } from './blogPostManager.requests';
import { useParams } from 'react-router-dom';

export default function BlogPostManagerPage() {
    const { Controller,Controller,createController }: any = useBlogPostManagerRequests();

    const {
        id: objectId
    } = useParams();

    const eventProps = {
        createController: createController
    };

    const requests = {
        Controller: Controller,
        Controller: Controller,
        createController: createController
    };

    useMount(() => {
        const redirectData = AsyncStorage.read('@redirectData');

        if (Object.keys(requests) && redirectData && redirectData.name && (redirectData.params && requests[redirectData.name])) {
            requests[redirectData.name].run(...redirectData.params);
            AsyncStorage.remove('@redirectData');
        }

        Controller.run(objectId);
        Controller.run();
    });

    return <Vertical height='100vh' alignItems='center' gap='50px'><PostDetails requests={requests} {...eventProps} /><CommentSection requests={requests} {...eventProps} /></Vertical>;
}
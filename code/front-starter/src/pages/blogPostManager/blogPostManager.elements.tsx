import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader, Text } from '@app-studio/web';
import { CommentFormForm } from 'src/forms';
import i18n from 'src/utils/locale';

export const PostDetails = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.Controller.data && requests.Controller.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}><PostContent {...props} />{requests?.Controller?.data?.length > 0 && requests?.Controller?.data.map(item => <PostComments key={item.id} item={item} {...props} />)}</Vertical>;
};

export const CommentSection = (
    {
        requests: requests,
        ...props
    }
) => {
    return <Vertical alignItems='center' gap={25}><CommentFormForm {...props} {...requests} /></Vertical>;
};

export const PostContent = (
    {
        ...props
    }
) => {
    return <Text>{i18n.t('BlogPostManager.PostContent.post_content_display.postText')}</Text>;
};

export const PostComments = (
    {
        item: item,
        ...props
    }
) => {
    return <Text>{i18n.t('BlogPostManager.PostComments.comment1.commentText')}</Text>;
};
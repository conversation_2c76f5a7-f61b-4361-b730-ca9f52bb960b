import {WorkflowService,ChatChatConversationService} from 'src/services/api';

export const useBlogPostManagerRequests = () => {
const Controller = WorkflowService.useIdWorkflowControllerCreateService();
const Controller = WorkflowService.useundefinedWorkflowControllerCreateService();
const createController = WorkflowService.useCreateWorkflowControllerCreateService();
    return {
      Controller,
      Controller,
      createController
    };
};

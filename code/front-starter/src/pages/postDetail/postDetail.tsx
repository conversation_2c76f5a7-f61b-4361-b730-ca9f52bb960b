import React from 'react';
import { Vertical, useMount } from 'app-studio';
import { showModal, Loader } from '@app-studio/web';
import 'src/components';
import * as AsyncStorage from 'src/utils/localstorage';
import { useAuthStore } from 'src/stores/AuthStore';
import { PostDisplay, CommentsSection } from './postDetail.elements';
import { usePostDetailRequests } from './postDetail.requests';
import { useParams } from 'react-router-dom';

export default function PostDetailPage() {
    const handleSuccessCreateController = data => {
        Controller.run({});
    };

    const { workflowControllerRead,Controller,createController }: any = usePostDetailRequests(handleSuccessCreateController);

    const {
        id: objectId
    } = useParams();

    const eventProps = {
        createController: createController
    };

    const requests = {
        workflowControllerRead: workflowControllerRead,
        Controller: Controller,
        createController: createController
    };

    useMount(() => {
        const redirectData = AsyncStorage.read('@redirectData');

        if (Object.keys(requests) && redirectData && redirectData.name && (redirectData.params && requests[redirectData.name])) {
            requests[redirectData.name].run(...redirectData.params);
            AsyncStorage.remove('@redirectData');
        }

        workflowControllerRead.run(objectId);
        Controller.run({objectId, objectType:"post"});
    });

    return <Vertical height='100vh' alignItems='center' gap='50px'><PostDisplay requests={requests} {...eventProps} /><CommentsSection requests={requests} {...eventProps} /></Vertical>;
}
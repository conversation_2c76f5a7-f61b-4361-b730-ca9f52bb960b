import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader, Text, Avatar } from '@app-studio/web';
import { NewCommentFormForm } from 'src/forms';
import i18n from 'src/utils/locale';
import ReactMarkdown from 'react-markdown';

export const PostDisplay = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.workflowControllerRead.data && requests.workflowControllerRead.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}><PostTitle data={requests?.workflowControllerRead?.data} {...props} /><AuthorInfo data={requests?.workflowControllerRead?.data} {...props} /><PostSummaryText data={requests?.workflowControllerRead?.data} {...props} /><PostContentMarkdown data={requests?.workflowControllerRead?.data} {...props} /></Vertical>;
};

export const CommentsSection = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.Controller.data && requests.Controller.isLoading || !requests.createController.data && requests.createController.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}>{requests?.Controller?.data?.length > 0 && requests?.Controller?.data.map(item => <CommentsList key={item.id} item={item} {...props} />)}<NewCommentFormForm {...props} {...requests} /></Vertical>;
};

export const PostTitle = (
    {
        data: data,
        ...props
    }
) => {
    return <Text>{data?.title}</Text>;
};

export const AuthorInfo = (
    {
        data: data,
        ...props
    }
) => {
    return <Horizontal alignItems='center' className='' gap={25}><Avatar src=''></Avatar><Text>{data?.user?.name}</Text></Horizontal>;
};

export const PostSummaryText = (
    {
        data: data,
        ...props
    }
) => {
    return <Text>{data?.summary}</Text>;
};

export const PostContentMarkdown = (
    {
        data: data,
        ...props
    }
) => {
    return (
        <ReactMarkdown
            components={{
                ul: (
                    {
                        children: children
                    }
                ) => <ul
                    style={{
                        paddingLeft: 20,
                        marginBottom: 8
                    }}>{children}</ul>,

                ol: (
                    {
                        children: children
                    }
                ) => <ol
                    style={{
                        paddingLeft: 20,
                        marginBottom: 8
                    }}>{children}</ol>,

                li: (
                    {
                        children: children
                    }
                ) => <li
                    style={{
                        marginBottom: 4,
                        listStyleType: 'disc'
                    }}>{children}</li>
            }}>{data?.content}</ReactMarkdown>
    );
};

export const CommentsList = (
    {
        item: item,
        ...props
    }
) => {
    return <Horizontal alignItems='center' className='comment_author_avatar' gap={25}><Avatar src=''></Avatar><Text>{item.tasks?.[item.tasks.length - 1]?.result?.input?.user.name}</Text><Text>{item.tasks?.[item.tasks.length - 1]?.result?.input?.content}</Text><Text>{item.tasks?.[item.tasks.length - 1]?.result?.input?.createdAt}</Text></Horizontal>;
};
import {WorkflowService,ChatChatConversationService} from 'src/services/api';

export const usePostDetailRequests = (handleSuccessCreateController) => {
const workflowControllerRead = WorkflowService.usePostWorkflowControllerReadService();
const Controller = WorkflowService.useFindWorkflowControllerCreateService();
const createController = WorkflowService.useCreateWorkflowControllerCreateService({
onSuccess: (data) => {
handleSuccessCreateController(data);
},
});
    return {
      workflowControllerRead,
      Controller,
      createController
    };
};

import React from 'react';
import { Vertical, Horizontal, Center } from 'app-studio';
import { H1, H2, C3 } from 'src/components/Text';
import { Button } from '@app-studio/web';
import { useNavigate } from 'react-router-dom';
import { SVGIcon } from 'src/components/Icon';

const NotFoundPage = () => {
  const navigate = useNavigate();

  return (
    <Center height="100vh" width="100%" backgroundColor="theme.gray.10">
      <Vertical
        gap={20}
        alignItems="center"
        padding={20}
        maxWidth="600px"
        backgroundColor="white"
        borderRadius={8}
        boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
      >
        <SVGIcon name="ErrorSvg" size={80} color="theme.error" />

        <H1 textAlign="center">404</H1>
        <H2 textAlign="center">Page Not Found</H2>

        <C3 textAlign="center" color="theme.gray.60">
          The page you are looking for does not exist or has been moved.
        </C3>

        <Horizontal gap={10} marginTop={20}>
          <Button onClick={() => navigate(-1)} variant="outline">
            Go Back
          </Button>

          <Button onClick={() => navigate('/')} colorScheme="theme.primary">
            Go Home
          </Button>
        </Horizontal>
      </Vertical>
    </Center>
  );
};

export default NotFoundPage;

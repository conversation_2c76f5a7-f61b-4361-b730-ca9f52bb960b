import React from 'react';
import { Vertical, Horizontal, Center } from 'app-studio';
import { H1, H2, C3 } from 'src/components/Text';
import { Button } from '@app-studio/web';
import { SVGIcon } from 'src/components/Icon';

const MaintenancePage = () => {
  const handleRefresh = () => {
    window.location.reload();
  };

  // Example maintenance window details
  const maintenanceDetails = {
    startTime: 'June 15, 2023 - 22:00 UTC',
    endTime: 'June 16, 2023 - 02:00 UTC',
    reason: 'System upgrades and performance improvements',
  };

  return (
    <Center height="100vh" width="100%" backgroundColor="theme.gray.10">
      <Vertical
        gap={20}
        alignItems="center"
        padding={20}
        maxWidth="600px"
        backgroundColor="white"
        borderRadius={8}
        boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
      >
        <SVGIcon name="SettingSvg" size={80} color="theme.warning" />

        <H1 textAlign="center">Scheduled Maintenance</H1>

        <C3 textAlign="center" color="theme.gray.60">
          Our system is currently undergoing scheduled maintenance. We apologize for any inconvenience this may cause.
        </C3>

        <Vertical gap={10} padding={20} backgroundColor="theme.gray.10" borderRadius={8} width="100%">
          <Horizontal justifyContent="space-between">
            <C3 fontWeight={500}>Start Time:</C3>
            <C3>{maintenanceDetails.startTime}</C3>
          </Horizontal>

          <Horizontal justifyContent="space-between">
            <C3 fontWeight={500}>End Time:</C3>
            <C3>{maintenanceDetails.endTime}</C3>
          </Horizontal>

          <Horizontal justifyContent="space-between">
            <C3 fontWeight={500}>Reason:</C3>
            <C3>{maintenanceDetails.reason}</C3>
          </Horizontal>
        </Vertical>

        <C3 textAlign="center">Please check back later. We will be back online shortly!</C3>

        <Button onClick={handleRefresh} colorScheme="theme.primary" marginTop={10}>
          Refresh Page
        </Button>
      </Vertical>
    </Center>
  );
};

export default MaintenancePage;

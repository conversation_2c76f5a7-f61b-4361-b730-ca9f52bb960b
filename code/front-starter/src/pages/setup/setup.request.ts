import { SetupService } from 'src/services/api';

export const useSetupWizardRequests = ({
  onStepsLoaded,
  onStatusLoaded,
  onProgressUpdated,
  onSetupCompleted,
}: {
  onStepsLoaded?: Function;
  onStatusLoaded?: Function;
  onProgressUpdated?: Function;
  onSetupCompleted?: Function;
}) => {
  const getStepsRequest = SetupService.useSetupControllerGetSetupStepsService({
    onSuccess: (data) => {
      if (onStepsLoaded) {
        onStepsLoaded(data);
      }
    },
  });

  const getStatusRequest = SetupService.useSetupControllerGetUserSetupStatusService({
    onSuccess: (data) => {
      if (onStatusLoaded) {
        onStatusLoaded(data);
      }
    },
  });

  const updateProgressRequest = SetupService.useSetupControllerUpdateSetupProgressService({
    onSuccess: (data) => {
      if (onProgressUpdated) {
        onProgressUpdated(data);
      }
    },
  });

  const completeSetupRequest = SetupService.useSetupControllerCompleteSetupService({
    onSuccess: (data) => {
      if (onSetupCompleted) {
        onSetupCompleted(data);
      }
    },
  });

  return {
    getStepsRequest,
    getStatusRequest,
    updateProgressRequest,
    completeSetupRequest,
  };
};

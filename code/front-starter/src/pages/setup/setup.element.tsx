import React from 'react';
import { Vertical, Horizontal, View, Text, Center } from 'app-studio';
import { Button, FormikTextField, FormikTextArea, FormikSelect, FormikForm } from '@app-studio/web';
import { Formik } from 'formik';
import { ProgressBar } from 'src/components/ProgressBar';
import i18n from 'src/utils/locale';

// Step components
export const WelcomeStep = ({ onNext }: { onNext: () => void }) => {
  return (
    <Vertical gap={20} alignItems="center" width="100%" padding={20}>
      <Text fontSize={24} fontWeight="bold">
        {i18n.t('setup.welcome.title')}
      </Text>
      <Text textAlign="center" maxWidth={600}>
        {i18n.t('setup.welcome.description')}
      </Text>
      <Button onClick={onNext}>{i18n.t('setup.welcome.getStarted')}</Button>
    </Vertical>
  );
};

export const ProfileStep = ({ onNext, onPrev, initialValues, onSubmit }: any) => {
  return (
    <Vertical gap={20} alignItems="center" width="100%" padding={20}>
      <Text fontSize={24} fontWeight="bold">
        {i18n.t('setup.profile.title')}
      </Text>
      <Text textAlign="center" maxWidth={600} marginBottom={20}>
        {i18n.t('setup.profile.description')}
      </Text>

      <Formik initialValues={initialValues} onSubmit={onSubmit}>
        {(formikProps) => (
          <FormikForm>
            <Vertical gap={20} width="100%" maxWidth={500}>
              <FormikTextField
                name="name"
                label={i18n.t('setup.profile.nameLabel')}
                placeholder={i18n.t('setup.profile.namePlaceholder')}
              />
              <FormikTextField
                name="job"
                label={i18n.t('setup.profile.jobLabel')}
                placeholder={i18n.t('setup.profile.jobPlaceholder')}
              />
              <FormikSelect
                name="country"
                label={i18n.t('setup.profile.countryLabel')}
                placeholder={i18n.t('setup.profile.countryPlaceholder')}
                options={[
                  { label: 'France', value: 'FR' },
                  { label: 'United States', value: 'US' },
                  { label: 'United Kingdom', value: 'UK' },
                  { label: 'Germany', value: 'DE' },
                  { label: 'Spain', value: 'ES' },
                ]}
              />
              <FormikTextArea
                name="bio"
                label={i18n.t('setup.profile.bioLabel')}
                placeholder={i18n.t('setup.profile.bioPlaceholder')}
              />
              <Horizontal gap={10} justifyContent="space-between" width="100%">
                <Button variant="outline" onClick={onPrev}>
                  {i18n.t('setup.common.back')}
                </Button>
                <Button onClick={formikProps.handleSubmit}>{i18n.t('setup.common.next')}</Button>
              </Horizontal>
            </Vertical>
          </FormikForm>
        )}
      </Formik>
    </Vertical>
  );
};

export const PreferencesStep = ({ onNext, onPrev, initialValues, onSubmit }: any) => {
  return (
    <Vertical gap={20} alignItems="center" width="100%" padding={20}>
      <Text fontSize={24} fontWeight="bold">
        {i18n.t('setup.preferences.title')}
      </Text>
      <Text textAlign="center" maxWidth={600} marginBottom={20}>
        {i18n.t('setup.preferences.description')}
      </Text>

      <Formik initialValues={initialValues} onSubmit={onSubmit}>
        {(formikProps) => (
          <FormikForm>
            <Vertical gap={20} width="100%" maxWidth={500}>
              <FormikSelect
                name="language"
                label={i18n.t('setup.preferences.languageLabel')}
                options={[
                  { label: 'English', value: 'en' },
                  { label: 'Français', value: 'fr' },
                ]}
              />
              <FormikSelect
                name="theme"
                label={i18n.t('setup.preferences.themeLabel')}
                options={[
                  { label: i18n.t('setup.preferences.themeLight'), value: 'light' },
                  { label: i18n.t('setup.preferences.themeDark'), value: 'dark' },
                  { label: i18n.t('setup.preferences.themeSystem'), value: 'system' },
                ]}
              />
              <Horizontal gap={10} justifyContent="space-between" width="100%">
                <Button variant="outline" onClick={onPrev}>
                  {i18n.t('setup.common.back')}
                </Button>
                <Button onClick={formikProps.handleSubmit}>{i18n.t('setup.common.next')}</Button>
              </Horizontal>
            </Vertical>
          </FormikForm>
        )}
      </Formik>
    </Vertical>
  );
};

export const CompleteStep = ({ onComplete }: { onComplete: () => void }) => {
  return (
    <Vertical gap={20} alignItems="center" width="100%" padding={20}>
      <Text fontSize={24} fontWeight="bold">
        {i18n.t('setup.complete.title')}
      </Text>
      <Text textAlign="center" maxWidth={600}>
        {i18n.t('setup.complete.description')}
      </Text>
      <Button onClick={onComplete}>{i18n.t('setup.complete.finishSetup')}</Button>
    </Vertical>
  );
};

// Wizard container
export const SetupWizardContainer = ({
  children,
  progress,
  currentStep,
  totalSteps,
}: {
  children: React.ReactNode;
  progress: number;
  currentStep: number;
  totalSteps: number;
}) => {
  return (
    <Vertical
      width="100%"
      height="100vh"
      backgroundColor="white"
      alignItems="center"
      justifyContent="center"
      padding={20}
    >
      <Vertical
        width="100%"
        maxWidth={800}
        minHeight={500}
        backgroundColor="white"
        borderRadius={10}
        boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
        padding={20}
        gap={20}
      >
        <Horizontal width="100%" justifyContent="space-between" alignItems="center">
          <Text fontSize={18} fontWeight="bold">
            {i18n.t('setup.common.step', { current: currentStep + 1, total: totalSteps })}
          </Text>
          <Text>{progress}%</Text>
        </Horizontal>
        <ProgressBar usageNumber={progress} totalUsage={100} />
        <Center flex={1} width="100%">
          {children}
        </Center>
      </Vertical>
    </Vertical>
  );
};

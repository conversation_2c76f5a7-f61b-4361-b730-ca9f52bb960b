import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMount } from 'app-studio';
import { Loader } from '@app-studio/web';
import { useSetupWizardState } from './setup.state';
import { useSetupWizardRequests } from './setup.request';
import { SetupWizardContainer, WelcomeStep, ProfileStep, PreferencesStep, CompleteStep } from './setup.element';
import { useAuthStore } from 'src/stores/AuthStore';

const SetupWizardPage = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const {
    steps,
    setSteps,
    currentStepIndex,
    setCurrentStepIndex,
    isLoading,
    setIsLoading,
    setupData,
    updateSetupData,
    goToNextStep,
    goToPreviousStep,
    getCurrentStep,
    setSetupCompleted,
    progress,
    setProgress,
    calculateProgress,
  } = useSetupWizardState();

  const { getStepsRequest, getStatusRequest, updateProgressRequest, completeSetupRequest } = useSetupWizardRequests({
    onStepsLoaded: (data) => {
      setSteps(data);
    },
    onStatusLoaded: (data) => {
      if (data.completed) {
        // If setup is already completed, redirect to home
        navigate('/home');
        return;
      }

      // Find the index of the current step
      const stepIndex = steps.findIndex((step) => step.id === data.currentStep);
      if (stepIndex !== -1) {
        setCurrentStepIndex(stepIndex);
      }
      setProgress(data.progress);
    },
    onProgressUpdated: (data) => {
      setProgress(data.progress);
    },
    onSetupCompleted: () => {
      setSetupCompleted(true);
      navigate('/home');
    },
  });

  useMount(() => {
    setIsLoading(true);
    getStepsRequest.run();
  });

  useEffect(() => {
    if (steps.length > 0) {
      getStatusRequest.run();
      setIsLoading(false);
    }
  }, [steps]);

  useEffect(() => {
    if (steps.length > 0) {
      const newProgress = calculateProgress();
      setProgress(newProgress);
    }
  }, [currentStepIndex, steps]);

  const handleStepSubmit = (stepId: string, values: any) => {
    updateSetupData(stepId, values);

    // Update progress on the server
    const newProgress = calculateProgress();
    updateProgressRequest.run({
      stepId: stepId,
      progress: newProgress,
    } as any);

    goToNextStep();
  };

  const handleComplete = () => {
    completeSetupRequest.run();
  };

  const renderCurrentStep = () => {
    const currentStep = getCurrentStep();
    if (!currentStep) return null;

    switch (currentStep.id) {
      case 'welcome':
        return <WelcomeStep onNext={goToNextStep} />;
      case 'profile':
        return (
          <ProfileStep
            onNext={goToNextStep}
            onPrev={goToPreviousStep}
            initialValues={
              setupData.profile || {
                name: user?.name || '',
                job: '',
                country: '',
                bio: '',
              }
            }
            onSubmit={(values) => handleStepSubmit('profile', values)}
          />
        );
      case 'preferences':
        return (
          <PreferencesStep
            onNext={goToNextStep}
            onPrev={goToPreviousStep}
            initialValues={
              setupData.preferences || {
                language: user?.language || 'en',
                theme: 'light',
              }
            }
            onSubmit={(values) => handleStepSubmit('preferences', values)}
          />
        );
      case 'complete':
        return <CompleteStep onComplete={handleComplete} />;
      default:
        return null;
    }
  };

  if (isLoading || getStepsRequest.loading) {
    return (
      <SetupWizardContainer progress={0} currentStep={0} totalSteps={1}>
        <Loader size="xl" />
      </SetupWizardContainer>
    );
  }

  return (
    <SetupWizardContainer progress={progress} currentStep={currentStepIndex} totalSteps={steps.length}>
      {renderCurrentStep()}
    </SetupWizardContainer>
  );
};

export default SetupWizardPage;

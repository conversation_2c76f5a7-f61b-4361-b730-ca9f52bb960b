import { useState } from 'react';

export const useSetupWizardState = () => {
  const [steps, setSteps] = useState<any[]>([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [setupData, setSetupData] = useState<Record<string, any>>({});
  const [setupCompleted, setSetupCompleted] = useState(false);
  const [progress, setProgress] = useState(0);

  const updateSetupData = (stepId: string, data: any) => {
    setSetupData((prevData) => ({
      ...prevData,
      [stepId]: data,
    }));
  };

  const goToNextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const getCurrentStep = () => {
    return steps[currentStepIndex] || null;
  };

  const calculateProgress = () => {
    return Math.round(((currentStepIndex + 1) / steps.length) * 100);
  };

  return {
    steps,
    setSteps,
    currentStepIndex,
    setCurrentStepIndex,
    isLoading,
    setIsLoading,
    setupData,
    updateSetupData,
    goToNextStep,
    goToPreviousStep,
    getCurrentStep,
    setupCompleted,
    setSetupCompleted,
    progress,
    setProgress,
    calculateProgress,
  };
};

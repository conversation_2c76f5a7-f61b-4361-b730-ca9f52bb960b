import React from 'react';
import { Horizontal, Vertical, View } from 'app-studio';
import { Checkbox } from '@app-studio/web';
import { C3, C4 } from 'src/components/Text';
import { SVGIcon } from 'src/components/Icon';
import { User, SortDirection } from './types';
import { Element } from 'app-studio';

// Table components
const TableContainer = (props: any) => <Element as="table" {...props} />;
const TableHead = (props: any) => <Element as="thead" {...props} />;
const TableRow = (props: any) => <Element as="tr" {...props} />;
const TableHeadCell = (props: any) => <Element as="th" {...props} />;
const TableBody = (props: any) => <Element as="tbody" {...props} />;
const TableCell = (props: any) => <Element as="td" {...props} />;

interface DataTableProps {
  data: User[];
  selectedUsers: string[];
  sortField: keyof User;
  sortDirection: SortDirection;
  onSort: (field: keyof User) => void;
  onSelectAll: (checked: boolean) => void;
  onSelectUser: (userId: string, checked: boolean) => void;
  isAllSelected: boolean;
}

export const DataTable: React.FC<DataTableProps> = ({
  data,
  selectedUsers,
  sortField,
  sortDirection,
  onSort,
  onSelectAll,
  onSelectUser,
  isAllSelected,
}) => {
  // Define columns
  const columns = [
    { id: 'name', label: 'Name', sortable: true, width: '20%' },
    { id: 'email', label: 'Email', sortable: true, width: '20%' },
    { id: 'status', label: 'Status', sortable: true, width: '10%' },
    { id: 'country', label: 'Country', sortable: true, width: '10%' },
    { id: 'job', label: 'Job', sortable: true, width: '20%' },
    { id: 'createdAt', label: 'Created At', sortable: true, width: '15%' },
  ];

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'theme.success';
      case 'inactive':
        return 'theme.warning';
      case 'blocked':
        return 'theme.error';
      default:
        return 'theme.gray.50';
    }
  };

  // Render sort icon
  const renderSortIcon = (columnId: keyof User) => {
    if (columnId !== sortField) {
      return <SVGIcon name="MenuSvg" size={14} color="theme.gray.50" />;
    }

    return sortDirection === 'asc' ? (
      <SVGIcon name="Expand_lessSvg" size={14} color="theme.primary" />
    ) : (
      <SVGIcon name="Expand_moreSvg" size={14} color="theme.primary" />
    );
  };

  return (
    <TableContainer
      width="100%"
      borderCollapse="collapse"
      borderRadius="8px"
      overflow="hidden"
      boxShadow="0 2px 8px rgba(0, 0, 0, 0.1)"
    >
      <TableHead
        backgroundColor="theme.gray.10"
        borderWidth="0px 0px 1px 0px"
        borderColor="theme.gray.30"
        borderStyle="solid"
      >
        <TableRow>
          <TableHeadCell width="5%" textAlign="center" padding="12px">
            <Checkbox isChecked={isAllSelected} onChange={(checked) => onSelectAll(checked)} />
          </TableHeadCell>

          {columns.map((column) => (
            <TableHeadCell
              key={column.id}
              width={column.width}
              textAlign="left"
              padding="12px"
              onClick={() => column.sortable && onSort(column.id as keyof User)}
              style={{ cursor: column.sortable ? 'pointer' : 'default' }}
            >
              <Horizontal alignItems="center" gap={5}>
                <C3 fontWeight={500}>{column.label}</C3>
                {column.sortable && renderSortIcon(column.id as keyof User)}
              </Horizontal>
            </TableHeadCell>
          ))}
        </TableRow>
      </TableHead>

      <TableBody>
        {data.length === 0 ? (
          <TableRow>
            <TableCell colSpan={columns.length + 1} textAlign="center" padding="20px">
              <C3>No data available</C3>
            </TableCell>
          </TableRow>
        ) : (
          data.map((user) => (
            <TableRow
              key={user.id}
              borderWidth="0px 0px 1px 0px"
              borderColor="theme.gray.20"
              borderStyle="solid"
              backgroundColor={selectedUsers.includes(user.id) ? 'rgba(0, 123, 255, 0.05)' : 'white'}
              _hover={{ backgroundColor: 'rgba(0, 123, 255, 0.05)' }}
            >
              <TableCell width="5%" textAlign="center" padding="12px">
                <Checkbox
                  isChecked={selectedUsers.includes(user.id)}
                  onChange={(checked) => onSelectUser(user.id, checked)}
                />
              </TableCell>

              <TableCell width="20%" padding="12px">
                <C3>{user.name}</C3>
              </TableCell>

              <TableCell width="20%" padding="12px">
                <C3>{user.email}</C3>
              </TableCell>

              <TableCell width="10%" padding="12px">
                <View
                  backgroundColor={getStatusColor(user.status)}
                  padding="4px 8px"
                  borderRadius="4px"
                  width="fit-content"
                >
                  <C4 color="white" textTransform="capitalize">
                    {user.status}
                  </C4>
                </View>
              </TableCell>

              <TableCell width="10%" padding="12px">
                <C3>{user.country}</C3>
              </TableCell>

              <TableCell width="20%" padding="12px">
                <C3>{user.job}</C3>
              </TableCell>

              <TableCell width="15%" padding="12px">
                <C3>{formatDate(user.createdAt)}</C3>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </TableContainer>
  );
};

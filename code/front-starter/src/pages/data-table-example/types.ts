import { SvgNameProp } from 'src/types/svg';

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'blocked';
  country: string;
  job: string;
  createdAt: Date;
}

export type SortDirection = 'asc' | 'desc';

export interface Column {
  id: keyof User;
  label: string;
  sortable: boolean;
  width?: string;
}

export interface BulkAction {
  id: string;
  label: string;
  icon: SvgNameProp;
  colorScheme: string;
  action: (selectedIds: string[]) => void;
}

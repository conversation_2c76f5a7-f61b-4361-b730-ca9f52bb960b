import { User } from './types';

export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    country: 'US',
    job: 'Software Developer',
    createdAt: new Date('2023-01-15'),
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    country: 'UK',
    job: 'Product Manager',
    createdAt: new Date('2023-02-20'),
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    status: 'inactive',
    country: 'CA',
    job: 'Data Scientist',
    createdAt: new Date('2023-03-10'),
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    status: 'inactive',
    country: 'AU',
    job: 'UX Designer',
    createdAt: new Date('2023-04-05'),
  },
  {
    id: '5',
    name: '<PERSON>',
    email: 'robert.w<PERSON><PERSON>@example.com',
    role: 'user',
    status: 'blocked',
    country: 'DE',
    job: 'Project Manager',
    createdAt: new Date('2023-05-12'),
  },
  {
    id: '6',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    country: 'FR',
    job: 'Marketing Specialist',
    createdAt: new Date('2023-06-18'),
  },
  {
    id: '7',
    name: 'William Taylor',
    email: '<EMAIL>',
    role: 'user',
    status: 'blocked',
    country: 'ES',
    job: 'Sales Manager',
    createdAt: new Date('2023-07-22'),
  },
  {
    id: '8',
    name: 'Sophia Martinez',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    country: 'IT',
    job: 'Content Writer',
    createdAt: new Date('2023-08-30'),
  },
];

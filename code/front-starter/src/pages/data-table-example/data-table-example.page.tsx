import React, { useState, useEffect } from 'react';
import { Horizontal, Vertical, View } from 'app-studio';
import { Button, Checkbox, showMessage } from '@app-studio/web';
import { H3, C3, C4 } from 'src/components/Text';
import { SVGIcon } from 'src/components/Icon';
import i18n from 'src/utils/locale';
import { DataTable } from './data-table-example.element';
import { mockUsers } from './mock-data';
import { User, SortDirection, BulkAction } from './types';

export const DataTableExamplePage = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [sortField, setSortField] = useState<keyof User>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  // Load mock data
  useEffect(() => {
    setUsers(mockUsers);
  }, []);

  // Handle sorting
  const handleSort = (field: keyof User) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sorted and paginated data
  const getSortedData = () => {
    const sortedData = [...users].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        return sortDirection === 'asc' ? aValue.getTime() - bValue.getTime() : bValue.getTime() - aValue.getTime();
      }

      return 0;
    });

    // Paginate
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  };

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const currentPageIds = getSortedData().map((user) => user.id);
      setSelectedUsers(currentPageIds);
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers([...selectedUsers, userId]);
    } else {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId));
    }
  };

  const isAllSelected = () => {
    const currentPageIds = getSortedData().map((user) => user.id);
    return currentPageIds.length > 0 && currentPageIds.every((id) => selectedUsers.includes(id));
  };

  // Bulk actions
  const handleDelete = () => {
    if (selectedUsers.length === 0) {
      showMessage('warning', 'Warning', 'No users selected');
      return;
    }

    // Filter out selected users
    const updatedUsers = users.filter((user) => !selectedUsers.includes(user.id));
    setUsers(updatedUsers);
    setSelectedUsers([]);
    showMessage('success', 'Success', `${selectedUsers.length} users deleted`);
  };

  const handleActivate = () => {
    if (selectedUsers.length === 0) {
      showMessage('warning', 'Warning', 'No users selected');
      return;
    }

    // Update status of selected users
    const updatedUsers = users.map((user) => {
      if (selectedUsers.includes(user.id)) {
        return { ...user, status: 'active' as const };
      }
      return user;
    });

    setUsers(updatedUsers);
    showMessage('success', 'Success', `${selectedUsers.length} users activated`);
  };

  const handleDeactivate = () => {
    if (selectedUsers.length === 0) {
      showMessage('warning', 'Warning', 'No users selected');
      return;
    }

    // Update status of selected users
    const updatedUsers = users.map((user) => {
      if (selectedUsers.includes(user.id)) {
        return { ...user, status: 'inactive' as const };
      }
      return user;
    });

    setUsers(updatedUsers);
    showMessage('success', 'Success', `${selectedUsers.length} users deactivated`);
  };

  const handleBlock = () => {
    if (selectedUsers.length === 0) {
      showMessage('warning', 'Warning', 'No users selected');
      return;
    }

    // Update status of selected users
    const updatedUsers = users.map((user) => {
      if (selectedUsers.includes(user.id)) {
        return { ...user, status: 'blocked' as const };
      }
      return user;
    });

    setUsers(updatedUsers);
    showMessage('success', 'Success', `${selectedUsers.length} users blocked`);
  };

  // Define bulk actions
  const bulkActions: BulkAction[] = [
    {
      id: 'delete',
      label: 'Delete Selected',
      icon: 'GarbageSvg',
      colorScheme: 'theme.error',
      action: handleDelete,
    },
    {
      id: 'activate',
      label: 'Activate Selected',
      icon: 'CheckedSvg',
      colorScheme: 'theme.success',
      action: handleActivate,
    },
    {
      id: 'deactivate',
      label: 'Deactivate Selected',
      icon: 'PauseSvg',
      colorScheme: 'theme.warning',
      action: handleDeactivate,
    },
    {
      id: 'block',
      label: 'Block Selected',
      icon: 'CloseSvg',
      colorScheme: 'theme.error',
      action: handleBlock,
    },
  ];

  // Pagination handlers
  const totalPages = Math.ceil(users.length / itemsPerPage);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  return (
    <Vertical padding={20} gap={20}>
      <H3>Data Table Example with Bulk Actions</H3>

      {/* Bulk action buttons */}
      <Horizontal gap={10} flexWrap="wrap">
        {bulkActions.map((action) => (
          <Button
            key={action.id}
            onClick={action.action}
            colorScheme={action.colorScheme}
            leftIcon={<SVGIcon name={action.icon} size={16} color="white" />}
            isDisabled={selectedUsers.length === 0}
          >
            {action.label}
          </Button>
        ))}
      </Horizontal>

      {/* Selected count */}
      {selectedUsers.length > 0 && (
        <View backgroundColor="theme.primary" color="white" padding="8px 16px" borderRadius="4px" width="fit-content">
          <C3 color="white">{selectedUsers.length} users selected</C3>
        </View>
      )}

      {/* Data table */}
      <DataTable
        data={getSortedData()}
        selectedUsers={selectedUsers}
        sortField={sortField}
        sortDirection={sortDirection}
        onSort={handleSort}
        onSelectAll={handleSelectAll}
        onSelectUser={handleSelectUser}
        isAllSelected={isAllSelected()}
      />

      {/* Pagination */}
      <Horizontal justifyContent="space-between" alignItems="center">
        <C4>
          Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, users.length)} of{' '}
          {users.length} entries
        </C4>
        <Horizontal gap={10}>
          <Button
            onClick={handlePreviousPage}
            isDisabled={currentPage === 1}
            variant="outline"
            size="sm"
            leftIcon={<SVGIcon name="Expand_lessSvg" size={16} />}
          >
            Previous
          </Button>
          <C3>
            {currentPage} of {totalPages}
          </C3>
          <Button
            onClick={handleNextPage}
            isDisabled={currentPage === totalPages}
            variant="outline"
            size="sm"
            rightIcon={<SVGIcon name="Expand_moreSvg" size={16} />}
          >
            Next
          </Button>
        </Horizontal>
      </Horizontal>
    </Vertical>
  );
};

export default DataTableExamplePage;

import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader, Text, Button } from '@app-studio/web';
import { PageTitleForm, PageDescriptionForm, PostListIntroductionForm, PostListForm } from 'src/forms';
import i18n from 'src/utils/locale';

export const BlogHeader = (
    {
        requests: requests,
        ...props
    }
) => {
    return <Horizontal alignItems='center' gap={25}><PageTitle {...props} /><PageDescription {...props} /></Horizontal>;
};

export const PostListings = (
    {
        requests: requests,
        ...props,
        workflowControllerFind: workflowControllerFind
    }
) => {
    return !requests.workflowControllerFind.data && requests.workflowControllerFind.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}><PostListIntroduction {...props} />{requests?.workflowControllerFind?.data?.length > 0 && requests?.workflowControllerFind?.data.map(item => <PostList key={item.id} item={item} {...props} />)}</Vertical>;
};

export const PageTitle = (
    {
        ...props
    }
) => {
    return <Text>{i18n.t('BlogPosts.PageTitle.undefined.title')}</Text>;
};

export const PageDescription = (
    {
        ...props
    }
) => {
    return <Text>{i18n.t('BlogPosts.PageDescription.undefined.descriptionText')}</Text>;
};

export const PostListIntroduction = (
    {
        ...props
    }
) => {
    return <Text>{i18n.t('BlogPosts.PostListIntroduction.undefined.intro')}</Text>;
};

export const PostList = (
    {
        item: item,
        ...props
    }
) => {
    return <Horizontal alignItems='center' className='post_entry' gap={25}><Button isAuto className='post_entry' onClick={() => props.post_entryClick(item)}>{item.tasks?.[item.tasks.length - 1]?.result?.input?.title}</Button><Text>{item.tasks?.[item.tasks.length - 1]?.result?.input?.summary}</Text></Horizontal>;
};
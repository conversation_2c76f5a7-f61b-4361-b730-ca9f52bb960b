import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader, PostCard } from '@app-studio/web';
import 'src/forms';
import i18n from 'src/utils/locale';

export const PostList = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.workflowControllerFind.data && requests.workflowControllerFind.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}>{requests?.workflowControllerFind?.data?.length > 0 && requests?.workflowControllerFind?.data.map(item => <PostListView key={item.id} item={item} {...props} />)}</Vertical>;
};

export const PostListView = (
    {
        item: item,
        ...props
    }
) => {
    return <PostCard></PostCard>;
};
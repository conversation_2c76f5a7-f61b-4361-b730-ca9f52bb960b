import React from 'react';
import { Vertical, useMount } from 'app-studio';
import { showModal, Loader } from '@app-studio/web';
import 'src/components';
import * as AsyncStorage from 'src/utils/localstorage';
import { useAuthStore } from 'src/stores/AuthStore';
import { PostList } from './blogPostList.elements';
import { useBlogPostListRequests } from './blogPostList.requests';
import { useNavigate } from 'react-router-dom';

export default function BlogPostListPage() {
    const navigate = useNavigate();

    const handleSuccessWorkflowControllerFind = data => {
        navigate('/posts');
    };

    const { workflowControllerFind }: any = useBlogPostListRequests(handleSuccessWorkflowControllerFind);
    const eventProps = {};

    const requests = {
        workflowControllerFind: workflowControllerFind
    };

    useMount(() => {
        const redirectData = AsyncStorage.read('@redirectData');

        if (Object.keys(requests) && redirectData && redirectData.name && (redirectData.params && requests[redirectData.name])) {
            requests[redirectData.name].run(...redirectData.params);
            AsyncStorage.remove('@redirectData');
        }

        workflowControllerFind.run({ownerType: 'post'});
    });

    return <Vertical height='100vh' alignItems='center' gap='50px'><PostList requests={requests} {...eventProps} /></Vertical>;
}
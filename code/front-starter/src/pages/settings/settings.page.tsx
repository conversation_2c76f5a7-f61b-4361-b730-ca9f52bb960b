import { useAuthStore } from 'src/stores/AuthStore';
import { useMount, View, Horizontal, Vertical, Text } from 'app-studio';
import { Button, Tabs } from '@app-studio/web';
import { useNavigate } from 'react-router-dom';
import i18n from 'src/utils/locale';
import { AuthService } from 'src/services/api';
import { Right } from 'src/components/Layout';
import { useState } from 'react';

// Import settings components
import ProfileSettings from 'src/components/settings/ProfileSettings';
import AIModelSettings from 'src/components/settings/AIModelSettings';
import InterfaceSettings from 'src/components/settings/InterfaceSettings';
import NotificationSettings from 'src/components/settings/NotificationSettings';
import PrivacySettings from 'src/components/settings/PrivacySettings';

function SettingsPage() {
  const MeService = AuthService.useAuthUserControllerMeService();
  const { logout } = useAuthStore();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [activeTab, setActiveTab] = useState('profile');

  useMount(() => {
    MeService.run();
  });

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return <ProfileSettings />;
      case 'ai-models':
        return <AIModelSettings />;
      case 'interface':
        return <InterfaceSettings />;
      case 'notifications':
        return <NotificationSettings />;
      case 'privacy':
        return <PrivacySettings />;
      default:
        return <ProfileSettings />;
    }
  };

  return (
    <SettingsInfoContainer>
      <Horizontal justifyContent="space-between" alignItems="center">
        <StudioHeading>{i18n.t('page.settings.info')}</StudioHeading>
        <Button
          onClick={() => {
            logout();
            navigate('/');
          }}
        >
          {i18n.t('action.logout')}
        </Button>
      </Horizontal>

      <ProfileForm>
        <Horizontal marginBottom={20}>
          <SettingsTab active={activeTab === 'profile'} onClick={() => setActiveTab('profile')}>
            {i18n.t('page.settings.tabs.profile')}
          </SettingsTab>
          <SettingsTab active={activeTab === 'ai-models'} onClick={() => setActiveTab('ai-models')}>
            {i18n.t('page.settings.tabs.aiModels')}
          </SettingsTab>
          <SettingsTab active={activeTab === 'interface'} onClick={() => setActiveTab('interface')}>
            {i18n.t('page.settings.tabs.interface')}
          </SettingsTab>
          <SettingsTab active={activeTab === 'notifications'} onClick={() => setActiveTab('notifications')}>
            {i18n.t('page.settings.tabs.notifications')}
          </SettingsTab>
          <SettingsTab active={activeTab === 'privacy'} onClick={() => setActiveTab('privacy')}>
            {i18n.t('page.settings.tabs.privacy')}
          </SettingsTab>
        </Horizontal>

        <SettingsContent>{renderTabContent()}</SettingsContent>
      </ProfileForm>
    </SettingsInfoContainer>
  );
}

const SettingsInfoContainer = (props) => (
  <View
    display={'flex'}
    flex={'0 0 auto'}
    flexDirection={'column'}
    alignItems={'stretch'}
    justifyContent={'flex-start'}
    marginTop={32}
    padding={20}
    maxWidth={1200}
    marginLeft="auto"
    marginRight="auto"
    {...props}
  />
);

const StudioHeading = (props) => <View flex={'0 0 auto'} font={'600 24px '} color={'rgba(19, 15, 38, 1)'} {...props} />;

const ProfileForm = (props) => (
  <View
    display={'flex'}
    flex={'0 0 auto'}
    flexDirection={'column'}
    alignItems={'stretch'}
    justifyContent={'flex-start'}
    padding={'20px 19.5px'}
    marginTop={20}
    background={'rgba(255, 255, 255, 1)'}
    border={'1px solid rgba(237, 237, 238, 1)'}
    borderRadius={8}
    boxShadow={'0px 1px 2px rgba(19, 15, 38, 0.04)'}
    {...props}
  />
);

const SettingsTab = ({ active, children, onClick }) => (
  <View
    as="button"
    padding="10px 15px"
    marginRight={10}
    borderRadius="4px 4px 0 0"
    backgroundColor={active ? 'rgba(255, 255, 255, 1)' : 'rgba(245, 245, 245, 1)'}
    borderWidth={1}
    borderStyle="solid"
    borderColor={active ? 'rgba(237, 237, 238, 1)' : 'transparent'}
    borderBottomWidth={active ? 0 : 1}
    borderBottomColor={active ? 'transparent' : 'rgba(237, 237, 238, 1)'}
    cursor="pointer"
    fontWeight={active ? 600 : 400}
    color={active ? 'rgba(19, 15, 38, 1)' : 'rgba(100, 100, 100, 1)'}
    onClick={onClick}
  >
    {children}
  </View>
);

const SettingsContent = (props) => (
  <View
    padding={20}
    backgroundColor="white"
    borderWidth={1}
    borderStyle="solid"
    borderColor="rgba(237, 237, 238, 1)"
    borderRadius="0 4px 4px 4px"
    {...props}
  />
);

export default SettingsPage;

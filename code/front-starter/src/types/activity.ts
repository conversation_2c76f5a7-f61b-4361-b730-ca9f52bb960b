export enum ActivityActionType {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  UPLOAD = 'UPLOAD',
  DOWNLOAD = 'DOWNLOAD',
  SHARE = 'SHARE',
  COMMENT = 'COMMENT',
  LIKE = 'LIKE',
  PAYMENT = 'PAYMENT',
  SUBSCRIPTION = 'SUBSCRIPTION',
  SETTINGS_CHANGE = 'SETTINGS_CHANGE',
  OTHER = 'OTHER',
}

export interface ActivityLog {
  id: string;
  userId: string;
  actionType: ActivityActionType;
  entityType: string;
  entityId?: string;
  description: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

export interface ActivitySummary {
  groupedByDate: Record<string, ActivityLog[]>;
  actionTypeCounts: Record<ActivityActionType, number>;
}

export interface DashboardActivityResponse {
  data: ActivityLog[];
  summary?: ActivitySummary;
}

export interface UserActivityLogsResponse {
  data: ActivityLog[];
  total: number;
}

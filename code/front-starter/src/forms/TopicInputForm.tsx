
        
    import React from 'react';
    import { Text, Horizontal, Vertical } from 'app-studio';
    import { <PERSON>ton, FormikForm, FormikTextField } from '@app-studio/web';
    import { Formik } from 'formik';
    import * as Yup from 'yup';
    import { MediaUploader } from 'src/components';
    
  

        const validationSchema = Yup.object().shape({
  topic: Yup.string().nullable().required('This field is required.').min(5, 'Must be at least 5 characters.').max(255, 'Cannot be more than 255 characters.')
});


        export const TopicInputForm = (props) => {
          
          

          const handleSubmit = (values, actions) => {
            props.postWorkflowControllerCreate.run()
          };

          return (
            <Formik
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
              initialValues={{"topic":""}}
            >
              {({ handleSubmit, isSubmitting }) => (
                <FormikForm>
                  <Vertical
                    media={{
                      mobile: { paddingBottom: 20 }
                    }}
                    width="100%"
                    height="100%"
                    alignItems="center"
                    justifyContent="center"
                    paddingTop={30}
                    gap={30}
                  >
                    <Vertical
                      gap={30}
                      flexWrap="wrap"
                      alignItems="flex-start"
                      media={{
                        mobile: { flexDirection: 'column', width: '100%', padding: '30px 0' },
                        desktop: { width: 'fit-content' },
                        tablet: { width: 'fit-content' }
                      }}
                    >
                      
        <Horizontal gap={15} alignItems="center">
          <Text fontSize={18} fontWeight={700}>Post Topic</Text>
          <FormikTextField { ...{"data-testid":"topic_input_01","name":"topic","id":"post_topic_input","eventId":"post_topic_input","component":"FormikTextField","fieldName":"topic","type":"string","label":"Post Topic","placeholder":"Enter the topic here","data":{"value":""},"validations":{"required":{"enabled":true,"requiredMessageError":"This field is required."},"minLength":{"enabled":true,"value":5,"invalidMessageError":"Must be at least 5 characters."},"maxLength":{"enabled":true,"value":255,"invalidMessageError":"Cannot be more than 255 characters."}}}}   />
        </Horizontal>
      
                    </Vertical>
                    <Button
        id="generate_post_button"
        role="submit_button_01"
        type="submit"
        color="white"
        whiteSpace="nowrap"
        onClick={handleSubmit}
        isLoading={props?.postWorkflowControllerCreate?.loading}
        borderRadius={13.5}
        width={150}
        height={45}
        cursor={props?.postWorkflowControllerCreate?.loading ? "not-allowed" : "pointer"}
        backgroundColor={props?.postWorkflowControllerCreate?.loading ? "gray" : "black"}
        isAuto
      >
        <Text>Submit</Text>
      </Button>
                  </Vertical>
                </FormikForm>
              )}
            </Formik>
          );
        };
      
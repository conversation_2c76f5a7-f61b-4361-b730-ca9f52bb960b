
        
    import React from 'react';
    import { Text, Horizontal, Vertical } from 'app-studio';
    import { <PERSON>ton, FormikForm, FormikTextArea } from '@app-studio/web';
    import { Formik } from 'formik';
    import * as Yup from 'yup';
    import { MediaUploader } from 'src/components';
    
  

        const validationSchema = Yup.object().shape({
  topic: Yup.string().nullable().required('Blog post topic is required.').min(10, 'Topic must be at least 10 characters long.').max(255, 'Topic cannot exceed 255 characters.')
});


        export const GeneratePostFormForm = (props) => {
          
          

          const handleSubmit = (values, actions) => {
            props.postWorkflowControllerCreate.run({
      ...values
    })
          };

          return (
            <Formik
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
              initialValues={{"topic":""}}
            >
              {({ handleSubmit, isSubmitting }) => (
                <FormikForm>
                  <Vertical
                    media={{
                      mobile: { paddingBottom: 20 }
                    }}
                    width="100%"
                    height="100%"
                    alignItems="center"
                    justifyContent="center"
                    paddingTop={30}
                    gap={30}
                  >
                    <Vertical
                      gap={30}
                      flexWrap="wrap"
                      alignItems="flex-start"
                      media={{
                        mobile: { flexDirection: 'column', width: '100%', padding: '30px 0' },
                        desktop: { width: 'fit-content' },
                        tablet: { width: 'fit-content' }
                      }}
                    >
                      
        <Vertical gap={15} alignItems="flex-start">
          <Text fontSize={18} fontWeight={700}>Topic</Text>
          <FormikTextArea { ...{"data-testid":"post-topic-input","name":"topic","id":"post_topic_input","eventId":"post_topic_input","component":"FormikTextArea","fieldName":"topic","type":"string","label":"Topic","placeholder":"Enter the topic for your blog post...","data":{"value":""},"validations":{"required":{"enabled":true,"requiredMessageError":"Blog post topic is required."},"minLength":{"enabled":true,"value":10,"invalidMessageError":"Topic must be at least 10 characters long."},"maxLength":{"enabled":true,"value":255,"invalidMessageError":"Topic cannot exceed 255 characters."}}}}   />
        </Vertical>
      
                    </Vertical>
                    <Button
        id="generate_post_button"
        role="generate-post-button"
        type="submit"
        color="white"
        whiteSpace="nowrap"
        onClick={handleSubmit}
        isLoading={props?.postWorkflowControllerCreate?.loading}
        borderRadius={13.5}
        width={150}
        height={45}
        cursor={props?.postWorkflowControllerCreate?.loading ? "not-allowed" : "pointer"}
        backgroundColor={props?.postWorkflowControllerCreate?.loading ? "gray" : "black"}
        isAuto
      >
        <Text>Generate</Text>
      </Button>
                  </Vertical>
                </FormikForm>
              )}
            </Formik>
          );
        };
      
import React, { useEffect, useState } from 'react';
import { Formik } from 'formik';
import i18n from 'src/utils/locale';
import { Button, FormikForm, FormikTextField, FormikSelect, FormikTextArea } from '@app-studio/web';
import * as Yup from 'yup';
import { ProfileService } from 'src/services/api';
import { useAuthStore } from 'src/stores/AuthStore';
import { Label } from 'src/components/Label';
import { View, Horizontal, Vertical } from 'app-studio';
import { FormItem } from 'src/components/Form';
import { Left } from 'src/components/Layout';
import { UpdateProfileParams } from 'src/services/api/models/UpdateProfileParams';

const validationSchema = Yup.object().shape({
  job: Yup.string().nullable(),
  country: Yup.string().nullable(),
  gender: Yup.string().nullable(),
  instagram: Yup.string().nullable(),
  facebook: Yup.string().nullable(),
});

interface ProfileFormProps {
  onSuccess?: () => void;
}

const ProfileForm: React.FC<ProfileFormProps> = ({ onSuccess }) => {
  const { user } = useAuthStore();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const { run: fetchProfile } = ProfileService.useProfileControllerMeService({
    onSuccess: (data) => {
      setProfile(data);
      setLoading(false);
    },
    onError: () => {
      setLoading(false);
    },
  });

  const { loading: updateLoading, run: updateProfile } = ProfileService.useProfileControllerUpdateService({
    onSuccess: () => {
      useAuthStore.getState().refresh();
      fetchProfile();
      if (onSuccess) {
        onSuccess();
      }
    },
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  if (!user || loading) return <View>Loading...</View>;

  const genderOptions = [
    { value: 'male', label: i18n.t('form.profile.gender.options.male') },
    { value: 'female', label: i18n.t('form.profile.gender.options.female') },
    { value: 'other', label: i18n.t('form.profile.gender.options.other') },
    { value: 'preferNotToSay', label: i18n.t('form.profile.gender.options.preferNotToSay') },
  ];

  const handleSubmit = (values: UpdateProfileParams) => {
    updateProfile(values);
  };

  return (
    <Formik
      onSubmit={handleSubmit}
      validationSchema={validationSchema}
      initialValues={{
        job: profile?.job || '',
        country: profile?.country || '',
        gender: profile?.gender || '',
        instagram: profile?.instagram || '',
        facebook: profile?.facebook || '',
      }}
      enableReinitialize
    >
      {({ errors, handleSubmit, isSubmitting, values }) => {
        return (
          <FormikForm>
            <Vertical gap={20}>
              <FormItem>
                <Label>{i18n.t('form.profile.job.label')}</Label>
                <FormikTextField
                  placeholder={i18n.t('form.profile.job.placeholder')}
                  name="job"
                  shape={'rounded'}
                  variant={'outline'}
                />
              </FormItem>

              <FormItem>
                <Label>{i18n.t('form.profile.country.label')}</Label>
                <FormikTextField
                  placeholder={i18n.t('form.profile.country.placeholder')}
                  name="country"
                  shape={'rounded'}
                  variant={'outline'}
                />
              </FormItem>

              <FormItem>
                <Label>{i18n.t('form.profile.gender.label')}</Label>
                <FormikSelect
                  placeholder={i18n.t('form.profile.gender.placeholder')}
                  name="gender"
                  shape={'rounded'}
                  variant={'outline'}
                  options={genderOptions}
                />
              </FormItem>

              <Horizontal gap={20} width="100%">
                <FormItem flex={1}>
                  <Label>{i18n.t('form.profile.instagram.label')}</Label>
                  <FormikTextField
                    placeholder={i18n.t('form.profile.instagram.placeholder')}
                    name="instagram"
                    shape={'rounded'}
                    variant={'outline'}
                  />
                </FormItem>

                <FormItem flex={1}>
                  <Label>{i18n.t('form.profile.facebook.label')}</Label>
                  <FormikTextField
                    placeholder={i18n.t('form.profile.facebook.placeholder')}
                    name="facebook"
                    shape={'rounded'}
                    variant={'outline'}
                  />
                </FormItem>
              </Horizontal>

              <Left marginVertical={20}>
                <Button isLoading={updateLoading} type="submit" width={200} onClick={handleSubmit}>
                  {i18n.t('button.updateProfile')}
                </Button>
              </Left>
            </Vertical>
          </FormikForm>
        );
      }}
    </Formik>
  );
};

export default ProfileForm;

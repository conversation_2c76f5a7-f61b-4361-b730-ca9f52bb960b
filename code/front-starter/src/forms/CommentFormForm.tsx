
        
    import React from 'react';
    import { Text, Horizontal, Vertical } from 'app-studio';
    import { Button, FormikForm, FormikTextArea } from '@app-studio/web';
    import { Formik } from 'formik';
    import * as Yup from 'yup';
    import { MediaUploader } from 'src/components';
    import { useParams } from 'react-router-dom'
  

        const validationSchema = Yup.object().shape({
  content: Yup.string().nullable().required('This field is required.').min(10, 'Must be at least 10 characters.').max(255, 'Cannot be more than 255 characters.')
});


        export const CommentFormForm = (props) => {
          
          const {id:objectId} = useParams();

          const handleSubmit = (values, actions) => {
            props.createController.run({
      ...values,
      objectId,
      objectType: "post",
    })
          };

          return (
            <Formik
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
              initialValues={{"content":""}}
            >
              {({ handleSubmit, isSubmitting }) => (
                <FormikForm>
                  <Vertical
                    media={{
                      mobile: { paddingBottom: 20 }
                    }}
                    width="100%"
                    height="100%"
                    alignItems="center"
                    justifyContent="center"
                    paddingTop={30}
                    gap={30}
                  >
                    <Vertical
                      gap={30}
                      flexWrap="wrap"
                      alignItems="flex-start"
                      media={{
                        mobile: { flexDirection: 'column', width: '100%', padding: '30px 0' },
                        desktop: { width: 'fit-content' },
                        tablet: { width: 'fit-content' }
                      }}
                    >
                      
        <Vertical gap={15} alignItems="flex-start">
          <Text fontSize={18} fontWeight={700}>Comment</Text>
          <FormikTextArea { ...{"data-testid":"comment-textarea","name":"content","id":"comment_input","eventId":"comment_input","component":"FormikTextArea","fieldName":"content","type":"string","label":"Comment","placeholder":"Write your comment here...","data":{"value":""},"validations":{"required":{"enabled":true,"requiredMessageError":"This field is required."},"minLength":{"enabled":true,"value":10,"invalidMessageError":"Must be at least 10 characters."},"maxLength":{"enabled":true,"value":255,"invalidMessageError":"Cannot be more than 255 characters."}}}}   />
        </Vertical>
      
                    </Vertical>
                    <Button
        id="submit_comment_button"
        role="submit-comment-button"
        type="submit"
        color="white"
        whiteSpace="nowrap"
        onClick={handleSubmit}
        isLoading={props?.createController?.loading}
        borderRadius={13.5}
        width={150}
        height={45}
        cursor={props?.createController?.loading ? "not-allowed" : "pointer"}
        backgroundColor={props?.createController?.loading ? "gray" : "black"}
        isAuto
      >
        <Text>Submit</Text>
      </Button>
                  </Vertical>
                </FormikForm>
              )}
            </Formik>
          );
        };
      
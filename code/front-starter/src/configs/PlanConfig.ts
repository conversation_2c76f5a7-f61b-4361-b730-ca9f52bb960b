
    export type PaymentType = 'subscription' | 'one-time-payment' | 'credit' | 'cart';

    export const PAYMENT_TYPES: PaymentType[] = ['subscription', 'one-time-payment', 'credit', 'cart'];

    export const PLAN_CONFIG = {
  "paymentType": "credit" as PaymentType,
  "options": {
    "smallPack": {
      "id": "smallPack",
      "name": "Small Credit Pack",
      "description": "Get 100 credits to unlock exclusive posts.",
      "price": 9.99,
      "currency": "usd",
      "interval": null,
      "credits": 100,
      "buttonLabel": "Buy 100 Credits"
    },
    "mediumPack": {
      "id": "mediumPack",
      "name": "Medium Credit Pack",
      "description": "Get 500 credits to unlock more exclusive content.",
      "price": 39.99,
      "currency": "usd",
      "interval": null,
      "credits": 500,
      "buttonLabel": "Buy 500 Credits"
    },
    "largePack": {
      "id": "largePack",
      "name": "Large Credit Pack",
      "description": "Get 1000 credits for extensive access to exclusive posts.",
      "price": 69.99,
      "currency": "usd",
      "interval": null,
      "credits": 1000,
      "buttonLabel": "Buy 1000 Credits"
    }
  },
  "creditNeeded": 1,
  "paymentEntity": "post"
};
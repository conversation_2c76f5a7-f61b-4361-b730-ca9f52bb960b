
import i18n from 'src/utils/locale';
import { HomeIcon, EditIcon, PlusIcon, ProfileIcon } from '@app-studio/web';

export const HOME_CONFIG = {
  interactionMode: "chatbot",
  cards: [
  {
    "title": i18n.t('homeRoutes.browseBlogPosts'),
    "path": "/posts",
    "icon": HomeIcon,
    "actionType": "navigation"
  },
  {
    "title": i18n.t('homeRoutes.generateBlogPost'),
    "path": "/posts/generate",
    "icon": EditIcon,
    "actionType": "navigation"
  },
  {
    "title": i18n.t('homeRoutes.purchaseCredits'),
    "path": "",
    "icon": PlusIcon,
    "actionType": "navigation"
  },
  {
    "title": i18n.t('homeRoutes.chatWithAi'),
    "path": "/chatbot",
    "icon": ProfileIcon,
    "actionType": "navigation"
  }
],
};

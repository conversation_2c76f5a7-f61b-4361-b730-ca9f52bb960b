
import i18n from 'src/utils/locale';
import { ProfileIcon, EditIcon, FileIcon } from '@app-studio/web';

export const HOME_CONFIG = {
  interactionMode: "chatbot",
  cards: [
  {
    "title": i18n.t('homeRoutes.chatWithAi'),
    "path": "/chatbot",
    "icon": ProfileIcon,
    "actionType": "navigation"
  },
  {
    "title": i18n.t('homeRoutes.generateBlogPost'),
    "path": "/posts/generate",
    "icon": EditIcon,
    "actionType": "navigation"
  },
  {
    "title": i18n.t('homeRoutes.browseBlogPosts'),
    "path": "/posts",
    "icon": FileIcon,
    "actionType": "navigation"
  }
],
};

import React from "react";

import BlogPostsPage from "src/pages/blogPosts/blogPosts";
import PostDetailPage from "src/pages/postDetail/postDetail";
import GenerateBlogPostPage from "src/pages/generateBlogPost/generateBlogPost";

import LoginPage from 'src/pages/auth/login.page';
import PlanPage from 'src/pages/plan/plan.page';
import WorkflowPage from 'src/pages/workflow/workflow.page';
import { CheckoutPage } from 'src/pages/checkout/checkout';
import RegisterPage from 'src/pages/auth/register.page';
import SettingsPage from 'src/pages/settings/settings.page';
import AccountPage from 'src/pages/account/account.page';
import { AuthTokenPage } from 'src/pages/auth/authToken.page';
import HomePage from "src/pages/home/<USER>";

export const RouterConfig = [
  { path: "/auth/login", element: <LoginPage /> },
  { path: "/plan", element: <PlanPage /> },
  { path: "/workflow/:id", element: <WorkflowPage /> },
  { path: "/checkout", element: <CheckoutPage /> },
  { path: '/auth/login', element: <LoginPage /> },
  { path: '/auth/register', element: <RegisterPage /> },
  { path: '/settings', element: <SettingsPage /> },
  { path: '/account', element: <AccountPage /> },
  { path: '/oauth', element: <AuthTokenPage /> },
  { path: '/home', element: <HomePage /> },
  { path: "/posts", element: <BlogPostsPage />, requiresAccess: false },
  { path: "/posts/:id", element: <PostDetailPage />, requiresAccess: true },
  { path: "/posts/generate", element: <GenerateBlogPostPage />, requiresAccess: false },

];

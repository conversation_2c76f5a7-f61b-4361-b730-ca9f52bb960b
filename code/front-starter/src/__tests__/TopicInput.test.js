import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import { TopicInputForm } from '../forms/TopicInputForm';

describe('TopicInputForm', () => {
  
 it('should show validation error when topic is empty', async () => {
   render(<TopicInputForm />);
   const input = await screen.findByTestId('topic_input_01');
   const submitButton = screen.getByRole('submit_button_01');

   await userEvent.clear(input);
   await userEvent.click(submitButton);

   const errorMessage = await screen.findByText('This field is required.');
   expect(errorMessage).toBeInTheDocument();
 });
        it('should show validation error for topic when minLength validation fails', async () => {
          render(<TopicInputForm />);
          const input = await screen.findByTestId('topic_input_01');
          const submitButton = screen.getByRole('submit_button_01');

          await userEvent.clear(input);
          await userEvent.type(input, "_");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Must be at least 5 characters.');
          expect(errorMessage).toBeInTheDocument();
        });
        it('should show validation error for topic when maxLength validation fails', async () => {
          render(<TopicInputForm />);
          const input = await screen.findByTestId('topic_input_01');
          const submitButton = screen.getByRole('submit_button_01');

          await userEvent.clear(input);
          await userEvent.type(input, "ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Cannot be more than 255 characters.');
          expect(errorMessage).toBeInTheDocument();
        });
});

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import { NewCommentFormForm } from '../forms/NewCommentFormForm';

describe('NewCommentFormForm', () => {
  
 it('should show validation error when content is empty', async () => {
   render(<NewCommentFormForm />);
   const input = await screen.findByTestId('comment-content-textarea');
   const submitButton = screen.getByRole('submit-comment-button');

   await userEvent.clear(input);
   await userEvent.click(submitButton);

   const errorMessage = await screen.findByText('This field is required.');
   expect(errorMessage).toBeInTheDocument();
 });
        it('should show validation error for content when minLength validation fails', async () => {
          render(<NewCommentFormForm />);
          const input = await screen.findByTestId('comment-content-textarea');
          const submitButton = screen.getByRole('submit-comment-button');

          await userEvent.clear(input);
          await userEvent.type(input, "_");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Comment must be at least 5 characters long.');
          expect(errorMessage).toBeInTheDocument();
        });
        it('should show validation error for content when maxLength validation fails', async () => {
          render(<NewCommentFormForm />);
          const input = await screen.findByTestId('comment-content-textarea');
          const submitButton = screen.getByRole('submit-comment-button');

          await userEvent.clear(input);
          await userEvent.type(input, "ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Comment cannot exceed 255 characters.');
          expect(errorMessage).toBeInTheDocument();
        });
});

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import { CommentFormForm } from '../forms/CommentFormForm';

describe('CommentFormForm', () => {
  
 it('should show validation error when content is empty', async () => {
   render(<CommentFormForm />);
   const input = await screen.findByTestId('comment-textarea');
   const submitButton = screen.getByRole('submit-comment-button');

   await userEvent.clear(input);
   await userEvent.click(submitButton);

   const errorMessage = await screen.findByText('This field is required.');
   expect(errorMessage).toBeInTheDocument();
 });
        it('should show validation error for content when minLength validation fails', async () => {
          render(<CommentFormForm />);
          const input = await screen.findByTestId('comment-textarea');
          const submitButton = screen.getByRole('submit-comment-button');

          await userEvent.clear(input);
          await userEvent.type(input, "_");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Must be at least 10 characters.');
          expect(errorMessage).toBeInTheDocument();
        });
        it('should show validation error for content when maxLength validation fails', async () => {
          render(<CommentFormForm />);
          const input = await screen.findByTestId('comment-textarea');
          const submitButton = screen.getByRole('submit-comment-button');

          await userEvent.clear(input);
          await userEvent.type(input, "ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Cannot be more than 255 characters.');
          expect(errorMessage).toBeInTheDocument();
        });
});

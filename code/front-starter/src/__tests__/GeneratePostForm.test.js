import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import { GeneratePostFormForm } from '../forms/GeneratePostFormForm';

describe('GeneratePostFormForm', () => {
  
 it('should show validation error when topic is empty', async () => {
   render(<GeneratePostFormForm />);
   const input = await screen.findByTestId('post-topic-input');
   const submitButton = screen.getByRole('generate-post-button');

   await userEvent.clear(input);
   await userEvent.click(submitButton);

   const errorMessage = await screen.findByText('Blog post topic is required.');
   expect(errorMessage).toBeInTheDocument();
 });
        it('should show validation error for topic when minLength validation fails', async () => {
          render(<GeneratePostFormForm />);
          const input = await screen.findByTestId('post-topic-input');
          const submitButton = screen.getByRole('generate-post-button');

          await userEvent.clear(input);
          await userEvent.type(input, "_");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Topic must be at least 10 characters long.');
          expect(errorMessage).toBeInTheDocument();
        });
        it('should show validation error for topic when maxLength validation fails', async () => {
          render(<GeneratePostFormForm />);
          const input = await screen.findByTestId('post-topic-input');
          const submitButton = screen.getByRole('generate-post-button');

          await userEvent.clear(input);
          await userEvent.type(input, "ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss");
          await userEvent.click(submitButton);

          const errorMessage = await screen.findByText('Topic cannot exceed 255 characters.');
          expect(errorMessage).toBeInTheDocument();
        });
});

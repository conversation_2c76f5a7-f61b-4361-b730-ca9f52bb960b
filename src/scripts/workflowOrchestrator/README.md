# Workflow Orchestrator

Le Workflow Orchestrator est un script qui automatise l'exécution séquentielle des scripts de génération de spécifications basé sur un workflow généré par l'IA.

## Fonctionnalités

- **Exécution complète en un seul script** : Lance automatiquement le projet entier depuis zéro jusqu'aux applications en cours d'exécution
- **Génération automatique des spécifications** : Lance le script `project` automatiquement si les spécifications n'existent pas
- **Génération automatique de workflow** : Utilise l'IA pour analyser la description du projet et générer un workflow optimal
- **Génération de code complète** : Génère automatiquement le code serveur et client
- **Lancement automatique des applications** : Démarre l'API, le frontend et les services auxiliaires
- **Exécution séquentielle** : Lance les scripts dans l'ordre défini par le workflow
- **Gestion d'état** : Suit le statut de chaque étape (pending, running, completed)
- **Reprise automatique** : Peut reprendre l'exécution à partir d'une étape échouée
- **Sauvegarde continue** : Met à jour le fichier workflow.json après chaque étape

## Usage

### Via npm script (recommandé)
```bash
npm run specs:workflow <provider> <model> <appType>
```

### Directement avec Node.js
```bash
node src/scripts/workflowOrchestrator/workflowOrchestrator.js <provider> <model> <appType>
```

### Paramètres

- `provider` : Fournisseur d'IA (ex: "openai", "anthropic")
- `model` : Modèle d'IA à utiliser (ex: "gpt-4", "claude-3")
- `appType` : Type d'application (ex: "agent", "blog", "image")

### Exemple
```bash
npm run specs:workflow openai gpt-4 agent
```

## Scripts supportés

Le workflow orchestrator peut exécuter les scripts suivants :

### Scripts obligatoires (toujours inclus)
- **project** : Génère les spécifications du projet
- **feature** : Génère les spécifications des fonctionnalités
- **screen** : Génère les spécifications des écrans
- **step** : Génère les étapes du workflow

### Scripts optionnels (inclus selon les besoins du projet)
- **navigation** : Génère la configuration de navigation
- **form** : Génère les spécifications des formulaires
- **home** : Génère les routes de la page d'accueil
- **notification** : Génère les templates de notifications

### Scripts de génération de code (toujours inclus)
- **generateServer** : Génère le code serveur basé sur les spécifications
- **generateScreens** : Génère les écrans et composants client

### Lancement automatique des applications
Après la génération de code, le script lance automatiquement :
- **API Server** : Base de données, seed, serveur de développement
- **Frontend** : Configuration API locale, serveur de développement
- **Services auxiliaires** : Stripe login, webhook listener

## Fichiers générés

- `specs/workflow.json` : Configuration du workflow avec statuts des étapes
- `specs/specifications.json` : Spécifications du projet (si généré)
- Autres fichiers selon les scripts exécutés

## Structure du workflow.json

```json
{
  "workflow": [
    {
      "step": 1,
      "script": "project",
      "status": "completed",
      "description": "Generate project configuration"
    },
    {
      "step": 2,
      "script": "feature",
      "status": "pending",
      "description": "Generate feature configuration"
    }
  ]
}
```

## Statuts des étapes

- `pending` : Étape en attente d'exécution
- `running` : Étape en cours d'exécution
- `completed` : Étape terminée avec succès

## Gestion d'erreurs

- Si une étape échoue, le workflow s'arrête et l'étape est remise en statut `pending`
- Vous pouvez relancer le script pour reprendre à partir de l'étape échouée
- Les étapes déjà complétées sont automatiquement ignorées

## Prérequis

1. **Aucun fichier requis** : Le script génère automatiquement tous les fichiers nécessaires
2. Tous les scripts référencés doivent être présents dans leurs dossiers respectifs
3. Les variables d'environnement pour l'IA doivent être configurées

## Flux d'exécution

1. **Vérification des spécifications** : Vérifie si `specs/specifications.json` existe
2. **Génération automatique** : Si absent, lance automatiquement le script `project`
3. **Génération du workflow** : Utilise les spécifications pour générer le workflow via l'IA
4. **Phase spécifications** : Lance tous les scripts de spécifications (project, feature, screen, step, optionnels)
5. **Phase génération de code** : Lance generateServer puis generateScreens
6. **Phase lancement** : Configure et démarre toutes les applications
7. **Suivi en temps réel** : Met à jour le statut de chaque étape

## Applications lancées automatiquement

À la fin du workflow, les services suivants sont démarrés :

### API Server (code/api-starter)
- `npm run prisma:sync` - Synchronise le schéma Prisma
- `npm run db:reset` - Remet à zéro la base de données
- `npm run seed` - Peuple la base de données avec des données de test
- `npm run dev` - Démarre le serveur API en mode développement

### Frontend (code/front-starter)
- `npm run api:local` - Configure l'API locale
- `npm run local` - Démarre le serveur frontend

### Services auxiliaires
- `npm run login` - Connexion Stripe
- `npm run listen` - Écoute des webhooks Stripe

### URLs d'accès
- **API** : http://localhost:3001
- **Frontend** : http://localhost:3000
- **Webhooks** : Configurés automatiquement pour Stripe

## Dépannage

### Erreur "Script not found"
Vérifiez que tous les scripts existent dans les chemins définis dans `SCRIPT_PATHS`

### Erreur "Workflow generation failed"
Assurez-vous que le fichier `specs/specifications.json` existe et contient une description du projet

### Étape bloquée en "running"
Supprimez le fichier `specs/workflow.json` et relancez le script pour régénérer le workflow

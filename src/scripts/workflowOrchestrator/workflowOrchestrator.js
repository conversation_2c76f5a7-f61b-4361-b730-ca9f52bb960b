import "dotenv/config";
import { WORKFLOW } from "../../../prompts/specs/workflow.js";
import { FileHandler } from "../utils/FileHandler.js";
import { pathManager } from "../utils/pathManager.js";
import { DEFAULT_AI_MODEL, DEFAULT_AI_PROVIDER } from "../utils/constant.js";
import { ApplicationError } from "../utils/CustomError.js";
import { AiService } from "../../ai/ai.service.js";
import { extractJson } from "../utils/jsonUtils.js";
import { spawn } from "child_process";

// Initialize services
const fileHandler = new FileHandler();
const aiService = new AiService();

/**
 * Enhanced sendPrompt function that accepts an optional file path
 * @param {string} provider - The AI provider to use
 * @param {string} model - The AI model to use
 * @param {string} prompt - The prompt to send to the AI
 * @param {object} options - Options for the AI service
 * @param {string} [filePath] - Optional file path to save the response
 * @returns {Promise<object>} - The extracted JSON from the AI response
 */
async function sendPrompt(provider, model, prompt, options, filePath = null) {
  // Construct the messages array for the AI service
  const messages = [{ role: "user", content: prompt }];

  // Send the prompt to the AI service
  const aiResponse = await aiService.send({
    model,
    provider,
    messages,
    options,
  });

  // Extract JSON from the response
  const extractedJson = extractJson(aiResponse);

  if (filePath) {
    await fileHandler.writeJson(filePath, extractedJson);
  }

  // Return the JSON response
  return extractedJson;
}

/**
 * Script path mapping based on script names
 */
const SCRIPT_PATHS = pathManager.scripts;

/**
 * Execute a script with given parameters
 * @param {string} scriptPath - Path to the script to execute
 * @param {string} provider - AI provider
 * @param {string} model - AI model
 * @param {string} [appType] - Optional app type for project script
 * @returns {Promise<boolean>} - Success status
 */
function executeScript(scriptPath, provider, model, appType = null) {
  return new Promise((resolve, reject) => {
    const args = [scriptPath, provider, model];
    if (appType) {
      args.push(appType);
    }

    console.log(`🚀 Executing: node ${args.join(" ")}`);

    const child = spawn("node", args, {
      stdio: "inherit",
      cwd: process.cwd(),
    });

    child.on("close", (code) => {
      if (code === 0) {
        console.log(`✅ Script completed successfully: ${scriptPath}`);
        resolve(true);
      } else {
        console.error(`❌ Script failed with code ${code}: ${scriptPath}`);
        reject(new Error(`Script execution failed: ${scriptPath}`));
      }
    });

    child.on("error", (error) => {
      console.error(`❌ Error executing script ${scriptPath}:`, error);
      reject(error);
    });
  });
}

/**
 * Update workflow step status
 * @param {Array} workflow - The workflow array
 * @param {number} stepNumber - Step number to update
 * @param {string} status - New status ('pending', 'running', 'completed')
 * @returns {Array} - Updated workflow
 */
function updateWorkflowStatus(workflow, stepNumber, status) {
  const updatedWorkflow = [...workflow];
  const stepIndex = updatedWorkflow.findIndex(
    (step) => step.step === stepNumber
  );

  if (stepIndex !== -1) {
    updatedWorkflow[stepIndex].status = status;
  }

  return updatedWorkflow;
}

/**
 * Save updated workflow to file
 * @param {object} workflowData - Complete workflow data object
 */
async function saveWorkflow(workflowData) {
  await fileHandler.writeJson(pathManager.output.workflow, workflowData);
  console.log(
    `📝 Workflow updated and saved to ${pathManager.output.workflow}`
  );
}

/**
 * Execute a command in a specific directory
 * @param {string} command - Command to execute
 * @param {string} cwd - Working directory
 * @param {boolean} [background=false] - Whether to run in background
 * @returns {Promise<boolean>} - Success status
 */
function executeCommand(command, cwd, background = false) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 Executing: ${command} in ${cwd}`);

    const child = spawn("npm", ["run", ...command.split(" ")], {
      stdio: background ? "pipe" : "inherit",
      cwd: cwd,
      shell: true,
    });

    if (background) {
      console.log(`🚀 Started background process: ${command}`);
      resolve(true);
      return;
    }

    child.on("close", (code) => {
      if (code === 0) {
        console.log(`✅ Command completed: ${command}`);
        resolve(true);
      } else {
        console.error(`❌ Command failed with code ${code}: ${command}`);
        reject(new Error(`Command execution failed: ${command}`));
      }
    });

    child.on("error", (error) => {
      console.error(`❌ Error executing command ${command}:`, error);
      reject(error);
    });
  });
}

/**
 * Launch the applications (API and Frontend)
 */
async function launchApplications() {
  try {
    console.log("🚀 Starting application launch sequence...");

    // API Starter commands
    const apiPath = "code/api-starter";
    console.log("📡 Setting up API server...");
    await executeCommand("db:start", apiPath);
    await executeCommand("prisma:sync", apiPath);
    await executeCommand("db:reset", apiPath);
    await executeCommand("seed", apiPath);

    // Start API in background
    console.log("🔥 Starting API server in background...");
    executeCommand("dev", apiPath, true);

    // Wait a bit for API to start
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Frontend Starter commands
    const frontPath = "code/front-starter";
    console.log("🌐 Setting up Frontend...");

    await executeCommand("api:local", frontPath);

    // Start Frontend in background
    console.log("🔥 Starting Frontend in background...");
    executeCommand("local", frontPath, true);

    // Additional services
    console.log("🔧 Starting additional services...");
    executeCommand("login", ".", true);
    executeCommand("listen", ".", true);

    console.log("🎉 All applications launched successfully!");
    console.log("📋 Services running:");
    console.log("   - API Server: http://localhost:3001");
    console.log("   - Frontend: http://localhost:3000");
    console.log("   - Additional services: login, listen");
  } catch (error) {
    console.error("💥 Failed to launch applications:", error);
    throw error;
  }
}

/**
 * Execute the workflow orchestrator
 * @param {string} provider - AI provider
 * @param {string} model - AI model
 * @param {string} appType - Application type
 */
async function executeWorkflow(provider, model, appType) {
  try {
    // Step 0: Ensure project specifications exist
    let projectSpecs;
    let projectExecutedNow = false;
    try {
      projectSpecs = await fileHandler.readJson(
        pathManager.output.specificationsApp
      );
      console.log("📋 Project specifications found, continuing...");
    } catch (error) {
      console.log(
        "� Project specifications not found, generating them first..."
      );

      // Execute project script first
      const projectScriptPath = SCRIPT_PATHS.project;
      await executeScript(projectScriptPath, provider, model, appType);

      // Now read the generated specifications
      projectSpecs = await fileHandler.readJson(
        pathManager.output.specificationsApp
      );
      projectExecutedNow = true;
      console.log("✅ Project specifications generated successfully");
    }

    // Step 1: Generate workflow if it doesn't exist
    let workflowData;

    try {
      workflowData = await fileHandler.readJson(pathManager.output.workflow);
      console.log("📋 Existing workflow found, continuing execution...");
    } catch (error) {
      console.log("🔄 Generating new workflow...");

      // Use project specifications to get project description
      const projectDescription =
        projectSpecs?.purpose || "No project description available";

      // Generate workflow using AI
      const prompt = WORKFLOW(projectDescription);
      workflowData = await sendPrompt(
        provider,
        model,
        prompt,
        {
          ownerId: "workflow",
          ownerType: "orchestrator",
        },
        pathManager.output.workflow
      );

      console.log("✅ Workflow generated successfully");
    }

    // If project was executed in step 0, mark it as completed in workflow
    if (projectExecutedNow) {
      workflowData.workflow = updateWorkflowStatus(
        workflowData.workflow,
        1,
        "completed"
      );
      await saveWorkflow(workflowData);
      console.log("✅ Project step marked as completed in workflow");
    }

    // Step 2: Execute workflow steps
    const workflow = workflowData.workflow;

    for (const step of workflow) {
      // Skip completed steps
      if (step.status === "completed") {
        console.log(`⏭️  Skipping completed step ${step.step}: ${step.script}`);
        continue;
      }

      // Check if script path exists
      const scriptPath = SCRIPT_PATHS[step.script];
      if (!scriptPath) {
        console.warn(`⚠️  Unknown script: ${step.script}, skipping...`);
        continue;
      }

      try {
        // Update status to running
        workflowData.workflow = updateWorkflowStatus(
          workflow,
          step.step,
          "running"
        );
        await saveWorkflow(workflowData);

        console.log(`🔄 Executing step ${step.step}: ${step.description}`);

        // Execute the script
        await executeScript(
          scriptPath,
          provider,
          model,
          step.script === "project" ? appType : null
        );

        // Update status to completed
        workflowData.workflow = updateWorkflowStatus(
          workflowData.workflow,
          step.step,
          "completed"
        );
        await saveWorkflow(workflowData);

        console.log(`✅ Step ${step.step} completed successfully`);
      } catch (error) {
        console.error(`❌ Step ${step.step} failed:`, error.message);

        // Update status back to pending for retry
        workflowData.workflow = updateWorkflowStatus(
          workflowData.workflow,
          step.step,
          "pending"
        );
        await saveWorkflow(workflowData);

        throw new ApplicationError(
          `Workflow execution failed at step ${step.step}: ${step.script}`,
          {
            step: step.step,
            script: step.script,
            originalError: error,
          }
        );
      }
    }

    console.log("🎉 Workflow execution completed successfully!");

    // Launch applications after successful workflow completion
    console.log("🚀 Starting application launch phase...");
    await launchApplications();
  } catch (error) {
    console.error("💥 Workflow execution failed:", error);
    throw error;
  }
}

// Main execution
(async () => {
  // Extract provider, model, and appType from command line arguments
  const provider = process.argv[2] ?? DEFAULT_AI_PROVIDER;
  const model = process.argv[3] ?? DEFAULT_AI_MODEL;
  const appType = process.argv[4] ?? "blog";

  // Validate required parameters
  if (!provider) {
    throw new ApplicationError(
      "Provider missing. Usage: node workflowOrchestrator.js <provider> <model> <appType>",
      { argument: "provider" }
    );
  }

  if (!model) {
    throw new ApplicationError(
      "Model missing. Usage: node workflowOrchestrator.js <provider> <model> <appType>",
      { argument: "model" }
    );
  }

  console.log("🚀 Starting Workflow Orchestrator...");
  console.log(
    `📋 Provider: ${provider}, Model: ${model}, App Type: ${appType}`
  );

  await executeWorkflow(provider, model, appType);
})();

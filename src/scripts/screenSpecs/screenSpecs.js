import path from "path";
import { FileHand<PERSON> } from "../utils/FileHandler.js";
import { pathManager } from "../utils/pathManager.js";
import { AiService } from "../../ai/ai.service.js";
import { formatPath, extractTextContent } from "../utils/stringUtils.js";
import { extractJson } from "../utils/jsonUtils.js";
import { applyDiff, extractModels, extractRequests } from "../utils/helpers.js";
import {
  unifyScreenSpecsPrompt,
  generateMergeSchemasPrompt,
  generateSchemaPatches,
  promptToUpdateModels,
} from "../../../prompts/index.js";
import { DEFAULT_AI_MODEL, DEFAULT_AI_PROVIDER } from "../utils/constant.js";
import { ApplicationError } from "../utils/CustomError.js";

// Initialize the AI service and file handler
const aiService = new AiService();
const fileHandler = new FileHandler();

/**
 * Sends a prompt to the AI service and processes the response.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 * @param {string} prompt - The prompt to send to the AI.
 * @param {string} id - An identifier for the current operation (e.g., screen ID).
 * @returns {Object} - The processed AI response as a JSON object.
 */
async function sendPrompt(provider, model, prompt, id = "home") {
  // Construct the messages array for the AI service
  const messages = [{ role: "user", content: prompt }];

  // Send the prompt to the AI service
  const response = await aiService.send({
    model,
    provider,
    messages,
    options: {
      ownerId: "screen",
      ownerType: id,
    },
  });
  // Extract the JSON from the response
  return extractJson(response);
}

/**
 * Sends a prompt to the AI service and processes the response.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 * @param {string} prompt - The prompt to send to the AI.
 * @param {string} id - An identifier for the current operation (e.g., screen ID).
 * @returns {Object} - The processed AI response as a Text object.
 */
async function sendPromptText(provider, model, prompt, id = "home") {
  // Construct the messages array for the AI service
  const messages = [{ role: "user", content: prompt }];

  // Send the prompt to the AI service
  const response = await aiService.send({
    model,
    provider,
    messages,
    options: {
      ownerId: "screen",
      ownerType: id,
    },
  });
  // Extract the text content from the response
  return extractTextContent(response);
}

/**
 * Moves screen specification files based on their type.
 */
async function moveFilesBasedOnType() {
  try {
    // Read the files from the pages output directory
    const files = await fileHandler.readFiles(pathManager.output.pages);

    // Iterate over each file
    for (const file of files) {
      // Construct the file path
      const filePath = path.join(pathManager.output.pages, file);
      // Read the JSON data from the file
      const data = await fileHandler.readJson(filePath);
      // Get the screen type from the data
      const screenType = data?.screenSpecs?.type?.toLowerCase();

      // If the screen type is "modal", move the file to the modals output directory
      if (screenType === "modal") {
        const destinationPath = path.join(pathManager.output.modals, file);
        await fileHandler.moveFile(filePath, destinationPath);
        console.log(`Moved "${file}" to pathManager.output.modals.`);
      } else if (screenType !== "page") {
        // If the screen type is not "page" or "modal", log a warning
        console.warn(
          `File "${file}" has an unrecognized type "${data?.screenSpecs?.type}". Expected "page" or "modal".`
        );
      }
      // If type is "page", no action is needed
    }
  } catch (error) {
    throw new ApplicationError("Error moving files based on type", {
      originalError: error,
    });
  }
}

/**
 * Retrieves feature specifications based on the "useIn" property of a screen.
 * @param {Array<string>} useIn - List of features associated with the screen.
 * @returns {Array<Object>} - Array of feature specifications with their user experiences.
 */
async function getFeatureSpecs(screenKey, useIn) {
  // Initialize the feature specifications array
  const featureSpecs = [];

  // Iterate over each feature name
  for (const featureName of useIn) {
    // Construct the feature path
    const featurePath = path.join(
      pathManager.output.features,
      `${featureName}.json`
    );
    // Read the JSON data from the feature path
    const feature = await fileHandler.readJson(featurePath);
    // Get the user experience from the feature
    const userExperience = feature?.feature?.userExperience || [];

    // Get the screen description from the feature
    const screenDescription = feature.feature.screens[screenKey];

    // If the user experience is missing, throw an error
    if (userExperience.length === 0) {
      throw new ApplicationError(
        `Missing userExperience data for feature: ${featureName}`
      );
    }

    // Push the feature specifications to the array
    featureSpecs.push({
      featureName,
      userExperience,
      screenDescription,
      requests: feature?.feature?.requests || [],
      dataSchemas: feature?.feature?.dataSchemas || {},
    });
  }

  // Return the feature specifications
  return featureSpecs;
}

/**
 * Updates the routing application with details for a specific screen.
 * @param {string} screenKey - The unique key (path) of the screen.
 * @param {Object} routingApp - The current routing application data.
 * @param {string} screenFilePath - Path to the screen's specification file.
 */
async function updateRoutingApp(screenKey, routingApp, screenFilePath) {
  try {
    // Read the screen specification from the file path
    const screenSpec = await fileHandler.readJson(screenFilePath);
    // Extract the name and type from the screen specification
    const { name, type } = screenSpec.screenSpecs;

    // Update the routing application with the name and type
    routingApp[screenKey] = {
      ...routingApp[screenKey],
      name,
      type,
    };

    // Write the updated routing application to the file
    await fileHandler.writeJson(pathManager.output.routingApp, routingApp);
    console.log(
      `Updated routing app for screen "${screenKey}" with name and type.`
    );
  } catch (error) {
    throw new ApplicationError("Error updating routing app", {
      screenKey,
      filePath: screenFilePath,
      originalError: error,
    });
  }
}
const updateObjectType = (schema, models) => {
  let updatedSchema = schema;

  const objectTypes = new Set([
    "news",
    "comment",
    "profile",
    "audio",
    ...Object.keys(models),
  ]);
  const splitSchema = schema.split("enum ObjectType");

  const relevantPart = splitSchema[1];
  const startIndex = relevantPart.indexOf("{");
  const endIndex = relevantPart.indexOf("}");

  if (startIndex !== -1 && endIndex !== -1) {
    // Join the objectTypes into a string for replacement
    const updatedContent = Array.from(objectTypes).join("\n ");
    updatedSchema =
      splitSchema[0] +
      `enum ObjectType { \n ${updatedContent} \n}` +
      relevantPart.substring(endIndex + 1);
  } else {
    console.error("Curly braces not found in 'enum ObjectType'.");
  }

  return updatedSchema;
};
/**
 * @param {Object} models
 */
async function updateSchemaPrisma(provider, model) {
  try {
    const modelsPath = path.join(pathManager.output.root, "models.json");
    const models = await fileHandler.readJson(modelsPath);
    const currentSchema = await fileHandler.readFile(
      pathManager.back.prismaSchema
    );

    //update schema objectType - pass to currentSchema
    const updatedSchema = updateObjectType(currentSchema, models);

    //To remove the `canBeCommented` property
    for (const modelKey in models) {
      if (models[modelKey].hasOwnProperty("canBeCommented")) {
        delete models[modelKey].canBeCommented;
      }
    }

    const prompt = await generateSchemaPatches(
      updatedSchema,
      JSON.stringify(models)
    );

    let diff = await sendPromptText(provider, model, prompt, "prisma");
    let updatedPrisma = applyDiff(updatedSchema, diff);

    //update the plan for credit payment
    const projectConfig = await fileHandler.readJson(
      pathManager.output.specificationsApp
    );

    const payment = projectConfig.features.find(
      (feature) => feature.paymentType
    );

    if (
      payment?.paymentType &&
      payment.paymentType === "credit" &&
      payment.options &&
      Object.keys(payment.options).length > 0
    ) {
      const keys = Object.keys(payment.options);

      updatedPrisma = updatedPrisma.replace(
        /autoChargePlan\s+String\s+@default\(".*?"\)/,
        `autoChargePlan      String  @default("${keys[0]}")`
      );

      // Replace balance default value
      updatedPrisma = updatedPrisma.replace(
        /balance\s+Int\s+@default\(\d+\)/,
        `balance      Int           @default(${
          payment.options[keys[0]]?.credits
        })`
      );
    }

    await fileHandler.writeText(pathManager.back.prismaSchema, updatedPrisma);
  } catch (error) {
    throw new ApplicationError("Error updating Prisma schema", {
      originalError: error,
    });
  }
}

async function updateSchemaModels(provider, model) {
  try {
    const currentModels = await fileHandler.readJson(pathManager.output.models);

    let schemaPrisma = await fileHandler.readFile(
      pathManager.back.prismaSchema
    );

    const modelKeys = Object.keys(currentModels);
    const deniedKeys = [
      "image",
      "video",
      "comment",
      "account",
      "chatMessage",
      "chatConversation",
    ];

    const filteredKeys = modelKeys.filter((key) => !deniedKeys.includes(key));

    schemaPrisma = JSON.stringify(extractModels(schemaPrisma, filteredKeys));

    const prompt = await promptToUpdateModels(schemaPrisma, currentModels);

    let updatedSchemaModels = await sendPrompt(
      provider,
      model,
      prompt,
      "schemaModels"
    );

    if (
      updatedSchemaModels &&
      typeof updatedSchemaModels === "object" &&
      Object.keys(updatedSchemaModels).length > 0
    ) {
      await fileHandler.writeJson(
        pathManager.output.models,
        updatedSchemaModels
      );
    } else {
      console.warn(
        "⚠️  No valid schema models to update, using existing models"
      );
      // Try to read existing models or create empty object
      try {
        const existingModels = await fileHandler.readJson(
          pathManager.output.models
        );
        if (!existingModels) {
          await fileHandler.writeJson(pathManager.output.models, {});
        }
      } catch (error) {
        console.warn("⚠️  Creating empty models file");
        await fileHandler.writeJson(pathManager.output.models, {});
      }
    }
  } catch (error) {
    throw new ApplicationError("Error updating schema models", {
      originalError: error,
    });
  }
}

/**
 * Updates the dataSchemas in screen specs based on the provided models.
 * @param {Object} routingApp - The routing application object containing screen configurations.
 */
async function updateScreenSpecs() {
  try {
    // Read the files from the screens output directory
    const files = await fileHandler.readFiles(pathManager.output.screens);
    // Read the models from the models output directory
    const models = await fileHandler.readJson(pathManager.output.models);

    // Normalize model keys to lowercase for case-insensitive lookup
    const normalizedModels = Object.keys(models).reduce((acc, key) => {
      acc[key.toLowerCase()] = models[key];
      return acc;
    }, {});

    // Iterate over each file
    for (const file of files) {
      // Construct the file path
      const filePath = path.join(pathManager.output.screens, file);
      // Read the screen specification from the file
      const screenSpec = await fileHandler.readJson(filePath);

      // If the screen specification does not have data schemas, log a warning and continue
      if (!screenSpec?.screenSpecs?.dataSchemas) {
        console.warn(
          `dataSchemas not found in "${filePath}". Skipping update for this screen.`
        );
        continue;
      }

      // Initialize a flag to track if the screen specification was updated
      let updated = false;
      // Iterate over each schema key in the screen specification's data schemas
      for (const schemaKey of Object.keys(screenSpec.screenSpecs.dataSchemas)) {
        // Normalize the schema key to lowercase for case-insensitive lookup
        const normalizedKey = schemaKey.toLowerCase();

        // If the normalized key exists in the normalized models, update the screen specification's data schema
        if (normalizedModels.hasOwnProperty(normalizedKey)) {
          screenSpec.screenSpecs.dataSchemas[schemaKey] =
            normalizedModels[normalizedKey];
          updated = true;
          console.log(`Updated dataSchema "${schemaKey}" in "${filePath}".`);
        } else {
          // If the normalized key does not exist in the normalized models, log a warning
          console.warn(
            `dataSchema "${schemaKey}" not found in models. Skipping update for this schema.`
          );
        }
      }

      // If the screen specification was updated, write the updated screen specification to the file
      if (updated) {
        await fileHandler.writeJson(filePath, screenSpec);
        console.log(`Successfully updated "${filePath}".`);
      }
    }
  } catch (error) {
    throw new ApplicationError("Error updating screen specs", {
      originalError: error,
    });
  }
}

// Function to update fields with isUser
function updateFields(entity) {
  const newFields = {};

  for (const field in entity.fields) {
    const fieldValue = entity.fields[field];

    if (fieldValue.isUser) {
      newFields["userId"] = {
        type: "String",
        required: true,
        isUser: true,
        description: "Identifier of the user.",
      };
    } else {
      newFields[field] = fieldValue;
    }
  }

  entity.fields = newFields;
  return entity;
}

/**
 * Generates and merges Prisma models using AI prompts.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 * @param {Object} routingApp - The routing application object containing screen configurations.
 */
async function unifyPrismaModelPrompt(provider, model) {
  try {
    let dataSchemasArray = [];
    const files = await fileHandler.readFiles(pathManager.output.screens);
    for (const file of files) {
      const filePath = path.join(pathManager.output.screens, file);
      const screenSpec = await fileHandler.readJson(filePath);
      const dataSchemas = screenSpec?.screenSpecs?.dataSchemas;
      // If the data schemas exist, push them to the data schemas array
      if (dataSchemas) {
        dataSchemasArray.push(dataSchemas);
      }
    }

    // Generate the merge schemas prompt
    const prompt = generateMergeSchemasPrompt(dataSchemasArray);
    // Send the prompt to the AI service and get the merged models
    let mergedModels = await sendPrompt(provider, model, prompt, "models");

    // Apply the update to each entity in the JSON
    for (const entityKey in mergedModels) {
      mergedModels[entityKey] = updateFields(mergedModels[entityKey]);
    }
    // Remove the "comment" property
    delete mergedModels.comment;

    // Construct the models path
    const modelsPath = path.join(pathManager.output.root, "models.json");
    // Write the merged models to the models path
    await fileHandler.writeJson(modelsPath, mergedModels);

    // update the prisma schema
    await updateSchemaPrisma(provider, model);
    // update the models schema
    await updateSchemaModels(provider, model);
    // update the data schema of each screen spec
    await updateScreenSpecs();
  } catch (error) {
    throw new ApplicationError("Error in unifyPrismaModelPrompt", {
      originalError: error,
    });
  }
}

/**
 * Unifies the features within a screen using AI and updates the screen file.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 * @param {string} screenKey - The key/path of the screen.
 * @param {string} screenFilePath - Path to the screen's specification file.
 */
async function unifyFeatures(provider, model, screenKey, screenFilePath) {
  try {
    // Read the project specifications from the file
    const projectSpecs = await fileHandler.readJson(
      pathManager.output.specificationsApp
    );

    // Find the payment feature in the project specifications
    let payment = projectSpecs.features.find(
      (feature) => feature.paymentEntity
    ) ?? { paymentEntity: "", paymentType: "", isCreditUsage: false };

    payment.isCreditUsage = payment?.creditType === "usage";

    // Read the screen data from the file
    const screenData = await fileHandler.readJson(screenFilePath);

    // If the screen data is invalid, throw an error
    if (!screenData?.screenSpecs) {
      throw new ApplicationError(`Invalid screen data for "${screenKey}".`, {
        screenKey: screenKey,
      });
    }

    // Generate the unify screen specifications prompt
    const prompt = unifyScreenSpecsPrompt(screenData.screenSpecs, payment);

    // Send the prompt to the AI service and get the unified response
    const unifiedResponse = await sendPrompt(
      provider,
      model,
      prompt,
      screenKey
    );

    // Construct the output file name
    const outputFileName = `${formatPath(screenKey, "/", "_")}.json`;
    // Construct the output path
    const outputPath = path.join(pathManager.output.pages, outputFileName);

    // Write the unified response to the file
    await fileHandler.writeJson(outputPath, unifiedResponse);
    console.log(
      `Unified features for screen "${screenKey}" and updated the file.`
    );
  } catch (error) {
    throw new ApplicationError("Failed to unify features", {
      screenKey: screenKey,
      originalError: error,
    });
  }
}
/**
 * Unifies feature specifications across all screens using AI.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 * @param {Object} routingApp - The routing application object containing screen configurations.
 */
async function unifyFeatureSpecs(provider, model, routingApp) {
  // Iterate over each screen in the routing application
  for (const [screenKey] of Object.entries(routingApp)) {
    // Construct the screen file path
    const screenFilePath = path.join(
      pathManager.output.screens,
      `${formatPath(screenKey, "/", "_")}.json`
    );

    // Unify the features for the screen
    await unifyFeatures(provider, model, screenKey, screenFilePath);
    // Update the routing application for the screen
    await updateRoutingApp(screenKey, routingApp, screenFilePath);
  }
}

/**
 * Processes a screen to extract and map its user experiences by feature.
 * @param {Object} screen - The screen data from the routing application.
 * @param {string} screenKey - The unique path identifier of the screen.
 * @param {string} screenFilePath - Path to the screen's specification file.
 */
async function processScreen(screen, screenKey, screenFilePath) {
  try {
    // Get the feature specifications for the screen
    const features = await getFeatureSpecs(screenKey, screen.useIn);
    // Initialize the user experience by feature object
    const userExperienceByFeature = {};
    // Initialize the request by screen object
    const requestByScreen = {};
    // Initialize the description
    let description = "";
    // Initialize the data schemas
    let dataSchemas = {};

    // Iterate over each feature
    for (const feature of features) {
      // Initialize the where requests object
      let whereRequests = {};

      // Add the screen description to the description
      description += `${feature.screenDescription} `;

      // Filter user experiences related to the current screen
      const featureUserExperience = feature.userExperience
        .filter((userExp) => userExp.where === screenKey)
        .map(({ where, ...rest }) => {
          const requestId = rest.action?.request?.requestId ?? "";
          if (requestId) {
            whereRequests[requestId] = true;
          }
          return rest;
        });

      // If the feature user experience exists, add it to the user experience by feature object
      if (featureUserExperience.length > 0) {
        userExperienceByFeature[feature.featureName] = featureUserExperience;
      }

      // Map the requests
      feature.requests.map((request) => {
        if (whereRequests[request.requestId] !== undefined) {
          const requestSchema = request.dataSchema;
          dataSchemas[requestSchema] = feature.dataSchemas[requestSchema];
          requestByScreen[request.requestId] = request;
        }
      });
    }

    // Construct the screen with user experience object
    const screenWithUserExperience = {
      screenSpecs: {
        path: screenKey,
        description: description.slice(0, -2), // Remove trailing " +"
        dataSchemas,
        userExperience: userExperienceByFeature,
        requests: requestByScreen,
      },
    };

    // Write the screen with user experience to the file
    await fileHandler.writeJson(screenFilePath, screenWithUserExperience);
    console.log(`Processed screen "${screenKey}" with user experiences.`);
  } catch (error) {
    console.error(`Error processing screen "${screenKey}":`, error);
    throw error;
  }
}

/**
 * Unifies feature specifications across all screens using AI.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 * @param {Object} routingApp - The routing application object containing screen configurations.
 */
async function processScreens(provider, model, routingApp) {
  try {
    // Process each screen to map user experiences
    const screenProcessingPromises = Object.entries(routingApp).map(
      async ([screenKey, screen]) => {
        try {
          const screenFilePath = path.join(
            pathManager.output.screens,
            `${formatPath(screenKey, "/", "_")}.json`
          );
          if (screenKey !== "/paywall") {
            await processScreen(screen, screenKey, screenFilePath);
          }
        } catch (error) {
          throw new ApplicationError("Error processing screen", {
            screenKey: screenKey,
            originalError: error,
          });
        }
      }
    );

    // Wait for all screen processing promises to resolve
    await Promise.all(screenProcessingPromises);
    console.log("Completed processing all screens.");

    // Unify Prisma models
    await unifyPrismaModelPrompt(provider, model);

    // Unify feature specifications
    await unifyFeatureSpecs(provider, model, routingApp);

    // Move files based on their type
    await moveFilesBasedOnType();

    console.log("All processing steps completed successfully.");
  } catch (error) {
    throw new ApplicationError("Error processing screens", {
      originalError: error,
    });
  }
}
/**
 * Entry point: Processes all screens in the routing application and maps their user experiences.
 */
(async () => {
  // Extract provider and model from command line arguments
  const [provider = DEFAULT_AI_PROVIDER, model = DEFAULT_AI_MODEL] =
    process.argv.slice(2);

  // If provider or model is missing, log an error and exit
  if (!provider || !model) {
    console.error(
      "Error: Provider or model missing. Usage: node script.js <provider> <model>"
    );
    process.exit(1);
  }

  // Read routing app data
  const routingApp = await fileHandler.readJson(pathManager.output.routingApp);

  // If the routing app data is invalid, log an error and exit
  if (!routingApp || typeof routingApp !== "object") {
    console.error(
      `Invalid routing application data at "${pathManager.output.routingApp}".`
    );
    process.exit(1);
  }

  await fileHandler.deleteFolder(pathManager.output.screens);
  await fileHandler.deleteFolder(pathManager.output.pages);
  // Process all screens in the routing app
  await processScreens(provider, model, routingApp);
  await extractRequests();
})();

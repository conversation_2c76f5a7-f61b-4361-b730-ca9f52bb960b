import { capitalizeFirstLetter } from "../../utils/stringUtils.js";
import {
  getFilteredOutputFields,
  getWorkerAction,
} from "../../utils/helpers.js";
import {
  KEY_FOR_GENERATION_TASK,
  KEY_FOR_STORE_TASK,
  KEY_FOR_UPSCALE_TASK,
  KEY_FOR_EDITING_TASK,
  KEY_FOR_TRANSCRIBE_TASK,
} from "../../utils/constant.js";

/**
 * Generates the service template for a resource.
 *
 * @param {Object} params - Contains capitalizedResource, isWorkflow, and resourceKey.
 * @returns {string} - The complete service template as a string.
 */
export function generateServiceTemplate({ capitalizedResource, resourceKey }) {
  const lowercaseResource = resourceKey.toLowerCase();

  let serviceContent = `
import { NotFoundException, Injectable } from '@nestjs/common';
import { ${capitalizedResource}, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
import { FileService } from 'src/shared/file/file.service';
import { AiService, DEFAULT_IMAGE_MODEL } from 'src/shared/ai/ai.service';
import path from 'path';
import fs from 'fs/promises';
import { ImagePrompt, VideoPrompt } from 'specs/prompt';
import { extractText } from 'src/bot/extractors';
import { extractJson } from 'src/shared/format/helpers';
import { downloadImage } from 'src/shared/format/images';
import { downloadMedia, getFileMetadata } from 'src/shared/format/media';
import {contentGenerationPrompt} from 'src/prompts/contentGenerationPrompt';

@Injectable()
export class ${capitalizedResource}WorkflowService {
  constructor(
    public prisma: PrismaService,
    public aiService: AiService,
    public fileService: FileService
  ) {}

  async create(data: Prisma.${capitalizedResource}CreateInput): Promise<${capitalizedResource}> {
    return await this.prisma.${resourceKey}.create({
      data
    });
  }

  async get(where: Prisma.${capitalizedResource}WhereUniqueInput): Promise<${capitalizedResource}> {
    return await this.prisma.${resourceKey}.findUnique({ where });
  }

  async delete(where: Prisma.${capitalizedResource}WhereUniqueInput): Promise<any> {
    try {
      return await this.prisma.${resourceKey}.delete({ where });
    } catch (e) {
      return false;
    }
  }

  async update(where: Prisma.${capitalizedResource}WhereUniqueInput, params: Prisma.${capitalizedResource}UpdateInput): Promise<${capitalizedResource}> {
    return await this.prisma.${resourceKey}.update({ data: params, where });
  }

  async findAll(): Promise<${capitalizedResource}[]> {
    return await this.prisma.${resourceKey}.findMany();
  }

  async find({ where, orderBy, skip = 0, take = 10 }: Prisma.${capitalizedResource}FindManyArgs): Promise<${capitalizedResource}[]> {
    return await this.prisma.${resourceKey}.findMany({ where, orderBy, skip, take });
  }

  async count(where: Prisma.${capitalizedResource}WhereInput): Promise<number> {
    return await this.prisma.${resourceKey}.count({ where });
  }
}
`
    .replace(/Exemple/g, capitalizedResource) // Replace all instances of 'Exemple' with 'Post'
    .replace(/exemple/g, lowercaseResource); // Replace all instances of 'exemple' with 'post';;

  // Handle specific Prisma types (e.g., Prisma.ExempleCreateInput -> Prisma.PostCreateInput)
  serviceContent = serviceContent
    .replace(
      new RegExp(`Prisma\\.${capitalizedResource}FindManyArgs`, "g"),
      `Prisma.${capitalizedResource}FindManyArgs`
    )
    .replace(
      new RegExp(`Prisma\\.${capitalizedResource}CreateInput`, "g"),
      `Prisma.${capitalizedResource}CreateInput`
    )
    .replace(
      new RegExp(`Prisma\\.${capitalizedResource}WhereUniqueInput`, "g"),
      `Prisma.${capitalizedResource}WhereUniqueInput`
    )
    .replace(
      new RegExp(`Prisma\\.${capitalizedResource}UpdateInput`, "g"),
      `Prisma.${capitalizedResource}UpdateInput`
    )
    .replace(
      new RegExp(`Prisma\\.${capitalizedResource}WhereInput`, "g"),
      `Prisma.${capitalizedResource}WhereInput`
    );

  return serviceContent;
}

export const getWorkerTaskTemplate = async (
  workerName,
  resource,
  requestConfigs
) => {
  const { aiConfig, mediaConfig, workflowConfig } = requestConfigs;

  const taskName = `${resource}WorkflowService.${workerName}`;
  // Check if this is a multiple process operation
  const resourceCapitalized = capitalizeFirstLetter(resource);
  const workerAction = getWorkerAction(workerName);

  console.log(
    `🔍 Debug: taskName=${taskName}, workflowConfig=`,
    workflowConfig
  );

  const step = workflowConfig?.steps?.[taskName];

  console.log(`🔍 Debug: step=`, step);

  if (!step || !step.model || !step.provider) {
    console.warn(
      `⚠️  Step not found or incomplete for taskName: ${taskName}, using default configuration`
    );
    console.warn(`⚠️  Step details:`, {
      step,
      hasModel: !!step?.model,
      hasProvider: !!step?.provider,
    });
    // Return a default template or skip this step
    return `
      // Default workflow step for ${taskName}
      console.log('Executing ${taskName}');
      return { success: true, message: 'Default step executed' };
    `;
  }

  const tempKey =
    resource === "image" || resource === "video"
      ? "media"
      : resource === "audio"
      ? "audio"
      : "default";

  switch (workerAction) {
    case KEY_FOR_GENERATION_TASK:
      const generateTemplates = {
        media: `
      // Generate optimized prompt
      let prompt = await this.aiService.send({
        user: inputs.user,
        type: "text",
        messages: [
          {
            role: 'user',
            content: ${resourceCapitalized}Prompt({
              context: inputs["prompt"],
              type: "${mediaConfig.contentType}",
            }),
          },
        ],
      });

      prompt = extractText(prompt);

      const response = await this.aiService.run({
        user: inputs.user,
        type: "${resource}",
        model: "${step.model}${
          step.modelVersion ? "/" + step.modelVersion : ""
        }",
        provider: "${step.provider}",
        inputs:{ "${step.modelInput}": prompt,
        ...${JSON.stringify(step.extraParams)}},
      });

      return {input: response${step.modelOutput ? step.modelOutput : ""}};
      `,
        default: `
      let response = await this.aiService.send({
        user: inputs.user,
        type: 'text',
        messages: [
          {
            role: 'user',
            content: contentGenerationPrompt(inputs.input.${aiConfig.key}, "${resource}"),
          },
        ],
      });

      return {input: extractJson(response)};
      `,
      };

      return generateTemplates[tempKey] ?? "";
    case KEY_FOR_TRANSCRIBE_TASK:
    case KEY_FOR_UPSCALE_TASK: {
      const extraParams = JSON.stringify(step.extraParams);
      const modelOutput = step.modelOutput ? step.modelOutput : "";

      return `
            const response = await this.aiService.run({
              user: inputs.user,
              model: "${step.model}${
        step.modelVersion ? "/" + step.modelVersion : ""
      }",
              provider: "${step.provider}",
              inputs: {
                "${step.modelInput}": inputs${step.input},
                ...${extraParams}
              },
            });

            return { input: response${modelOutput} };
          `;
    }

    case KEY_FOR_EDITING_TASK:
      return `
          const response = await this.aiService.run({
            user: inputs.user,
            type: "${resource}",
            model: "${step.model}${
        step.modelVersion ? "/" + step.modelVersion : ""
      }",
            provider: "${step.provider}",
            inputs: {"prompt": inputs.prompt, ${step.modelInput}: inputs${
        step.input
      }}
          });

          return {input: response${step.modelOutput ? step.modelOutput : ""}};
        `;

    case KEY_FOR_STORE_TASK:
      const storeTemplates = {
        media: `
        // Set up temporary storage
        const fileFolder = path.join('/tmp', '${resource}s', taskId);
        await fs.mkdir(fileFolder, { recursive: true });

        const filePath = await downloadMedia(inputs${step.input}, fileFolder);

        // Prepare and upload the image
        const file = getFileMetadata(filePath, '${resource}');

        const fileUploaded = await this.fileService.uploadMedia({
          folder: '${resource}s',
          file,
          width: 800,
          height: 400,
        });

        const output = await this.create({
          topic: inputs?.prompt ?? "",
          userId: inputs.user.id,
          publicId: fileUploaded.json.public_id,
          url: fileUploaded.url,
          thumbnailUrl: fileUploaded.json.eager[0].url,
          json: fileUploaded.json,
          type: '${resource}',
        });

      return {input: output };
    `,
        audio: `
          const output = await this.create({
            userId: inputs.user.id,
            publicId: inputs.fileUploaded?.publicId ?? '',
            url: inputs.input ?? '',
            json: inputs.fileUploaded?.json || {},
            transcription: inputs${step.input},
            workflowId,
            name: inputs${step.input}.substr(0, 20),
            type: '${resource}',
          });

          return {input: output};
    `,
        default: `
        const output = await this.create({
            userId: inputs.user.id,
            workflowId,
            ${aiConfig.key}: inputs.input.${aiConfig.key},
            ...inputs${step.input}
          });

        return {input: output};
    `,
      };
      return storeTemplates[tempKey] ?? "";
    default:
      return "";
  }
};

export const generateWorkerFunctionTemplate = ({
  name,
  functionContent,
  type,
}) => {
  return `\n async ${name}(inputs, taskId, workflowId): Promise<${type}> {
${functionContent}
}\n`;
};

/**
 * Provide all text manipulation, formatting, random string generation, and path-handling utilities in one place.
 */

/* -------------------------------------------------------
   BASIC STRING MANIPULATION
   ------------------------------------------------------- */
/**
 * Capitalizes the first letter of a string.
 *
 * @param {string} str - Input string
 * @returns {string}   - String with the first letter capitalized
 */
export function capitalizeFirstLetter(str) {
  // Check if str is null or undefined
  if (!str) {
    return "";
  }
  // Capitalize the first letter and concatenate with the rest of the string
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Converts the first letter of a string to lowercase.
 *
 * @param {string} str - Input string
 * @returns {string}   - String with the first letter lowercased
 */
export function LowerCaseFirstLetter(str) {
  // Check if str is null or undefined
  if (!str) {
    return "";
  }
  // Lowercase the first letter and concatenate with the rest of the string
  return str.charAt(0).toLowerCase() + str.slice(1);
}
export const getMethodName = (path) => {
  const pathSegments = path
    .replace(":", "")
    .split("/")
    .filter((segment) => segment);

  return pathSegments[2] === "id"
    ? "Read"
    : capitalizeFirstLetter(pathSegments[2]);
};

export const getEntityName = (path) => {
  const pathSegments = path
    .replace(":", "")
    .split("/")
    .filter((segment) => segment);

  return pathSegments[1];
};
/**
 * Generates a request name based on an HTTP method and path.
 * Example: ("POST", "/users/:id") -> "postUsersIdRequest"
 *
 * @param {string} type - The HTTP method, e.g., "GET", "POST", "PATCH", etc.
 * @param {string} path - The request path, e.g., "/users/:id"
 * @returns {string}    - The generated request name
 */
export function generateRequestName(type, path) {
  const isWorkflow = path.includes("workflow");

  const controllerType = isWorkflow ? "WorkflowController" : "Controller";

  if (path.includes("create")) {
    return `${getEntityName(path)}${controllerType}${getMethodName(path)}`;
  }
  return `${isWorkflow ? "workflow" : ""}Controller${getMethodName(path)}`;
}

/* -------------------------------------------------------
   FORMATTING & PATH UTILITIES
   ------------------------------------------------------- */

/**
 * Extracts text between triple backticks (``` ... ```).
 *
 * @param {string} response - A string that may include ``` code blocks ```
 * @returns {string}        - The extracted text or the original string if none found
 */
export const extractTextContent = (response) => {
  // Find the start and end indices of the code blocks
  const start = response.indexOf("```");
  const end = response.lastIndexOf("```");
  // If both indices are found and the end is after the start, extract the text content
  if (start !== -1 && end !== -1 && end > start) {
    return response.substring(start + 3, end).trim();
  }
  // Otherwise, return the original string
  return response;
};

/**
 * Removes all whitespace from a string.
 *
 * @param {string} text
 * @returns {string} - Text with all whitespace removed
 */
export const removeWhiteSpace = (text) => {
  // Replace all whitespace characters with an empty string
  return text.replace(/\s+/g, "");
};

/**
 * Replaces path delimiters from one form to another.
 *
 * @param {string} path              - The original path
 * @param {string} currentDelimiter  - The delimiter to replace
 * @param {string} newDelimiter      - The delimiter to use instead
 * @returns {string}                 - The updated path
 */
export const formatPath = (path, currentDelimiter, newDelimiter) => {
  // Create a regex with the current delimiter and replace all occurrences with the new delimiter
  const regex = new RegExp(currentDelimiter, "g");
  return path.replace(regex, newDelimiter);
};

/**
 * Replaces all occurrences of a search string with a replacement string.
 *
 * @param {string} originalText - The original text
 * @param {string} searchText   - The text or pattern to look for
 * @param {string} replaceTxt   - The replacement text
 * @returns {string}            - The updated text
 */
export function replaceText(originalText, searchText, replaceTxt) {
  // Create a regex with the search text and replace all occurrences with the replacement text
  return originalText.replace(new RegExp(searchText, "g"), replaceTxt);
}

/**
 * Repeats a specific character (currently "s") based on a given value,
 * adjusting by +1 or -1. Example: repeatCharacter(5,"min") -> "ssss"
 *
 * @param {number} value - The base number to repeat
 * @param {"min"|"max"} type - Whether to decrement or increment the repeat count
 * @returns {string}
 */
export function repeatCharacter(value, type) {
  // Adjust the value based on the type
  const adjustedValue = type === "min" ? value - 1 : value + 1;
  // If the adjusted value is less than or equal to 0, return an empty string
  if (adjustedValue <= 0) {
    return "";
  }
  // Repeat the character "s" the adjusted value number of times
  return "s".repeat(adjustedValue);
}

/**
 * Parses a path with dynamic segments (e.g., /users/:id), collecting
 * parameters and optionally inserting placeholders.
 *
 * @param {string} path      - The original path, e.g., "/users/:id"
 * @param {boolean} hasItems - Whether to use item-based placeholder (e.g., ${item.id})
 * @returns {object}         - { path: string, params: string[] }
 */
export function parseDynamicPath(path, hasItems = false) {
  // Initialize the parameters array
  const params = [];
  // Replace the dynamic segments with placeholders and collect the parameters
  const resolvedPath = path.replace(/:([a-zA-Z0-9_]+)/g, (_, param) => {
    params.push(param);
    return hasItems ? `\${item.${hasItems ? "id" : param}}` : param;
  });
  // Return the resolved path and parameters
  return { path: resolvedPath, params };
}

/**
 * Extracts the first non-empty segment from the path.
 *
 * @param {string} pathStr - The request path
 * @returns {string}       - The first segment of the path
 */
export function extractKeyFromPath(pathStr) {
  // If the path string is missing, return an empty string
  if (!pathStr) return "";
  // Split the path string into segments and filter out empty segments
  const segments = pathStr.split("/").filter((segment) => segment.length > 0);

  // Special handling for workflow paths like "/workflow/post/create"
  if (segments[0] === "workflow" && segments.length > 1) {
    return segments[1]; // Return "post" from "/workflow/post/create"
  }

  // For regular paths like "/posts/:id" or "/comment/find", return the first segment
  return segments.length > 0 ? segments[0] : "";
}

/**
 * Extracts a resource name and method name from a path, capitalizing them.
 * e.g. "/users/:id" -> { resource: "Users", method: "Read" }
 *
 * @param {string} path - The path to examine
 * @returns {object}    - { resource: string, method: string }
 */
export const getResourceAndMethod = (path) => {
  // Split the path into segments, filter out empty segments, and capitalize each segment
  const pathSegments = path
    .replace(":", "")
    .split("/")
    .filter((segment) => segment);
  const capitalizedSegments = pathSegments.map(capitalizeFirstLetter);
  // Return the resource and method
  return {
    resource: capitalizedSegments[0],
    method: capitalizedSegments[1] === "Id" ? "Read" : capitalizedSegments[1],
  };
};

/**
 * Formats an event ID by removing hyphens.
 *
 * @param {string} eventId
 * @returns {string}
 */
export const formatEventId = (eventId) => {
  // If the event ID exists, remove the hyphens, otherwise return an empty string
  return eventId ? eventId.replace("-", "") : "";
};

/* -------------------------------------------------------
   RANDOM VALUE GENERATION
   ------------------------------------------------------- */

/**
 * Generates a random string of a given length using
 * uppercase and lowercase letters.
 *
 * @param {number} [length=10] - The length of the generated string
 * @returns {string}           - The random string
 */
export function generateRandomString(length = 10) {
  // Define the characters to use
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  // Initialize the result string
  let result = "";
  // Iterate over the length
  for (let i = 0; i < length; i++) {
    // Add a random character to the result string
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  // Return the result string
  return result;
}

/**
 * Generates a random username, prefixed by "user_".
 *
 * @returns {string} - e.g., "user_AbcDefG"
 */
export function generateRandomName() {
  // Generate a random string and prefix it with "user_"
  return `user_${generateRandomString(7)}`;
}

/**
 * Generates a random 10-digit number (1-9 each digit).
 *
 * @returns {string} - e.g., "8374926152"
 */
export function generateRandomNumber() {
  // Generate a random digit between 1 and 9
  const randomDigits = () => Math.floor(Math.random() * 9) + 1;
  // Create an array of 10 random digits and join them into a string
  return Array.from({ length: 10 }, randomDigits).join("");
}

/**
 * Generates a random email address at "example.com".
 *
 * @returns {string} - e.g., "<EMAIL>"
 */
export function generateRandomEmail() {
  // Generate a random string and append "@example.com"
  return `${generateRandomString(9)}@example.com`;
}

/**
 * Generates a random 10-character password.
 *
 * @returns {string}
 */
export function generateRandomPassword() {
  // Generate a random string of length 10
  return generateRandomString(10);
}

// Helper: converts a string to camelCase.
export function toCamelCase(str) {
  return str
    .toLowerCase() // Convert the entire string to lowercase
    .replace(/[\s-_/]+(.)/g, (_, chr) => chr.toUpperCase()); // Replace spaces and special characters
}

// Helper: derive a function name from the NAV_CONFIG key.
// E.g. '/post/:id_GET' becomes 'postIdGET'
export function deriveFunctionName(key) {
  // Remove leading slash and split by '/'
  const parts = key.replace(/^\//, "").split("/");
  // Remove colon characters and join parts with an underscore.
  const cleaned = parts.map((part) => part.replace(/:/g, ""));
  // Join and convert to camelCase.
  return toCamelCase(cleaned.join("_"));
}

export function generateNotificationKey(
  path,
  type,
  isWorkflow,
  hasDynamicData
) {
  return isWorkflow
    ? `${path}_${type}_workflow${hasDynamicData ? "_params" : ""}`
    : `${path}_${type}${hasDynamicData ? "_params" : ""}`;
}
export function extractDynamicParams(path) {
  const regex = /:([a-zA-Z0-9_]+)/g;
  const params = [];
  let match;
  while ((match = regex.exec(path)) !== null) {
    params.push(match[1]);
  }
  return params;
}

export function addContentToObject(fileContent, newContent) {
  // Find the position of the first "{"
  const firstBraceIndex = fileContent.indexOf("{");

  if (firstBraceIndex === -1) {
    console.error("No object found in the file.");
    return;
  }

  // Remove the outermost brackets of newContent
  const trimmedContent = newContent.trim().replace(/^\{|\}$/g, "");

  // Insert new content after the first "{"
  const updatedContent =
    fileContent.slice(0, firstBraceIndex + 1) +
    "\n  " +
    trimmedContent +
    "," +
    fileContent.slice(firstBraceIndex + 1);

  return updatedContent;
}

/**
 * Converts a path with parameters (e.g., '/chatbot/:id') into a template literal string (e.g., `/chatbot/${data}`)
 *
 * @param {string} path - The path containing parameters (e.g., '/users/:id/posts/:postId')
 * @param {string} [dataVariable='data'] - The variable name to use in the interpolation (default: 'data')
 * @returns {string} - The interpolated path as a template literal
 */
export function interpolatePath(path) {
  if (!path) return null;
  const cleanPath = path.replace(/['"]/g, "");
  const interpolated = cleanPath.replace(/:([a-zA-Z0-9_]+)/g, "");
  return {
    raw: interpolated,
    cooked: interpolated,
    identifier: "data.id",
  };
}

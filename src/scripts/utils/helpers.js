/**
 * - Provide a collection of miscellaneous but important helper functions, including file I/O, request grouping, diff application, etc.
 */

import fs from "fs/promises";
import path from "path";
import { join, resolve } from "path";
import { isLocalEnv } from "../../env/env.utils.js";
import { extractTextContent } from "./stringUtils.js";
import { ApplicationError } from "./CustomError.js";
import {
  KEY_FOR_GENERATION_TASK,
  KEY_FOR_STORE_TASK,
  KEY_FOR_UPSCALE_TASK,
  KEY_FOR_EDITING_TASK,
  KEY_FOR_TRANSCRIBE_TASK,
} from "./constant.js";
import { FileHandler } from "./FileHandler.js";
import { pathManager } from "./pathManager.js";
import { extractKeyFromPath } from "./stringUtils.js";
import { generatePrompt } from "../../../prompts/specs/generatePrompt.js";
// Initialize the file handler for file system operations
const fileHandler = new FileHandler();
/**
 * Persists a prompt's input/response to the local filesystem (if in local env).
 */
export async function storePrompt({ id, key, params, response }) {
  // If the environment is local
  if (isLocalEnv()) {
    try {
      // Construct the base directory
      const baseDir = resolve(join("cache", id, key));
      // Create the base directory
      await fs.mkdir(baseDir, { recursive: true });

      // Construct the parameters JSON file path
      const paramsJsonFile = join(baseDir, "params.json");
      // Write the parameters to the JSON file
      await fs.writeFile(
        paramsJsonFile,
        JSON.stringify(params, null, 2),
        "utf8"
      );

      try {
        // Extract the text content from the response
        response = extractTextContent(response);
      } catch (error) {
        throw new ApplicationError("Error extracting text from response", {
          originalError: error,
        });
      }

      // Construct the parameters response file path
      const paramsResponseFile = join(baseDir, "response.md");
      // Write the response to the file
      await fs.writeFile(paramsResponseFile, response, "utf8");
    } catch (e) {
      console.error(`Cache not saved for: ${id} - ${key}`, e);
    }
  }
}

const getServiceName = (path) => {
  if (path.includes("chat")) {
    return "ChatChatConversationService";
  }
  return "WorkflowService";
};
/**
 * Groups requests by type and path, returning an array of grouped objects.
 */
export function groupRequestsByTypeAndPath(requests) {
  // Initialize the groups map
  const groups = new Map();

  // Iterate over each request
  Object.entries(requests || {}).forEach(([requestId, request]) => {
    // Construct the key
    const key = `${request.type}_${request.path}`;
    // If the key does not exist, create it
    if (!groups.has(key)) {
      groups.set(key, {
        service: getServiceName(request.path),
        type: request.type,
        path: request.path,
        requests: {},
        useWorkflow: null,
      });
    }
    // Set the request in the group
    groups.get(key).requests[requestId] = request;

    // Check if any request in the group has useWorkflow.tasks.length > 0
    if (
      request.useWorkflow &&
      request.useWorkflow.tasks &&
      request.useWorkflow.tasks.length > 0
    ) {
      groups.get(key).useWorkflow = request.useWorkflow;
    }
  });

  // Return the array of groups
  return Array.from(groups.values());
}

/**
 * Applies a diff with ORIGINAL / UPDATED markers to the given code string.
 */
export const applyDiff = (code, diff) => {
  // Define the regex to match the diff
  const regex = /<<<<<<< ORIGINAL\n(.*?)=======\n(.*?)>>>>>>> UPDATED/gs;
  // Initialize the match variable
  let match;

  // Escape the regex special characters
  const escapeRegExp = (string) =>
    string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

  // While there are matches
  while ((match = regex.exec(diff)) !== null) {
    // Get the before and after strings
    const [, before, after] = match;
    // If the before string is empty, append the after string to the code
    if (before.trim() === "") {
      code += `\n${after}`;
    } else {
      // Otherwise, replace the before string with the after string
      let replacementRegex = escapeRegExp(before);
      replacementRegex = replacementRegex.replaceAll(/\r?\n/g, "\\s+");
      replacementRegex = replacementRegex.replaceAll(/\t/g, "");
      const replaceRegex = new RegExp(replacementRegex);
      code = code.replace(replaceRegex, after);
    }
  }
  // Return the code
  return code;
};

/**
 * Extracts a single named model from a schema string.
 */
export function extractModel(schema, modelName) {
  // Construct the regex to match the model
  const regex = new RegExp(`model\\s+${modelName}\\s*{`);
  // Execute the regex
  const match = regex.exec(schema);
  // If there is no match, return null
  if (!match) return null;

  // Get the start index
  const start = match.index;
  // Get the first brace index
  const firstBrace = schema.indexOf("{", start);
  // If there is no first brace, return null
  if (firstBrace === -1) return null;

  // Initialize the count and i variables
  let count = 1;
  let i = firstBrace + 1;
  // Iterate over the schema
  for (; i < schema.length; i++) {
    // If the character is an open brace, increment the count
    if (schema[i] === "{") count++;
    // Otherwise, if the character is a close brace, decrement the count
    else if (schema[i] === "}") count--;
    // If the count is 0, break
    if (count === 0) break;
  }
  // Return the extracted model
  return schema.slice(start, i + 1);
}

/**
 * Extracts multiple models from a schema if they exist.
 */
export function extractModels(schema, models) {
  // Define the regex to match the model
  const modelRegex = /model (\w+) \{[^\}]*\}/g;
  // Initialize the extracted models object
  const extractedModels = {};

  // While there are matches
  let match;
  while ((match = modelRegex.exec(schema)) !== null) {
    // Get the full match and model name
    const [fullMatch, modelName] = match;
    // If the model name is in the models array, add it to the extracted models object
    if (models.includes(modelName.toLowerCase())) {
      extractedModels[modelName] = fullMatch;
    }
  }
  // Return the extracted models object
  return extractedModels;
}

/**
 * Determines the high-level operation type (create, read, update, delete, etc.)
 * based on the request object.
 */
export const getOperationType = (req) => {
  // Get the parameters, body, data result, and type from the request
  const { params = {}, body = {}, dataResult, type, path } = req;

  const hasEntityToEnhance = shouldEnhanceEntity(req);

  // Determine if the request has an ID
  const hasId = "id" in params;
  // Determine if the request has a body
  const hasBody = body && Object.keys(body).length > 0;
  // Determine if the request has a data ID
  const hasDataId = "id" in dataResult || "userId" in dataResult;

  // If the type is POST, does not have an ID, has a body, and has a data ID, return create
  if (
    type === "POST" &&
    ((path.includes("create") && hasBody) || hasEntityToEnhance)
  )
    return "create";
  // If the type is DELETE, has an ID, and does not have a body, return delete
  if (type === "DELETE") return "delete";
  // If the type is GET, has an ID, does not have a body, and has a data ID, return read
  if (type === "GET" && hasId && !hasBody && hasDataId) return "read";
  // If the type is POST, does not have an ID, has a body, or does not have a body and does not have parameters, return find
  if (
    type === "POST" &&
    ((!hasId && Object.keys(body).length > 0) ||
      (Object.keys(body).length === 0 && Object.keys(params).length === 0))
  ) {
    return "find";
  }
  // If the type is PATCH, has an ID, and has a body, return update
  if (type === "PATCH" && hasId && hasBody) return "update";
  // Otherwise, return null

  return null;
};

/**
 * @function isDuplicateRequest
 * @description Checks if a request is already in the request array to avoid duplicates.
 * @param {Array} requestArray - Existing array of requests for a resource.
 * @param {Object} newRequest - Candidate request to check.
 * @returns {Boolean} - True if a duplicate is found, otherwise false.
 */
function isDuplicateRequest(requestArray, newRequest) {
  return requestArray.some((existingRequest) => {
    return (
      existingRequest.type === newRequest.type &&
      existingRequest.path === newRequest.path &&
      JSON.stringify(existingRequest.params) ===
        JSON.stringify(newRequest.params)
    );
  });
}

/**
 * @function transformScreenSpecs
 * @description Merges the requests from a single spec file into the global request list.
 * @param {Object} spec - The spec data from a single screen or modal file.
 * @param {Object} requestList - The global request list keyed by resource.
 */
export function transformScreenSpecs(spec, requestList) {
  // If the spec or requests are missing, return early
  if (!spec || !spec.requests) return;
  // Extract requests from the spec
  const requests = spec.requests;

  // Iterate over each request ID
  for (const requestId in requests) {
    // Get the request object
    const request = requests[requestId];
    // Extract the path string from the request, defaulting to an empty string if not present
    const pathStr = request.path || "";
    // Extract the resource key from the path string
    const resource = extractKeyFromPath(pathStr);

    // If the resource is missing, log a warning and skip to the next request
    if (!resource) {
      console.warn(`Skipping request with empty resource for path: ${pathStr}`);
      continue;
    }
    // If the resource is not yet in the request list, initialize it with an empty array
    if (!requestList[resource]) {
      requestList[resource] = [];
    }
    // Construct the request data object
    const requestData = {
      type: request.type || null,
      path: request.path || null,
      useAi: request.useAi,
      useWorkflow: request.useWorkflow || {},
      params: request.params || {},
      body: request.body || {},
      dataResult: request.dataResult || {},
      notifyLink: request.notifyLink,
    };
    // Add the request data to the request list if it's not a duplicate
    if (!isDuplicateRequest(requestList[resource], requestData)) {
      requestList[resource].push(requestData);
    }
  }
}

/**
 * @async
 * @function extractRequests
 * @description Reads all screen and modal spec files, extracts their requests, and writes a merged `requests.json`.
 * @returns {Object} The fully merged `requests` object keyed by resource.
 */
export async function extractRequests() {
  try {
    // Read files from the pages output directory
    const pagesFiles = await fileHandler.readFiles(pathManager.output.pages);
    // Read files from the modals output directory
    const modalFiles = await fileHandler.readFiles(pathManager.output.modals);
    // Combine the file lists from both directories
    const files = [...pagesFiles, ...modalFiles];
    // Initialize an empty object to hold the merged request list
    let requestList = {};

    // Loop through each file in the combined list
    for (const file of files) {
      // Create the full file path by joining the output pages directory with the file name
      const filePath = path.join(pathManager.output.pages, file);

      try {
        // Read the JSON data from the file
        const spec = await fileHandler.readJson(filePath);

        // Skip files with null or invalid content
        if (!spec || !spec.screenSpecs) {
          console.warn(
            `⚠️  Skipping invalid file: ${file} (null or missing screenSpecs)`
          );
          continue;
        }

        // Transform the screen specifications to extract requests and merge them into the request list
        transformScreenSpecs(spec.screenSpecs, requestList);
      } catch (fileError) {
        console.warn(
          `⚠️  Skipping corrupted file: ${file} - ${fileError.message}`
        );
        continue;
      }
    }
    // Write the merged requests to a JSON file
    await fileHandler.writeJson(`${pathManager.output.requests}`, requestList);
    // Return the merged request list
    return requestList;
  } catch (error) {
    // Log an error message if extracting requests fails
    console.error("Failed to extract requests:", error);
    // Throw a custom application error with details of the original error
    throw new ApplicationError("Error extracting requests", {
      originalError: error,
    });
  }
}

export async function updateRequestsWithSteps() {
  const requests = await fileHandler.readJson(pathManager.output.requests);

  const modelList = await fileHandler.readFile(
    pathManager.back.shared.ai.model
  );

  for (const key in requests) {
    const requestArray = requests[key];
    for (const request of requestArray) {
      if (request?.useWorkflow && request.useWorkflow?.tasks?.length > 0) {
        // Generate the navigation specifications
        const tasks = request.useWorkflow.tasks;
        const prompt = await promptToGenerateStepsData(tasks, modelList);
        const response = await sendPromptToAi(provider, model, prompt, "steps");
        request.steps = response;
      }
    }
  }

  await fileHandler.writeJson(pathManager.output.requests, requests);
}

// Helper: extract dynamic parameters from a given link string
export function getFunctionParams(link) {
  const params = [];
  const regex = /:([a-zA-Z0-9_]+)/g;
  let match;
  while ((match = regex.exec(link)) !== null) {
    params.push(match[1]);
  }
  return params.join(", ");
}

export function getGenerationMode(types) {
  const sorted = [...types].sort().join("+");

  switch (sorted) {
    case "text":
      return "TEXT_ONLY";
    case "image":
      return "IMAGE_ONLY";
    case "video":
      return "VIDEO_ONLY";
    case "audio":
      return "AUDIO_ONLY";
    case "image+text":
      return "TEXT_IMAGE";
    case "text+video":
      return "TEXT_VIDEO";
    case "audio+text":
      return "TEXT_AUDIO";
    default:
      return "UNKNOWN";
  }
}

export function extractAiConfigFromRequest(request, resourceKey, fileType) {
  const workflowConfig = {};

  if (request?.useWorkflow?.tasks?.length > 0) {
    workflowConfig.tasks = request.useWorkflow.tasks;
    if (request.steps) {
      workflowConfig.steps = request.steps;
    }
    if (request?.accessCredit?.consumes) {
      workflowConfig.accessCredit = request.accessCredit;
    }
  }
  if (
    !request.useAi ||
    typeof request.useAi !== "object" ||
    Object.keys(request.useAi).length === 0
  ) {
    return {
      useAi: false,
      aiConfig: { useAi: false },
      imageConfig: {},
      mediaConfig: {},
      workflowConfig: {},
      generativeMode: "TEXT_WITHOUT_AI",
    };
  }

  const aiRequest = request.useAi;
  const generationTypes = aiRequest.generationType ?? [];
  const aiOutput = aiRequest.output ?? [];
  const bodyKey = request.body ? Object.keys(request.body)[0] : null;
  const operation = request.operation || "create"; // fallback if undefined
  const generativeMode = getGenerationMode(generationTypes);
  const requestBody = fileType === "service" ? resourceKey : "params";

  const aiConfig = { useAi: true, key: bodyKey };

  const mediaConfig = {};

  // Handle text generation
  if (
    generationTypes.includes("text") &&
    aiRequest.role &&
    aiRequest.objective
  ) {
    const { role, objective } = aiRequest;

    const outputFormat = aiOutput.reduce((acc, item) => {
      const key = Object.keys(item)[0];
      if (key === "price") {
        acc[
          key
        ] = `[Price to buy a ${resourceKey} (must be a number, minimum price: 1)]`;
      } else {
        acc[key] = `[${resourceKey}'s ${key} text]`;
      }
      return acc;
    }, {});

    aiConfig.prompt = generatePrompt(
      JSON.stringify(outputFormat, null, 2),
      operation,
      role,
      bodyKey ? objective.replace(bodyKey, `[${bodyKey}]`) : objective
    );
    aiConfig.params = request.body ?? {};
    aiConfig.key = bodyKey;
    aiConfig.output = aiOutput.map((item) => Object.keys(item)[0]);
  }

  if (
    generationTypes.includes("video") ||
    generationTypes.includes("image") ||
    generationTypes.includes("audio")
  ) {
    if (generationTypes.includes("video")) {
      mediaConfig.type = "video";
      mediaConfig.useVideo = true;
      mediaConfig.modelKey =
        aiOutput.find((item) => item.hasOwnProperty("video"))?.url ?? "url";
      mediaConfig.contentType = "cinematicNarrative";
    } else if (generationTypes.includes("audio")) {
      mediaConfig.type = "audio";
      mediaConfig.useAudio = true;
      mediaConfig.modelKey =
        aiOutput.find((item) => item.hasOwnProperty("audio"))?.url ?? "url";
    } else {
      mediaConfig.type = "image";
      mediaConfig.useImage = true;
      mediaConfig.modelKey =
        aiOutput.find((item) => item.hasOwnProperty("image"))?.image ?? "image";
      mediaConfig.contentType = "photography";
    }

    if (aiRequest?.context) {
      mediaConfig.resourceParams = aiRequest.context
        .map((field) => `${requestBody}.${field}`)
        .join(" + ");
      mediaConfig.fields = aiRequest.context
        .map((field) => `${field}`)
        .join(" , ");
    }
  }

  return {
    useAi: true,
    aiConfig,
    mediaConfig,
    generativeMode,
    workflowConfig,
  };
}

/**
 * Helper function to get output fields excluding image model key
 */
export function getFilteredOutputFields(aiConfig, imageConfig) {
  if (!aiConfig?.output || !Array.isArray(aiConfig.output)) {
    return "";
  }

  return aiConfig.output
    .filter((field) => field !== imageConfig.modelKey)
    .join(", ");
}

export function shouldEnhanceEntity(workflow) {
  if (workflow?.steps && Object.keys(workflow.steps)) {
    return (
      !!Object.keys(workflow.steps).join("").includes(KEY_FOR_UPSCALE_TASK) ||
      !!Object.keys(workflow.steps).join("").includes(KEY_FOR_EDITING_TASK)
    );
  }
  if (workflow?.useWorkflow && Object.keys(workflow.useWorkflow)) {
    return (
      !!JSON.stringify(workflow.useWorkflow).includes(KEY_FOR_UPSCALE_TASK) ||
      !!JSON.stringify(workflow.useWorkflow).includes(KEY_FOR_EDITING_TASK)
    );
  }
  return false;
}

export function getWorkflowType(steps) {
  if (!steps) {
    console.warn(
      "⚠️  Steps is undefined in getWorkflowType, returning default"
    );
    return "default";
  }

  const stepsToString = JSON.stringify(steps);
  const containsGenerationStep = stepsToString.includes(
    KEY_FOR_GENERATION_TASK
  );
  const containsUpscalingStep = stepsToString.includes(KEY_FOR_UPSCALE_TASK);
  const containsStoreStep = stepsToString.includes(KEY_FOR_STORE_TASK);
  const containsEditingStep = stepsToString.includes(KEY_FOR_EDITING_TASK);
  const containsTranscribeStep = stepsToString.includes(
    KEY_FOR_TRANSCRIBE_TASK
  );
  // Build the workflow type string based on flags
  let workflowType = [];

  if (containsGenerationStep) {
    workflowType.push(KEY_FOR_GENERATION_TASK);
  }
  if (containsUpscalingStep) {
    workflowType.push(KEY_FOR_UPSCALE_TASK);
  }
  if (containsEditingStep) {
    workflowType.push(KEY_FOR_EDITING_TASK);
  }
  if (containsTranscribeStep) {
    workflowType.push(KEY_FOR_TRANSCRIBE_TASK);
  }
  if (containsStoreStep) {
    workflowType.push(KEY_FOR_STORE_TASK);
  }

  // If we have any workflow steps, return the concatenated type, else return false
  if (workflowType.length > 0) {
    return workflowType.join("+");
  }

  return false;
}

export function getWorkerAction(taskName) {
  switch (true) {
    case taskName.includes(KEY_FOR_GENERATION_TASK):
      return KEY_FOR_GENERATION_TASK;
    case taskName.includes(KEY_FOR_UPSCALE_TASK):
      return KEY_FOR_UPSCALE_TASK;
    case taskName.includes(KEY_FOR_STORE_TASK):
      return KEY_FOR_STORE_TASK;
    case taskName.includes(KEY_FOR_EDITING_TASK):
      return KEY_FOR_EDITING_TASK;
    case taskName.includes(KEY_FOR_TRANSCRIBE_TASK):
      return KEY_FOR_TRANSCRIBE_TASK;
    default:
      return "default";
  }
}

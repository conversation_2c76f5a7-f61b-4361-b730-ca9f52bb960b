// Define the path manager
export const pathManager = {
  // Project paths
  project: {
    root: "src",
    front: "code/front-starter/src",
    back: "code/api-starter/src",
  },
  // Script paths
  scripts: (() => {
    const base = "src/scripts";
    return {
      root: base,
      project: `${base}/projectSpecs/projectSpecs.js`,
      feature: `${base}/featureSpecs/featureSpecs.js`,
      screen: `${base}/screenSpecs/screenSpecs.js`,
      step: `${base}/stepsSpecs/stepsSpecs.js`,
      navigation: `${base}/navigationSpecs/navigationSpecs.js`,
      form: `${base}/formSpecs/formSpecs.js`,
      home: `${base}/homeRoutesSpecs/homeRoutesSpecs.js`,
      notification: `${base}/notificationTemplates/notificationTemplates.js`,
      generateServer: `${base}/serverCode/generateServer.js`,
      generateScreens: `${base}/clientCode/generateScreens.js`,
    };
  })(),
  // Output paths
  output: (() => {
    const base = "specs";
    return {
      root: base,
      features: `${base}/features`,
      screens: `${base}/screens`,
      pages: `${base}/pages`,
      modals: `${base}/modals`,
      forms: `${base}/forms`,
      tests: `${base}/tests`,
      routingApp: `${base}/routingApp.json`,
      specificationsApp: `${base}/specifications.json`,
      models: `${base}/models.json`,
      navigation: `${base}/navigation.json`,
      requests: `${base}/requests.json`,
      homeRoutes: `${base}/homeRoutes.json`,
      workflow: `${base}/workflow.json`,
    };
  })(),

  // Front-end paths
  front: (() => {
    const base = "code/front-starter/src";
    return {
      root: base,
      mock: `${base}/mock`,
      forms: `${base}/forms`,
      locales: `${base}/locales`,
      test: `${base}/__tests__`,
      pages: `${base}/pages`,
      modals: `${base}/modals`,
      configs: {
        routerApp: `${base}/configs/RouterConfig.tsx`,
        plan: `${base}/configs/PlanConfig.ts`,
        nav: `${base}/configs/NavConfig.ts`,
        home: `${base}/configs/HomeConfig.ts`,
      },
      layouts: {
        router: `${base}/layouts/RouterLayout.tsx`,
      },
      mockApi: `${base}/mock/api.tsx`,
      loginPage: `${base}/pages/auth/login.page.tsx`,
    };
  })(),

  // Back-end paths
  back: (() => {
    const base = "code/api-starter";
    const src = `${base}/src`;
    const modules = `${src}/modules`;

    return {
      prismaSchema: `${base}/prisma/schema.prisma`,
      story: `${base}/specs/story`,
      seed: {
        root: `${base}/specs/seed`,
        index: `${base}/specs/seed/index.ts`,
      },
      acl: `${src}/acl/acl.config.json`,
      modules: {
        root: modules,
        seed: {
          root: `${modules}/seed`,
          module: `${modules}/seed/seed.module.ts`,
          service: `${modules}/seed/seed.service.ts`,
        },
        workflow: {
          root: `${modules}/workflow`,
          workerService: `${modules}/workflow/worker.service.ts`,
          module: `${modules}/workflow/workflow.module.ts`,
        },
      },
      shared: {
        ai: {
          model: `${src}/shared/ai/ai.model.ts`,
        },
        config: {
          paymentOptions: `${src}/shared/config/plan.config.ts`,
          notifications: `${src}/shared/config/notification.config.ts`,
        },
        notification: {
          notifications: `${src}/shared/notification/notifications.ts`,
        },
      },
    };
  })(),

  // Code mod paths
  codemod: (() => {
    const base = "codemod";
    return {
      root: base,
      addNav: `${base}/addOnClickNavigate.mjs`,
      testPage: `${base}/page.test.mjs`,
    };
  })(),
};
